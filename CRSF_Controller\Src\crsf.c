/**
 * @file crsf.c
 * @brief CRSF协议主要实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "crsf.h"
#include "hal_drivers.h"

/* 全局变量定义 */
uint16_t rcChannels[CRSF_MAX_CHANNELS];
crsf_device_t crsf_devices[CRSF_MAX_DEVICES];
elrs_info_t elrs_info;
elrs_info_t local_info;
module_type_t module_type = MODULE_UNKNOWN;
crsf_link_statistics_t LinkStatistics;

uint8_t rxConnected = 0;
uint8_t txConnected = 0;
uint32_t crsfTime = 0;
uint32_t lastCrsfTime = 0;
uint32_t updateInterval = CRSF_TIME_BETWEEN_FRAMES_US;

/* CRSF接收状态 */
volatile uint8_t crsf_rx_buffer[CRSF_RX_BUFFER_SIZE];
volatile uint16_t crsf_rx_head = 0;
volatile uint16_t crsf_rx_tail = 0;
volatile bool crsf_frame_available = false;

/* CRSF发送缓冲区 */
uint8_t crsf_tx_buffer[CRSF_TX_BUFFER_SIZE];

/* 私有变量 */
static crsf_state_t crsf_state = CRSF_STATE_IDLE;
static uint8_t crsf_frame_buffer[CRSF_FRAME_SIZE_MAX];
static uint8_t crsf_frame_length = 0;
static uint8_t crsf_frame_pos = 0;
static uint8_t crsf_expected_length = 0;

/* 私有函数声明 */
static void CRSF_ProcessByte(uint8_t byte);
static error_code_t CRSF_ValidateFrame(uint8_t* frame, uint8_t length);
static void CRSF_ResetReceiver(void);

/**
 * @brief CRSF初始化
 */
error_code_t CRSF_Init(void)
{
    /* 初始化通道值 */
    for (uint8_t i = 0; i < CRSF_MAX_CHANNELS; i++) {
        rcChannels[i] = CRSF_CHANNEL_VALUE_MID;
    }

    /* 清空设备信息 */
    memset(crsf_devices, 0, sizeof(crsf_devices));
    memset(&elrs_info, 0, sizeof(elrs_info));
    memset(&local_info, 0, sizeof(local_info));
    memset(&LinkStatistics, 0, sizeof(LinkStatistics));

    /* 重置接收器状态 */
    CRSF_ResetReceiver();

    /* 启动UART DMA接收 */
    if (HAL_UART_Receive_DMA_Safe(&huart1, (uint8_t*)crsf_rx_buffer, CRSF_RX_BUFFER_SIZE) != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    return ERR_OK;
}

/**
 * @brief 启动CRSF
 */
error_code_t CRSF_Start(void)
{
    crsfTime = micros();
    lastCrsfTime = crsfTime;
    
    return ERR_OK;
}

/**
 * @brief 停止CRSF
 */
error_code_t CRSF_Stop(void)
{
    HAL_UART_DMAStop(&huart1);
    return ERR_OK;
}

/**
 * @brief 设置CRSF更新频率
 */
void CRSF_SetUpdateRate(uint32_t rate_hz)
{
    if (rate_hz > 0) {
        updateInterval = 1000000 / rate_hz;
        updateInterval = CONSTRAIN(updateInterval, CRSF_FRAME_PERIOD_MIN, CRSF_FRAME_PERIOD_MAX);
    }
}

/**
 * @brief 发送通道数据
 */
error_code_t CRSF_SendChannels(const rc_input_t* rc_input)
{
    uint8_t frame[CRSF_PACKET_SIZE];

    /* 转换ADC值到CRSF通道值 */
    rcChannels[0] = CRSF_ConvertChannel(rc_input->ch1);       // 通道1
    rcChannels[1] = CRSF_ConvertChannel(rc_input->ch2);       // 通道2
    rcChannels[2] = CRSF_ConvertChannel(rc_input->ch3);       // 通道3
    rcChannels[3] = CRSF_ConvertChannel(rc_input->ch4);       // 通道4
    rcChannels[4] = CRSF_ConvertChannel(rc_input->swa);       // 开关A
    rcChannels[5] = CRSF_ConvertChannel(rc_input->swb);       // 开关B
    rcChannels[6] = CRSF_ConvertChannel(rc_input->vra);       // 电位器A
    rcChannels[7] = CRSF_ConvertChannel(rc_input->vrb);       // 电位器B
    
    /* 构建CRSF帧 */
    frame[0] = ADDR_MODULE;                    // 目标地址
    frame[1] = 24;                             // 帧长度
    frame[2] = CRSF_FRAMETYPE_RC_CHANNELS_PACKED; // 帧类型
    
    /* 打包通道数据 */
    CRSF_PackChannels(rcChannels, &frame[3]);
    
    /* 计算CRC */
    frame[25] = crsf_crc8(&frame[2], 23);
    
    /* 发送帧 */
    return CRSF_SendFrame(frame, CRSF_PACKET_SIZE);
}

/**
 * @brief 发送CRSF帧
 */
error_code_t CRSF_SendFrame(uint8_t* frame, uint8_t length)
{
    if (length > CRSF_TX_BUFFER_SIZE) {
        return ERR_INVALID_PARAM;
    }
    
    /* 复制到发送缓冲区 */
    memcpy(crsf_tx_buffer, frame, length);
    
    /* 通过UART DMA发送 */
    return HAL_UART_Transmit_DMA_Safe(&huart1, crsf_tx_buffer, length);
}

/**
 * @brief 广播Ping
 */
error_code_t CRSF_BroadcastPing(void)
{
    uint8_t frame[6];
    
    frame[0] = ADDR_MODULE;
    frame[1] = 4;
    frame[2] = CRSF_FRAMETYPE_DEVICE_PING;
    frame[3] = CRSF_ADDRESS_BROADCAST;
    frame[4] = ADDR_RADIO;
    frame[5] = crsf_crc8(&frame[2], 3);
    
    return CRSF_SendFrame(frame, 6);
}

/**
 * @brief 获取ELRS信息
 */
error_code_t CRSF_GetElrsInfo(uint8_t target)
{
    uint8_t frame[8];
    
    frame[0] = ELRS_ADDRESS;
    frame[1] = 6;
    frame[2] = CRSF_FRAMETYPE_PARAMETER_WRITE;
    frame[3] = target;
    frame[4] = ADDR_RADIO;
    frame[5] = 0;
    frame[6] = 0;
    frame[7] = crsf_crc8(&frame[2], 5);
    
    return CRSF_SendFrame(frame, 8);
}

/**
 * @brief 读取参数
 */
error_code_t CRSF_ReadParam(uint8_t param_num, uint8_t chunk_num, uint8_t target)
{
    uint8_t frame[8];
    
    frame[0] = ELRS_ADDRESS;
    frame[1] = 6;
    frame[2] = CRSF_FRAMETYPE_PARAMETER_READ;
    frame[3] = target;
    frame[4] = ADDR_RADIO;
    frame[5] = param_num;
    frame[6] = chunk_num;
    frame[7] = crsf_crc8(&frame[2], 5);
    
    return CRSF_SendFrame(frame, 8);
}

/**
 * @brief 写入参数
 */
error_code_t CRSF_WriteParam(uint8_t param_num, uint8_t chunk_num, uint8_t target)
{
    uint8_t frame[8];
    
    frame[0] = ADDR_MODULE;
    frame[1] = 6;
    frame[2] = CRSF_FRAMETYPE_PARAMETER_WRITE;
    frame[3] = ELRS_ADDRESS;
    frame[4] = ADDR_RADIO;
    frame[5] = param_num;
    frame[6] = chunk_num;
    frame[7] = crsf_crc8(&frame[2], 5);
    
    return CRSF_SendFrame(frame, 8);
}

/**
 * @brief 发送模型ID
 */
error_code_t CRSF_SendModelId(uint8_t model_id)
{
    uint8_t frame[10];
    
    frame[0] = ELRS_ADDRESS;
    frame[1] = 8;
    frame[2] = CRSF_FRAMETYPE_COMMAND;
    frame[3] = ELRS_ADDRESS;
    frame[4] = ADDR_RADIO;
    frame[5] = CRSF_COMMAND_SUBCMD;
    frame[6] = CRSF_COMMAND_MODEL_SELECT_ID;
    frame[7] = model_id;
    frame[8] = crsf_crc8_ba(&frame[2], 6);
    frame[9] = crsf_crc8(&frame[2], 7);
    
    return CRSF_SendFrame(frame, 10);
}

/**
 * @brief 转换ADC值到CRSF通道值
 */
uint16_t CRSF_ConvertChannel(uint16_t adc_value)
{
    return MAP(adc_value, 0, ADC_RESOLUTION-1, CRSF_CHANNEL_VALUE_MIN, CRSF_CHANNEL_VALUE_MAX);
}

/**
 * @brief 打包通道数据
 */
void CRSF_PackChannels(const uint16_t* channels, uint8_t* packed_data)
{
    /* 将16个11位通道值打包到22字节中 */
    packed_data[0] = (uint8_t)(channels[0] & 0x07FF);
    packed_data[1] = (uint8_t)((channels[0] & 0x07FF) >> 8 | (channels[1] & 0x07FF) << 3);
    packed_data[2] = (uint8_t)((channels[1] & 0x07FF) >> 5 | (channels[2] & 0x07FF) << 6);
    packed_data[3] = (uint8_t)((channels[2] & 0x07FF) >> 2);
    packed_data[4] = (uint8_t)((channels[2] & 0x07FF) >> 10 | (channels[3] & 0x07FF) << 1);
    packed_data[5] = (uint8_t)((channels[3] & 0x07FF) >> 7 | (channels[4] & 0x07FF) << 4);
    packed_data[6] = (uint8_t)((channels[4] & 0x07FF) >> 4 | (channels[5] & 0x07FF) << 7);
    packed_data[7] = (uint8_t)((channels[5] & 0x07FF) >> 1);
    packed_data[8] = (uint8_t)((channels[5] & 0x07FF) >> 9 | (channels[6] & 0x07FF) << 2);
    packed_data[9] = (uint8_t)((channels[6] & 0x07FF) >> 6 | (channels[7] & 0x07FF) << 5);
    packed_data[10] = (uint8_t)((channels[7] & 0x07FF) >> 3);
    packed_data[11] = (uint8_t)((channels[8] & 0x07FF));
    packed_data[12] = (uint8_t)((channels[8] & 0x07FF) >> 8 | (channels[9] & 0x07FF) << 3);
    packed_data[13] = (uint8_t)((channels[9] & 0x07FF) >> 5 | (channels[10] & 0x07FF) << 6);
    packed_data[14] = (uint8_t)((channels[10] & 0x07FF) >> 2);
    packed_data[15] = (uint8_t)((channels[10] & 0x07FF) >> 10 | (channels[11] & 0x07FF) << 1);
    packed_data[16] = (uint8_t)((channels[11] & 0x07FF) >> 7 | (channels[12] & 0x07FF) << 4);
    packed_data[17] = (uint8_t)((channels[12] & 0x07FF) >> 4 | (channels[13] & 0x07FF) << 7);
    packed_data[18] = (uint8_t)((channels[13] & 0x07FF) >> 1);
    packed_data[19] = (uint8_t)((channels[13] & 0x07FF) >> 9 | (channels[14] & 0x07FF) << 2);
    packed_data[20] = (uint8_t)((channels[14] & 0x07FF) >> 6 | (channels[15] & 0x07FF) << 5);
    packed_data[21] = (uint8_t)((channels[15] & 0x07FF) >> 3);
}

/**
 * @brief 重置接收器状态
 */
static void CRSF_ResetReceiver(void)
{
    crsf_state = CRSF_STATE_IDLE;
    crsf_frame_length = 0;
    crsf_frame_pos = 0;
    crsf_expected_length = 0;
    crsf_frame_available = false;
}

/**
 * @brief 处理接收数据
 */
void CRSF_ProcessRxData(void)
{
    /* 计算可用数据长度 */
    uint16_t dma_pos = CRSF_RX_BUFFER_SIZE - __HAL_DMA_GET_COUNTER(huart1.hdmarx);

    while (crsf_rx_tail != dma_pos) {
        uint8_t byte = crsf_rx_buffer[crsf_rx_tail];
        crsf_rx_tail = (crsf_rx_tail + 1) % CRSF_RX_BUFFER_SIZE;

        CRSF_ProcessByte(byte);
    }
}

/**
 * @brief 处理单个字节
 */
static void CRSF_ProcessByte(uint8_t byte)
{
    switch (crsf_state) {
        case CRSF_STATE_IDLE:
            if (byte == CRSF_ADDRESS_RADIO_TRANSMITTER) {
                crsf_frame_buffer[0] = byte;
                crsf_frame_pos = 1;
                crsf_state = CRSF_STATE_LENGTH;
            }
            break;

        case CRSF_STATE_LENGTH:
            if (byte > 2 && byte <= CRSF_PAYLOAD_SIZE_MAX + 2) {
                crsf_frame_buffer[1] = byte;
                crsf_expected_length = byte + 2; // +2 for address and length
                crsf_frame_pos = 2;
                crsf_state = CRSF_STATE_DATA;
            } else {
                CRSF_ResetReceiver();
            }
            break;

        case CRSF_STATE_DATA:
            crsf_frame_buffer[crsf_frame_pos++] = byte;
            if (crsf_frame_pos >= crsf_expected_length) {
                /* 验证CRC */
                if (CRSF_ValidateFrame(crsf_frame_buffer, crsf_expected_length) == ERR_OK) {
                    /* 解析帧 */
                    CRSF_ParseFrame(&crsf_frame_buffer[2], crsf_expected_length - 3);
                    txConnected++;
                }
                CRSF_ResetReceiver();
            }
            break;

        default:
            CRSF_ResetReceiver();
            break;
    }
}

/**
 * @brief 验证帧CRC
 */
static error_code_t CRSF_ValidateFrame(uint8_t* frame, uint8_t length)
{
    if (length < 4) {
        return ERR_INVALID_PARAM;
    }

    uint8_t calculated_crc = crsf_crc8(&frame[2], length - 3);
    uint8_t received_crc = frame[length - 1];

    return (calculated_crc == received_crc) ? ERR_OK : ERR_INVALID_PARAM;
}

/**
 * @brief 解析CRSF帧
 */
void CRSF_ParseFrame(uint8_t* frame, uint8_t length)
{
    if (length < 1) {
        return;
    }

    uint8_t frame_type = frame[0];

    switch (frame_type) {
        case CRSF_FRAMETYPE_DEVICE_INFO:
            CRSF_AddDevice(frame);
            break;

        case CRSF_FRAMETYPE_PARAMETER_SETTINGS_ENTRY:
            CRSF_AddParam(frame, length);
            break;

        case CRSF_FRAMETYPE_ELRS_STATUS:
            CRSF_ParseElrsInfo(frame);
            break;

        case CRSF_FRAMETYPE_LINK_STATISTICS:
            if (length >= sizeof(crsf_link_statistics_t)) {
                memcpy(&LinkStatistics, &frame[1], sizeof(crsf_link_statistics_t));
                CRSF_OnLinkStatistics(&LinkStatistics);
            }
            break;

        case CRSF_FRAMETYPE_BATTERY_SENSOR:
            if (length >= sizeof(crsf_battery_sensor_t)) {
                crsf_battery_sensor_t battery;
                memcpy(&battery, &frame[1], sizeof(crsf_battery_sensor_t));
                CRSF_OnBatteryInfo(&battery);
            }
            break;

        case CRSF_FRAMETYPE_GPS:
            if (length >= sizeof(crsf_gps_t)) {
                crsf_gps_t gps;
                memcpy(&gps, &frame[1], sizeof(crsf_gps_t));
                CRSF_OnGpsInfo(&gps);
            }
            break;

        case CRSF_FRAMETYPE_ATTITUDE:
            if (length >= sizeof(crsf_attitude_t)) {
                crsf_attitude_t attitude;
                memcpy(&attitude, &frame[1], sizeof(crsf_attitude_t));
                CRSF_OnAttitude(&attitude);
            }
            break;

        case CRSF_FRAMETYPE_FLIGHT_MODE:
            if (length >= sizeof(crsf_flight_mode_t)) {
                crsf_flight_mode_t flight_mode;
                memcpy(&flight_mode, &frame[1], sizeof(crsf_flight_mode_t));
                CRSF_OnFlightMode(&flight_mode);
            }
            break;

        default:
            break;
    }

    /* 调用通用回调 */
    CRSF_OnFrameReceived(frame_type, &frame[1], length - 1);
}

/**
 * @brief 添加设备信息
 */
void CRSF_AddDevice(uint8_t* buffer)
{
    if (buffer[1] < 4) {
        return;
    }

    uint8_t dest_addr = buffer[1];
    uint8_t orig_addr = buffer[2];

    /* 查找设备槽位 */
    uint8_t device_index = 0;
    for (uint8_t i = 0; i < CRSF_MAX_DEVICES; i++) {
        if (crsf_devices[i].address == orig_addr || crsf_devices[i].address == 0) {
            device_index = i;
            break;
        }
    }

    /* 解析设备信息 */
    crsf_device_t* device = &crsf_devices[device_index];
    device->address = orig_addr;

    uint8_t* data = &buffer[3];
    device->serial_number = (data[0] << 24) | (data[1] << 16) | (data[2] << 8) | data[3];
    device->hardware_id = (data[4] << 24) | (data[5] << 16) | (data[6] << 8) | data[7];
    device->firmware_id = (data[8] << 24) | (data[9] << 16) | (data[10] << 8) | data[11];
    device->number_of_params = data[12];
    device->params_version = data[13];

    /* 复制设备名称 */
    uint8_t name_len = MIN(strlen((char*)&data[14]), CRSF_MAX_NAME_LEN - 1);
    memcpy(device->name, &data[14], name_len);
    device->name[name_len] = '\0';

    /* 调用回调 */
    CRSF_OnDeviceInfo(device);
}

/**
 * @brief 解析ELRS信息
 */
void CRSF_ParseElrsInfo(uint8_t* buffer)
{
    if (buffer[1] < 6) {
        return;
    }

    local_info.bad_pkts = buffer[3];
    local_info.good_pkts = (buffer[4] << 8) | buffer[5];
    local_info.flags = buffer[6];

    /* 复制状态信息 */
    if (buffer[1] > 6) {
        uint8_t info_len = MIN(buffer[1] - 6, sizeof(local_info.flag_info) - 1);
        memcpy(local_info.flag_info, &buffer[7], info_len);
        local_info.flag_info[info_len] = '\0';
    }

    /* 更新全局信息 */
    if (memcmp(&elrs_info, &local_info, sizeof(elrs_info_t)) != 0) {
        memcpy(&elrs_info, &local_info, sizeof(elrs_info_t));
        elrs_info.update++;
    }
}

/**
 * @brief 添加参数信息
 */
void CRSF_AddParam(uint8_t* buffer, uint8_t length)
{
    /* 参数解析实现 */
    /* 这里可以根据需要实现参数存储和管理 */
}

/**
 * @brief 检查链路状态
 */
void CRSF_CheckLinkState(void)
{
    static uint32_t last_check_time = 0;
    uint32_t current_time = millis();

    if (current_time - last_check_time >= 2000) { // 每2秒检查一次
        last_check_time = current_time;

        if (txConnected > 0) {
            if (local_info.good_pkts == 0) {
                CRSF_GetElrsInfo(ELRS_ADDRESS);
            }

            if (rxConnected == 0) {
                /* 清除RX设备信息 */
                crsf_devices[1].address = 0;
                memset(crsf_devices[1].name, 0, CRSF_MAX_NAME_LEN);
            } else {
                if (crsf_devices[1].address == 0) {
                    CRSF_BroadcastPing();
                }
            }
        } else {
            /* 清除TX设备信息 */
            crsf_devices[0].address = 0;
            memset(crsf_devices[0].name, 0, CRSF_MAX_NAME_LEN);
            local_info.good_pkts = 0;
        }

        /* 重置连接计数器 */
        rxConnected = 0;
        txConnected = 0;
    }
}

/**
 * @brief 检查是否连接
 */
bool CRSF_IsConnected(void)
{
    return (txConnected > 0 && rxConnected > 0);
}

/**
 * @brief 获取链路质量
 */
uint8_t CRSF_GetLinkQuality(void)
{
    return LinkStatistics.uplink_Link_quality;
}

/**
 * @brief 获取RSSI
 */
int8_t CRSF_GetRSSI(void)
{
    return LinkStatistics.uplink_RSSI_1;
}

/**
 * @brief 同步时序
 */
void CRSF_SyncTiming(int32_t delay_us)
{
    crsfTime = micros();
    int32_t offset = crsfTime - lastCrsfTime;
    uint32_t updated_interval = CRSF_GetUpdateInterval();

    crsfTime += (updated_interval + delay_us) - offset;
    lastCrsfTime = crsfTime;
}

/**
 * @brief 获取更新间隔
 */
uint32_t CRSF_GetUpdateInterval(void)
{
    return updateInterval;
}

/**
 * @brief 检查是否到发送时间
 */
bool CRSF_IsTimeToSend(void)
{
    return (micros() >= crsfTime);
}

/**
 * @brief 统计已加载参数数量
 */
uint8_t CRSF_CountParamsLoaded(uint8_t device_index)
{
    if (device_index >= CRSF_MAX_DEVICES) {
        return 0;
    }

    return crsf_devices[device_index].number_of_params;
}

/* 弱定义的回调函数 */
__weak void CRSF_OnFrameReceived(uint8_t frame_type, uint8_t* data, uint8_t length)
{
    /* 用户可以重写此函数 */
}

__weak void CRSF_OnDeviceInfo(crsf_device_t* device)
{
    /* 用户可以重写此函数 */
}

__weak void CRSF_OnLinkStatistics(crsf_link_statistics_t* stats)
{
    /* 用户可以重写此函数 */
}

__weak void CRSF_OnBatteryInfo(crsf_battery_sensor_t* battery)
{
    /* 用户可以重写此函数 */
}

__weak void CRSF_OnGpsInfo(crsf_gps_t* gps)
{
    /* 用户可以重写此函数 */
}

__weak void CRSF_OnAttitude(crsf_attitude_t* attitude)
{
    /* 用户可以重写此函数 */
}

__weak void CRSF_OnFlightMode(crsf_flight_mode_t* flight_mode)
{
    /* 用户可以重写此函数 */
}
