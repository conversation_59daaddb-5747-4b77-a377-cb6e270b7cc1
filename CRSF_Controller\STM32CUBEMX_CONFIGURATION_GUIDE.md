# STM32CubeMX 配置指南

## 📖 **概述**

本指南详细介绍如何使用STM32CubeMX为CRSF Controller项目进行硬件配置，包括时钟配置、外设配置、中断设置等。

## 🚀 **项目创建**

### 1. 新建项目
```
1. 启动STM32CubeMX
2. 选择 "File" → "New Project"
3. 在MCU Selector中搜索 "STM32F072CBT6"
4. 选择对应的MCU型号
5. 点击 "Start Project"
```

### 2. 项目基本设置
```
Project Manager → Project:
- Project Name: CRSF_Controller
- Project Location: 选择项目目录
- Toolchain/IDE: Makefile (或选择你使用的IDE)

Project Manager → Code Generator:
☑ Generate peripheral initialization as a pair of '.c/.h' files per peripheral
☑ Keep User Code when re-generating
☑ Delete previously generated files when not re-generated
```

## ⏰ **时钟配置 (Clock Configuration)**

### 1. 时钟源配置
```
Clock Configuration 标签页:

1. HSE (High Speed External):
   - HSE: Crystal/Ceramic Resonator
   - HSE Value: 8 MHz

2. PLL Configuration:
   - PLL Source: HSE
   - PLLMUL: x6 (8MHz × 6 = 48MHz)
   - PLLDIV: /1

3. System Clock:
   - System Clock Source: PLLCLK
   - HCLK: 48 MHz
   - APB1 Prescaler: /1 (48 MHz)
   - APB2 Prescaler: /1 (48 MHz)

4. USB Clock:
   - USB Clock Source: HSI48
   - HSI48: Enable
```

### 2. 外设时钟使能
```
在 Pinout & Configuration → System Core → RCC:

☑ HSE: Crystal/Ceramic Resonator
☑ HSI48: Enable (用于USB)
☑ LSI: Enable (用于IWDG)

时钟输出 (可选):
- MCO: PA8 (用于调试时钟)
```

## 📌 **引脚配置 (Pinout Configuration)**

### 1. ADC输入配置
```
Pinout & Configuration → Analog:

ADC:
- PA0: ADC_IN0 (CH1 - 右摇杆X)
- PA1: ADC_IN1 (CH2 - 右摇杆Y)
- PA2: ADC_IN2 (CH3 - 左摇杆Y)
- PA3: ADC_IN3 (CH4 - 左摇杆X)
- PA4: ADC_IN4 (SWA - 三段开关A)
- PA5: ADC_IN5 (SWB - 三段开关B)
- PA6: ADC_IN6 (VRA - 电位器A)
- PA7: ADC_IN7 (VRB - 电位器B)
- PB0: ADC_IN8 (VBAT - 电池电压)
- PB1: ADC_IN9 (VIN - 外接电源)

配置参数:
- Mode: Independent mode
- Clock Prescaler: Synchronous clock mode divided by 4
- Resolution: 12 bits
- Data Alignment: Right alignment
- Scan Conversion Mode: Enable
- Continuous Conversion Mode: Enable
- DMA Continuous Requests: Enable
```

### 2. UART配置 (CRSF通信)
```
Pinout & Configuration → Connectivity:

USART1:
- Mode: Asynchronous
- PA9: USART1_TX
- PA10: USART1_RX

Parameter Settings:
- Baud Rate: 420000 Bits/s
- Word Length: 8 Bits
- Parity: None
- Stop Bits: 1
- Data Direction: Receive and Transmit
- Over Sampling: 16 Samples

Advanced Features:
☑ DMA Settings:
  - USART1_TX: DMA1 Channel 2, Memory To Peripheral, Normal
  - USART1_RX: DMA1 Channel 3, Peripheral To Memory, Circular

☑ NVIC Settings:
  - USART1 global interrupt: Enable
  - DMA1 channel 2 global interrupt: Enable
  - DMA1 channel 3 global interrupt: Enable
```

### 3. I2C配置 (OLED显示)
```
I2C2:
- Mode: I2C
- PB10: I2C2_SCL
- PB11: I2C2_SDA

Parameter Settings:
- I2C Speed Mode: Standard Mode
- I2C Clock Speed: 100000 Hz
- Clock No Stretch Mode: Disable
- Primary Address Length selection: 7-bit
- Dual Address Acknowledged: Disable
- General Call Address Detection: Disable
- Clock Stretching Disable: Disable

Advanced Features:
☑ DMA Settings:
  - I2C2_TX: DMA1 Channel 4, Memory To Peripheral, Normal
  - I2C2_RX: DMA1 Channel 5, Peripheral To Memory, Normal

☑ NVIC Settings:
  - I2C2 event interrupt: Enable
  - I2C2 error interrupt: Enable
```

### 4. USB配置
```
USB_DEVICE:
- Class For FS IP: Communication Device Class (Virtual Port Com)
- PA11: USB_DM
- PA12: USB_DP

Parameter Settings:
- Device Descriptor FS:
  - VID: 0x0483 (STMicroelectronics)
  - PID: 0x5740 (Virtual COM Port)
  - Product String: "CRSF Controller"
  - Manufacturer String: "CRSF Team"

☑ NVIC Settings:
  - USB global interrupt: Enable
```

### 5. 定时器配置
```
TIM1 (音效PWM):
- Channel1: PA8 (蜂鸣器)
- Mode: PWM Generation CH1

Parameter Settings:
- Prescaler: 47 (48MHz/48 = 1MHz)
- Counter Period: 1000 (1kHz PWM)
- Pulse: 0 (初始占空比0%)

TIM2 (CRSF定时器):
- Internal Clock
- Mode: Internal Clock

Parameter Settings:
- Prescaler: 47 (1MHz时钟)
- Counter Period: 3999 (4ms周期)
- auto-reload preload: Enable

☑ NVIC Settings:
  - TIM2 global interrupt: Enable, Priority 0 (最高)

TIM3 (震动电机PWM):
- Channel1: PE3 (震动电机)
- Mode: PWM Generation CH1

Parameter Settings:
- Prescaler: 47 (1MHz时钟)
- Counter Period: 1000 (1kHz PWM)
- Pulse: 0 (初始关闭)

TIM7 (ADC触发):
- Internal Clock
- Mode: Internal Clock

Parameter Settings:
- Prescaler: 47 (1MHz时钟)
- Counter Period: 999 (1ms触发周期)
- auto-reload preload: Enable
- Trigger Event Selection: Update Event

☑ NVIC Settings:
  - TIM7 global interrupt: Enable
```

### 6. GPIO配置
```
按键输入:
- PD0: GPIO_Input (KEY1 - 菜单键)
- PD1: GPIO_Input (KEY2 - 上键)
- PD2: GPIO_Input (KEY3 - 下键)
- PD3: GPIO_Input (KEY4 - 左键)
- PD4: GPIO_Input (KEY5 - 确认键)

GPIO配置:
- GPIO mode: Input mode
- GPIO Pull-up/Pull-down: Pull-up
- Maximum output speed: Low

LED输出:
- PE4: GPIO_Output (LED1 - 电源指示)
- PE5: GPIO_Output (LED2 - 连接指示)
- PE6: GPIO_Output (LED3 - 数据指示)
- PE7: GPIO_Output (LED4 - 错误指示)

GPIO配置:
- GPIO mode: Output Push Pull
- GPIO Pull-up/Pull-down: No pull-up and no pull-down
- Maximum output speed: Low
- User Label: LED1, LED2, LED3, LED4

软件中断引脚:
- PB1: GPIO_EXTI1 (混音器计算)
- PB3: GPIO_EXTI3 (一次性任务)
- PB4: GPIO_EXTI4 (UART处理)

EXTI配置:
- External Interrupt Mode: Rising edge trigger detection
- GPIO Pull-up/Pull-down: No pull-up and no pull-down

☑ NVIC Settings:
  - EXTI line[1] interrupt: Enable, Priority 1
  - EXTI line[3] interrupt: Enable, Priority 3
  - EXTI line[4:15] interrupts: Enable, Priority 2
```

## 🔧 **外设详细配置**

### 1. ADC详细配置
```
Configuration → ADC:

Parameter Settings:
- Clock Prescaler: Synchronous clock mode divided by 4
- Resolution: ADC_RESOLUTION_12B
- Data Alignment: ADC_DATAALIGN_RIGHT
- Scan Conversion Mode: ENABLE
- EOC Selection: ADC_EOC_SEQ_CONV
- Low Power Auto Wait: DISABLE
- Low Power Auto Power Off: DISABLE
- Channels Bank: Bank A
- Continuous Conversion Mode: ENABLE
- Number Of Conversion: 10
- DMA Continuous Requests: ENABLE
- End Of Conversion Selection: EOC flag at the end of sequence

Regular Conversion:
Rank 1: Channel 0 (PA0), Sampling Time: 239.5 Cycles
Rank 2: Channel 1 (PA1), Sampling Time: 239.5 Cycles
Rank 3: Channel 2 (PA2), Sampling Time: 239.5 Cycles
Rank 4: Channel 3 (PA3), Sampling Time: 239.5 Cycles
Rank 5: Channel 4 (PA4), Sampling Time: 239.5 Cycles
Rank 6: Channel 5 (PA5), Sampling Time: 239.5 Cycles
Rank 7: Channel 6 (PA6), Sampling Time: 239.5 Cycles
Rank 8: Channel 7 (PA7), Sampling Time: 239.5 Cycles
Rank 9: Channel 8 (PB0), Sampling Time: 239.5 Cycles
Rank 10: Channel 9 (PB1), Sampling Time: 239.5 Cycles

DMA Settings:
- DMA Request: ADC
- Stream: DMA1 Channel 1
- Direction: Peripheral To Memory
- Priority: High
- Mode: Circular
- Data Width: Half Word (16-bit)
```

### 2. DMA详细配置
```
Configuration → DMA:

DMA1:
- Channel 1: ADC (Peripheral To Memory, Circular, High Priority)
- Channel 2: USART1_TX (Memory To Peripheral, Normal, Medium Priority)
- Channel 3: USART1_RX (Peripheral To Memory, Circular, Medium Priority)
- Channel 4: I2C2_TX (Memory To Peripheral, Normal, Low Priority)
- Channel 5: I2C2_RX (Peripheral To Memory, Normal, Low Priority)

每个通道配置:
- Data Width: Half Word (ADC) / Byte (UART/I2C)
- Increment Address: Memory地址递增，Peripheral地址不变
- DMA Request: 对应外设的DMA请求
```

### 3. NVIC中断优先级配置
```
Configuration → NVIC:

中断优先级 (数字越小优先级越高):
- TIM2 global interrupt: Priority 0, Subpriority 0 (CRSF定时器 - 最高)
- SysTick interrupt: Priority 0, Subpriority 0 (系统时钟 - 最高)
- EXTI line[1] interrupt: Priority 1, Subpriority 0 (混音器 - 高)
- EXTI line[4:15] interrupts: Priority 2, Subpriority 0 (UART处理 - 中)
- EXTI line[3] interrupt: Priority 3, Subpriority 0 (后台任务 - 低)
- USART1 global interrupt: Priority 4, Subpriority 0
- DMA1 channel 1 interrupt: Priority 5, Subpriority 0 (ADC DMA)
- DMA1 channel 2 interrupt: Priority 4, Subpriority 1 (UART TX DMA)
- DMA1 channel 3 interrupt: Priority 4, Subpriority 2 (UART RX DMA)
- I2C2 event interrupt: Priority 6, Subpriority 0
- I2C2 error interrupt: Priority 6, Subpriority 1
- USB global interrupt: Priority 7, Subpriority 0
- TIM7 global interrupt: Priority 5, Subpriority 1 (ADC触发)
```

## 🛠️ **高级配置**

### 1. 看门狗配置
```
Configuration → IWDG:

Parameter Settings:
- LSI RC: Enable
- Prescaler divider: 64
- Down-counter reload value: 4095
- Window value: 4095

计算超时时间:
Timeout = (4095 * 64) / 37000 ≈ 7.1 seconds
```

### 2. CRC配置 (用于数据校验)
```
Configuration → CRC:

Parameter Settings:
- CRC Calculation Unit: CRC-32
- Generating Polynomial: 0x04C11DB7 (标准CRC32)
- Initialization Value: 0xFFFFFFFF
- Input Data Inversion: None
- Output Data Inversion: Disable
```

### 3. 电源管理配置
```
Configuration → PWR:

Parameter Settings:
- Power Regulator: Main Regulator
- FLASH Power Down: Enable in Stop mode
- Low Power Regulator: Enable in Stop mode
- Ultra Low Power: Disable

Wake-up Sources:
☑ WKUP1 (PA0): Enable
☑ WKUP2 (PC13): Enable
```

## 📁 **代码生成配置**

### 1. 项目设置
```
Project Manager → Project:

Project Settings:
- Project Name: CRSF_Controller
- Project Location: 选择合适的目录
- Toolchain/IDE: Makefile
- Firmware Package Name and Version: STM32Cube FW_F0 V1.11.0

Code Generator:
☑ Copy only the necessary library files
☑ Generate peripheral initialization as a pair of '.c/.h' files per peripheral
☑ Backup previously generated files when re-generating
☑ Keep User Code when re-generating
☑ Delete previously generated files when not re-generated

Advanced Settings:
- Set all free pins as analog (to reduce power consumption): No
- Initialize all peripherals with their default Mode: No
```

### 2. 代码生成选项
```
Project Manager → Advanced Settings:

Driver Selector:
- ADC: HAL
- DMA: HAL  
- GPIO: HAL
- I2C: HAL
- RCC: HAL
- SYS: HAL
- TIM: HAL
- USART: HAL
- USB_DEVICE: HAL
- IWDG: HAL
- CRC: HAL
- PWR: HAL

Generated Function Calls:
☑ MX_ADC_Init()
☑ MX_DMA_Init()
☑ MX_GPIO_Init()
☑ MX_I2C2_Init()
☑ MX_TIM1_Init()
☑ MX_TIM2_Init()
☑ MX_TIM3_Init()
☑ MX_TIM7_Init()
☑ MX_USART1_UART_Init()
☑ MX_USB_DEVICE_Init()
☑ MX_IWDG_Init()
☑ MX_CRC_Init()
```

## 🔍 **配置验证**

### 1. 引脚冲突检查
```
在Pinout view中检查:
- 确保没有引脚冲突 (红色标记)
- 检查所有必需的引脚都已配置
- 验证复用功能配置正确
```

### 2. 时钟配置验证
```
在Clock Configuration中检查:
- 系统时钟: 48MHz ✓
- APB1时钟: 48MHz ✓  
- APB2时钟: 48MHz ✓
- USB时钟: 48MHz (来自HSI48) ✓
- ADC时钟: 12MHz (48MHz/4) ✓
```

### 3. 资源使用检查
```
检查资源使用情况:
- Flash: 预计使用 < 64KB
- RAM: 预计使用 < 12KB
- DMA通道: 使用5个通道 (共7个可用)
- 定时器: 使用4个定时器 (TIM1,2,3,7)
```

## 📋 **生成代码后的修改**

### 1. main.c修改
```c
// 在main.c中添加用户代码区域
/* USER CODE BEGIN Includes */
#include "crsf_protocol_v2.h"
#include "mixer.h"
#include "adc_input.h"
// ... 其他头文件
/* USER CODE END Includes */

/* USER CODE BEGIN 2 */
// 在初始化完成后添加应用程序初始化
CRSF_Protocol_Init();
MIXER_Init();
ADC_Input_Init();
// ... 其他模块初始化
/* USER CODE END 2 */

/* USER CODE BEGIN WHILE */
while (1)
{
    // 主循环代码
    System_MainLoop();
    
    /* USER CODE END WHILE */
    /* USER CODE BEGIN 3 */
}
/* USER CODE END 3 */
```

### 2. 中断回调函数
```c
// 在stm32f0xx_it.c中添加中断处理
/* USER CODE BEGIN 1 */
extern void CLOCK_SysTickCallback(void);
extern void CRSF_TimerCallback(void);
/* USER CODE END 1 */

void SysTick_Handler(void)
{
    /* USER CODE BEGIN SysTick_IRQn 0 */
    CLOCK_SysTickCallback();
    /* USER CODE END SysTick_IRQn 0 */
    
    HAL_IncTick();
    
    /* USER CODE BEGIN SysTick_IRQn 1 */
    /* USER CODE END SysTick_IRQn 1 */
}

void TIM2_IRQHandler(void)
{
    /* USER CODE BEGIN TIM2_IRQn 0 */
    CRSF_TimerCallback();
    /* USER CODE END TIM2_IRQn 0 */
    
    HAL_TIM_IRQHandler(&htim2);
    
    /* USER CODE BEGIN TIM2_IRQn 1 */
    /* USER CODE END TIM2_IRQn 1 */
}
```

## 🎯 **配置总结**

通过STM32CubeMX配置，我们实现了：

✅ **完整的硬件配置**: 所有外设的正确配置  
✅ **优化的时钟系统**: 48MHz系统时钟，各外设时钟合理分配  
✅ **合理的中断优先级**: 确保实时性能  
✅ **高效的DMA配置**: 减少CPU负担  
✅ **标准的代码结构**: 便于维护和扩展  

这个配置为CRSF Controller提供了坚实的硬件基础！
