/**
 * @file crsf_protocol.c
 * @brief CRSF协议基础实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "crsf_protocol.h"

/* CRC8查找表 */
static const uint8_t crc8tab[256] = {
    0x00, 0xD5, 0x7F, 0xAA, 0xFE, 0x2B, 0x81, 0x54, 0x29, 0xFC, 0x56, 0x83, 0xD7, 0x02, 0xA8, 0x7D,
    0x52, 0x87, 0x2D, 0xF8, 0xAC, 0x79, 0xD3, 0x06, 0x7B, 0xAE, 0x04, 0xD1, 0x85, 0x50, 0xFA, 0x2F,
    0xA4, 0x71, 0xDB, 0x0E, 0x5A, 0x8F, 0x25, 0xF0, 0x8D, 0x58, 0xF2, 0x27, 0x73, 0xA6, 0x0C, 0xD9,
    0xF6, 0x23, 0x89, 0x5C, 0x08, 0xDD, 0x77, 0xA2, 0xDF, 0x0A, 0xA0, 0x75, 0x21, 0xF4, 0x5E, 0x8B,
    0x9D, 0x48, 0xE2, 0x37, 0x63, 0xB6, 0x1C, 0xC9, 0xB4, 0x61, 0xCB, 0x1E, 0x4A, 0x9F, 0x35, 0xE0,
    0xCF, 0x1A, 0xB0, 0x65, 0x31, 0xE4, 0x4E, 0x9B, 0xE6, 0x33, 0x99, 0x4C, 0x18, 0xCD, 0x67, 0xB2,
    0x39, 0xEC, 0x46, 0x93, 0xC7, 0x12, 0xB8, 0x6D, 0x10, 0xC5, 0x6F, 0xBA, 0xEE, 0x3B, 0x91, 0x44,
    0x6B, 0xBE, 0x14, 0xC1, 0x95, 0x40, 0xEA, 0x3F, 0x42, 0x97, 0x3D, 0xE8, 0xBC, 0x69, 0xC3, 0x16,
    0xEF, 0x3A, 0x90, 0x45, 0x11, 0xC4, 0x6E, 0xBB, 0xC6, 0x13, 0xB9, 0x6C, 0x38, 0xED, 0x47, 0x92,
    0xBD, 0x68, 0xC2, 0x17, 0x43, 0x96, 0x3C, 0xE9, 0x94, 0x41, 0xEB, 0x3E, 0x6A, 0xBF, 0x15, 0xC0,
    0x4B, 0x9E, 0x34, 0xE1, 0xB5, 0x60, 0xCA, 0x1F, 0x62, 0xB7, 0x1D, 0xC8, 0x9C, 0x49, 0xE3, 0x36,
    0x19, 0xCC, 0x66, 0xB3, 0xE7, 0x32, 0x98, 0x4D, 0x30, 0xE5, 0x4F, 0x9A, 0xCE, 0x1B, 0xB1, 0x64,
    0x72, 0xA7, 0x0D, 0xD8, 0x8C, 0x59, 0xF3, 0x26, 0x5B, 0x8E, 0x24, 0xF1, 0xA5, 0x70, 0xDA, 0x0F,
    0x20, 0xF5, 0x5F, 0x8A, 0xDE, 0x0B, 0xA1, 0x74, 0x09, 0xDC, 0x76, 0xA3, 0xF7, 0x22, 0x88, 0x5D,
    0xD6, 0x03, 0xA9, 0x7C, 0x28, 0xFD, 0x57, 0x82, 0xFF, 0x2A, 0x80, 0x55, 0x01, 0xD4, 0x7E, 0xAB,
    0x84, 0x51, 0xFB, 0x2E, 0x7A, 0xAF, 0x05, 0xD0, 0xAD, 0x78, 0xD2, 0x07, 0x53, 0x86, 0x2C, 0xF9
};

/* CRC8_BA查找表 */
static const uint8_t crc8tab_ba[256] = {
    0x00, 0xBA, 0x7C, 0xC6, 0xF8, 0x42, 0x84, 0x3E, 0xEC, 0x56, 0x90, 0x2A, 0x14, 0xAE, 0x68, 0xD2,
    0xC5, 0x7F, 0xB9, 0x03, 0x3D, 0x87, 0x41, 0xFB, 0x29, 0x93, 0x55, 0xEF, 0xD1, 0x6B, 0xAD, 0x17,
    0x97, 0x2D, 0xEB, 0x51, 0x6F, 0xD5, 0x13, 0xA9, 0x7B, 0xC1, 0x07, 0xBD, 0x83, 0x39, 0xFF, 0x45,
    0x52, 0xE8, 0x2E, 0x94, 0xAA, 0x10, 0xD6, 0x6C, 0xBE, 0x04, 0xC2, 0x78, 0x46, 0xFC, 0x3A, 0x80,
    0x35, 0x8F, 0x49, 0xF3, 0xCD, 0x77, 0xB1, 0x0B, 0xD9, 0x63, 0xA5, 0x1F, 0x21, 0x9B, 0x5D, 0xE7,
    0xF0, 0x4A, 0x8C, 0x36, 0x08, 0xB2, 0x74, 0xCE, 0x1C, 0xA6, 0x60, 0xDA, 0xE4, 0x5E, 0x98, 0x22,
    0xA2, 0x18, 0xDE, 0x64, 0x5A, 0xE0, 0x26, 0x9C, 0x4E, 0xF4, 0x32, 0x88, 0xB6, 0x0C, 0xCA, 0x70,
    0x67, 0xDD, 0x1B, 0xA1, 0x9F, 0x25, 0xE3, 0x59, 0x8B, 0x31, 0xF7, 0x4D, 0x73, 0xC9, 0x0F, 0xB5,
    0x6A, 0xD0, 0x16, 0xAC, 0x92, 0x28, 0xEE, 0x54, 0x86, 0x3C, 0xFA, 0x40, 0x7E, 0xC4, 0x02, 0xB8,
    0xAF, 0x15, 0xD3, 0x69, 0x57, 0xED, 0x2B, 0x91, 0x43, 0xF9, 0x3F, 0x85, 0xBB, 0x01, 0xC7, 0x7D,
    0xFD, 0x47, 0x81, 0x3B, 0x05, 0xBF, 0x79, 0xC3, 0x11, 0xAB, 0x6D, 0xD7, 0xE9, 0x53, 0x95, 0x2F,
    0x38, 0x82, 0x44, 0xFE, 0xC0, 0x7A, 0xBC, 0x06, 0xD4, 0x6E, 0xA8, 0x12, 0x2C, 0x96, 0x50, 0xEA,
    0x5F, 0xE5, 0x23, 0x99, 0xA7, 0x1D, 0xDB, 0x61, 0xB3, 0x09, 0xCF, 0x75, 0x4B, 0xF1, 0x37, 0x8D,
    0x9A, 0x20, 0xE6, 0x5C, 0x62, 0xD8, 0x1E, 0xA4, 0x76, 0xCC, 0x0A, 0xB0, 0x8E, 0x34, 0xF2, 0x48,
    0xC8, 0x72, 0xB4, 0x0E, 0x30, 0x8A, 0x4C, 0xF6, 0x24, 0x9E, 0x58, 0xE2, 0xDC, 0x66, 0xA0, 0x1A,
    0x0D, 0xB7, 0x71, 0xCB, 0xF5, 0x4F, 0x89, 0x33, 0xE1, 0x5B, 0x9D, 0x27, 0x19, 0xA3, 0x65, 0xDF
};

/**
 * @brief 计算CRC8校验值
 * @param ptr 数据指针
 * @param len 数据长度
 * @return CRC8校验值
 */
uint8_t crsf_crc8(const uint8_t *ptr, uint8_t len)
{
    uint8_t crc = 0;
    for (uint8_t i = 0; i < len; i++) {
        crc = crc8tab[crc ^ *ptr++];
    }
    return crc;
}

/**
 * @brief 计算CRC8_BA校验值
 * @param ptr 数据指针
 * @param len 数据长度
 * @return CRC8_BA校验值
 */
uint8_t crsf_crc8_ba(const uint8_t *ptr, uint8_t len)
{
    uint8_t crc = 0;
    for (uint8_t i = 0; i < len; i++) {
        crc = crc8tab_ba[crc ^ *ptr++];
    }
    return crc;
}
