/**
 * @file usbd_cdc_if.c
 * @brief USB CDC接口实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "usbd_cdc_if.h"
#include "usb_cdc.h"

/* 私有函数声明 */
static int8_t CDC_Init_FS(void);
static int8_t CDC_DeInit_FS(void);
static int8_t CDC_Control_FS(uint8_t cmd, uint8_t* pbuf, uint16_t length);
static int8_t CDC_Receive_FS(uint8_t* pbuf, uint32_t *Len);

/* CDC接口函数结构 */
USBD_CDC_ItfTypeDef USBD_Interface_fops_FS = {
    CDC_Init_FS,
    CDC_DeInit_FS,
    CDC_Control_FS,
    CDC_Receive_FS
};

/* 私有变量 */
static uint8_t UserRxBufferFS[APP_RX_DATA_SIZE];
static uint8_t UserTxBufferFS[APP_TX_DATA_SIZE];

/**
 * @brief CDC初始化
 */
static int8_t CDC_Init_FS(void)
{
    /* 设置接收缓冲区 */
    USBD_CDC_SetRxBuffer(&hUsbDeviceFS, &UserRxBufferFS[0]);
    
    /* 调用连接回调 */
    USB_CDC_OnConnect();
    
    return USBD_OK;
}

/**
 * @brief CDC去初始化
 */
static int8_t CDC_DeInit_FS(void)
{
    /* 调用断开回调 */
    USB_CDC_OnDisconnect();
    
    return USBD_OK;
}

/**
 * @brief CDC控制请求处理
 */
static int8_t CDC_Control_FS(uint8_t cmd, uint8_t* pbuf, uint16_t length)
{
    switch (cmd) {
        case CDC_SEND_ENCAPSULATED_COMMAND:
            break;
            
        case CDC_GET_ENCAPSULATED_RESPONSE:
            break;
            
        case CDC_SET_COMM_FEATURE:
            break;
            
        case CDC_GET_COMM_FEATURE:
            break;
            
        case CDC_CLEAR_COMM_FEATURE:
            break;
            
        case CDC_SET_LINE_CODING:
            /* 设置线路编码 (波特率等) */
            break;
            
        case CDC_GET_LINE_CODING:
            /* 获取线路编码 */
            pbuf[0] = (uint8_t)(115200);
            pbuf[1] = (uint8_t)(115200 >> 8);
            pbuf[2] = (uint8_t)(115200 >> 16);
            pbuf[3] = (uint8_t)(115200 >> 24);
            pbuf[4] = 0; // 停止位
            pbuf[5] = 0; // 校验位
            pbuf[6] = 8; // 数据位
            break;
            
        case CDC_SET_CONTROL_LINE_STATE:
            /* 设置控制线状态 */
            break;
            
        case CDC_SEND_BREAK:
            break;
            
        default:
            break;
    }
    
    return USBD_OK;
}

/**
 * @brief CDC数据接收处理
 */
static int8_t CDC_Receive_FS(uint8_t* Buf, uint32_t *Len)
{
    /* 调用数据接收回调 */
    USB_CDC_OnDataReceived(Buf, *Len);
    
    /* 重新启动接收 */
    USBD_CDC_SetRxBuffer(&hUsbDeviceFS, &UserRxBufferFS[0]);
    USBD_CDC_ReceivePacket(&hUsbDeviceFS);
    
    return USBD_OK;
}

/**
 * @brief CDC数据发送
 */
uint8_t CDC_Transmit_FS(uint8_t* Buf, uint16_t Len)
{
    uint8_t result = USBD_OK;
    
    /* 设置发送缓冲区 */
    USBD_CDC_HandleTypeDef *hcdc = (USBD_CDC_HandleTypeDef*)hUsbDeviceFS.pClassData;
    if (hcdc->TxState != 0) {
        return USBD_BUSY;
    }
    
    USBD_CDC_SetTxBuffer(&hUsbDeviceFS, Buf, Len);
    result = USBD_CDC_TransmitPacket(&hUsbDeviceFS);
    
    if (result == USBD_OK) {
        /* 调用发送完成回调 */
        USB_CDC_OnDataSent();
    }
    
    return result;
}
