/**
 * @file hal_drivers.c
 * @brief HAL驱动实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "hal_drivers.h"

/* 外设句柄定义 */
UART_HandleTypeDef huart1;      // CRSF UART
I2C_HandleTypeDef hi2c2;        // OLED & EEPROM I2C
ADC_HandleTypeDef hadc1;        // ADC
TIM_HandleTypeDef htim1;        // 蜂鸣器PWM定时器
TIM_HandleTypeDef htim2;        // 微秒定时器
TIM_HandleTypeDef htim3;        // 震动电机PWM定时器
TIM_HandleTypeDef htim6;        // 任务调度定时器
TIM_HandleTypeDef htim7;        // ADC触发定时器
DMA_HandleTypeDef hdma_usart1_rx;
DMA_HandleTypeDef hdma_usart1_tx;
DMA_HandleTypeDef hdma_adc1;
PCD_HandleTypeDef hpcd_USB_FS;  // USB设备

/* 私有变量 */
static volatile uint32_t uwTick = 0;
static volatile uint32_t microsCounter = 0;

/**
 * @brief 系统初始化
 */
error_code_t HAL_System_Init(void)
{
    /* 复位所有外设，初始化Flash接口和Systick */
    if (HAL_Init() != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 配置系统时钟 */
    SystemClock_Config();

    /* 初始化所有GPIO */
    if (HAL_GPIO_Init_All() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 初始化DMA */
    if (HAL_DMA_Init_All() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 初始化UART */
    if (HAL_UART_Init_All() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 初始化I2C */
    if (HAL_I2C_Init_All() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 初始化ADC */
    if (HAL_ADC_Init_All() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 初始化定时器 */
    if (HAL_TIM_Init_All() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 配置中断优先级 */
    HAL_NVIC_Config();

    return ERR_OK;
}

/**
 * @brief 系统时钟配置
 */
void SystemClock_Config(void)
{
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

    /* 配置主内部RC振荡器 */
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI48;
    RCC_OscInitStruct.HSI48State = RCC_HSI48_ON;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
    if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK) {
        Error_Handler();
    }

    /* 初始化CPU、AHB和APB总线时钟 */
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK
                                | RCC_CLOCKTYPE_PCLK1;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSI48;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;

    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_1) != HAL_OK) {
        Error_Handler();
    }
}

/**
 * @brief GPIO初始化
 */
error_code_t HAL_GPIO_Init_All(void)
{
    /* 使能GPIO时钟 */
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();

    /* 初始化各功能GPIO */
    HAL_GPIO_Button_Init();
    HAL_GPIO_ADC_Init();
    HAL_GPIO_UART_Init();
    HAL_GPIO_I2C_Init();

    return ERR_OK;
}

/**
 * @brief 按键GPIO初始化
 */
void HAL_GPIO_Button_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    /* 使能GPIO时钟 */
    __HAL_RCC_GPIOC_CLK_ENABLE();
    __HAL_RCC_GPIOD_CLK_ENABLE();

    /* 配置按键引脚 - PC口按键 */
    GPIO_InitStruct.Pin = KEY0_Pin | KEY1_Pin | KEY2_Pin | KEY3_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

    /* 配置按键引脚 - PD口按键 */
    GPIO_InitStruct.Pin = PWR_SW_Pin | KEY4_Pin | KEY5_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);

    /* 配置电源控制引脚 */
    GPIO_InitStruct.Pin = PWR_OFF_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(PWR_OFF_GPIO_Port, &GPIO_InitStruct);

    /* 配置充电状态检测引脚 */
    GPIO_InitStruct.Pin = STDBY_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(STDBY_GPIO_Port, &GPIO_InitStruct);

    /* 配置LED引脚 */
    GPIO_InitStruct.Pin = LED4_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(LED4_GPIO_Port, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = LED5_Pin;
    HAL_GPIO_Init(LED5_GPIO_Port, &GPIO_InitStruct);

    /* 初始化LED状态 */
    HAL_GPIO_WritePin(LED4_GPIO_Port, LED4_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LED5_GPIO_Port, LED5_Pin, GPIO_PIN_RESET);
}

/**
 * @brief ADC GPIO初始化
 */
void HAL_GPIO_ADC_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    /* 使能GPIO时钟 */
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();

    /* 配置PA口ADC引脚 */
    GPIO_InitStruct.Pin = GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_2 | GPIO_PIN_3 |
                         GPIO_PIN_4 | GPIO_PIN_5;  // PA0-PA5 (CH1-CH4, SWA, SWB)
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    /* 配置PC口ADC引脚 */
    GPIO_InitStruct.Pin = GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_2 | GPIO_PIN_3;  // PC0-PC3 (VBAT, BIN, VRA, VRB)
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
}

/**
 * @brief UART GPIO初始化
 */
void HAL_GPIO_UART_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    /* UART1引脚配置 */
    GPIO_InitStruct.Pin = CRSF_UART_TX_PIN | CRSF_UART_RX_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF1_USART1;
    HAL_GPIO_Init(CRSF_UART_TX_PORT, &GPIO_InitStruct);
}

/**
 * @brief I2C GPIO初始化
 */
void HAL_GPIO_I2C_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    /* 使能GPIO时钟 */
    __HAL_RCC_GPIOB_CLK_ENABLE();

    /* I2C2引脚配置 */
    GPIO_InitStruct.Pin = GPIO_PIN_10 | GPIO_PIN_11;  // PB10(SCL), PB11(SDA)
    GPIO_InitStruct.Mode = GPIO_MODE_AF_OD;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF1_I2C2;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
}

/**
 * @brief PWM GPIO初始化
 */
void HAL_GPIO_PWM_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    /* 使能GPIO时钟 */
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOE_CLK_ENABLE();

    /* 蜂鸣器PWM引脚配置 - PA8 (TIM1_CH1) */
    GPIO_InitStruct.Pin = GPIO_PIN_8;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF2_TIM1;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    /* 震动电机PWM引脚配置 - PE3 (TIM3_CH1) */
    GPIO_InitStruct.Pin = GPIO_PIN_3;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF2_TIM3;
    HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);
}

/**
 * @brief USB GPIO初始化
 */
void HAL_GPIO_USB_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    /* 使能GPIO时钟 */
    __HAL_RCC_GPIOA_CLK_ENABLE();

    /* USB引脚配置 */
    GPIO_InitStruct.Pin = GPIO_PIN_11 | GPIO_PIN_12;  // PA11(DM), PA12(DP)
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF14_USB;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
}

/**
 * @brief 微秒计数器
 */
uint32_t micros(void)
{
    uint32_t ms = HAL_GetTick();
    uint32_t st = SysTick->VAL;
    return (ms * 1000) + (1000 - (st * 1000) / SysTick->LOAD);
}

/**
 * @brief 毫秒计数器
 */
uint32_t millis(void)
{
    return HAL_GetTick();
}

/**
 * @brief 微秒延时
 */
void delay_us(uint32_t us)
{
    uint32_t start = micros();
    while ((micros() - start) < us);
}

/**
 * @brief 毫秒延时
 */
void delay_ms(uint32_t ms)
{
    HAL_Delay(ms);
}

/**
 * @brief 错误处理函数
 */
void Error_Handler(void)
{
    /* 禁用中断 */
    __disable_irq();
    
    /* 无限循环 */
    while (1) {
        /* 可以在这里添加LED闪烁等错误指示 */
    }
}

/**
 * @brief 中断优先级配置
 */
void HAL_NVIC_Config(void)
{
    /* 设置中断优先级分组 */
    HAL_NVIC_SetPriorityGrouping(NVIC_PRIORITYGROUP_4);

    /* 配置各中断优先级 */
    HAL_NVIC_SetPriority(TIM6_IRQn, 0, 0);              // 任务调度最高优先级
    HAL_NVIC_SetPriority(DMA1_Channel2_3_IRQn, 1, 0);   // UART DMA
    HAL_NVIC_SetPriority(DMA1_Channel1_IRQn, 2, 0);     // ADC DMA
    HAL_NVIC_SetPriority(USART1_IRQn, 3, 0);            // UART中断
    HAL_NVIC_SetPriority(TIM7_IRQn, 4, 0);              // ADC触发定时器
}

/**
 * @brief SysTick中断回调
 */
void HAL_IncTick(void)
{
    uwTick += 1;
}

/**
 * @brief 获取系统Tick
 */
uint32_t HAL_GetTick(void)
{
    return uwTick;
}

/**
 * @brief DMA初始化
 */
error_code_t HAL_DMA_Init_All(void)
{
    /* 使能DMA时钟 */
    __HAL_RCC_DMA1_CLK_ENABLE();

    /* DMA1 Channel1 - ADC */
    hdma_adc1.Instance = DMA1_Channel1;
    hdma_adc1.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_adc1.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_adc1.Init.MemInc = DMA_MINC_ENABLE;
    hdma_adc1.Init.PeriphDataAlignment = DMA_PDATAALIGN_HALFWORD;
    hdma_adc1.Init.MemDataAlignment = DMA_MDATAALIGN_HALFWORD;
    hdma_adc1.Init.Mode = DMA_CIRCULAR;
    hdma_adc1.Init.Priority = DMA_PRIORITY_HIGH;
    if (HAL_DMA_Init(&hdma_adc1) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* DMA1 Channel2 - USART1_TX */
    hdma_usart1_tx.Instance = DMA1_Channel2;
    hdma_usart1_tx.Init.Direction = DMA_MEMORY_TO_PERIPH;
    hdma_usart1_tx.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_usart1_tx.Init.MemInc = DMA_MINC_ENABLE;
    hdma_usart1_tx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
    hdma_usart1_tx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
    hdma_usart1_tx.Init.Mode = DMA_NORMAL;
    hdma_usart1_tx.Init.Priority = DMA_PRIORITY_MEDIUM;
    if (HAL_DMA_Init(&hdma_usart1_tx) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* DMA1 Channel3 - USART1_RX */
    hdma_usart1_rx.Instance = DMA1_Channel3;
    hdma_usart1_rx.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_usart1_rx.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_usart1_rx.Init.MemInc = DMA_MINC_ENABLE;
    hdma_usart1_rx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
    hdma_usart1_rx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
    hdma_usart1_rx.Init.Mode = DMA_CIRCULAR;
    hdma_usart1_rx.Init.Priority = DMA_PRIORITY_MEDIUM;
    if (HAL_DMA_Init(&hdma_usart1_rx) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 使能DMA中断 */
    HAL_NVIC_SetPriority(DMA1_Channel1_IRQn, 2, 0);
    HAL_NVIC_EnableIRQ(DMA1_Channel1_IRQn);

    HAL_NVIC_SetPriority(DMA1_Channel2_3_IRQn, 1, 0);
    HAL_NVIC_EnableIRQ(DMA1_Channel2_3_IRQn);

    return ERR_OK;
}

/**
 * @brief UART初始化
 */
error_code_t HAL_UART_Init_All(void)
{
    /* 使能UART时钟 */
    __HAL_RCC_USART1_CLK_ENABLE();
    __HAL_RCC_USART2_CLK_ENABLE();

    /* UART1配置 - CRSF */
    huart1.Instance = USART1;
    huart1.Init.BaudRate = CRSF_UART_BAUDRATE;
    huart1.Init.WordLength = UART_WORDLENGTH_8B;
    huart1.Init.StopBits = UART_STOPBITS_1;
    huart1.Init.Parity = UART_PARITY_NONE;
    huart1.Init.Mode = UART_MODE_TX_RX;
    huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart1.Init.OverSampling = UART_OVERSAMPLING_16;
    huart1.Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
    huart1.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;
    if (HAL_UART_Init(&huart1) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 关联DMA */
    __HAL_LINKDMA(&huart1, hdmatx, hdma_usart1_tx);
    __HAL_LINKDMA(&huart1, hdmarx, hdma_usart1_rx);

    /* 使能UART中断 */
    HAL_NVIC_SetPriority(USART1_IRQn, 3, 0);
    HAL_NVIC_EnableIRQ(USART1_IRQn);

    return ERR_OK;
}

/**
 * @brief 安全的UART DMA发送
 */
error_code_t HAL_UART_Transmit_DMA_Safe(UART_HandleTypeDef *huart, uint8_t *pData, uint16_t Size)
{
    if (huart->gState != HAL_UART_STATE_READY) {
        return ERR_BUSY;
    }

    if (HAL_UART_Transmit_DMA(huart, pData, Size) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    return ERR_OK;
}

/**
 * @brief 安全的UART DMA接收
 */
error_code_t HAL_UART_Receive_DMA_Safe(UART_HandleTypeDef *huart, uint8_t *pData, uint16_t Size)
{
    if (huart->RxState != HAL_UART_STATE_READY) {
        return ERR_BUSY;
    }

    if (HAL_UART_Receive_DMA(huart, pData, Size) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    return ERR_OK;
}

/**
 * @brief I2C初始化
 */
error_code_t HAL_I2C_Init_All(void)
{
    /* 使能I2C时钟 */
    __HAL_RCC_I2C1_CLK_ENABLE();

    /* I2C1配置 */
    hi2c1.Instance = I2C1;
    hi2c1.Init.Timing = 0x2000090E;  // 100kHz @ 48MHz
    hi2c1.Init.OwnAddress1 = 0;
    hi2c1.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;
    hi2c1.Init.DualAddressMode = I2C_DUALADDRESS_DISABLE;
    hi2c1.Init.OwnAddress2 = 0;
    hi2c1.Init.OwnAddress2Masks = I2C_OA2_NOMASK;
    hi2c1.Init.GeneralCallMode = I2C_GENERALCALL_DISABLE;
    hi2c1.Init.NoStretchMode = I2C_NOSTRETCH_DISABLE;
    if (HAL_I2C_Init(&hi2c1) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 配置模拟滤波器 */
    if (HAL_I2CEx_ConfigAnalogFilter(&hi2c1, I2C_ANALOGFILTER_ENABLE) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 配置数字滤波器 */
    if (HAL_I2CEx_ConfigDigitalFilter(&hi2c1, 0) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    return ERR_OK;
}

/**
 * @brief 安全的I2C发送
 */
error_code_t HAL_I2C_Transmit_Safe(I2C_HandleTypeDef *hi2c, uint16_t DevAddress,
                                   uint8_t *pData, uint16_t Size, uint32_t Timeout)
{
    if (hi2c->State != HAL_I2C_STATE_READY) {
        return ERR_BUSY;
    }

    if (HAL_I2C_Master_Transmit(hi2c, DevAddress, pData, Size, Timeout) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    return ERR_OK;
}

/**
 * @brief 安全的I2C内存写入
 */
error_code_t HAL_I2C_Mem_Write_Safe(I2C_HandleTypeDef *hi2c, uint16_t DevAddress,
                                     uint16_t MemAddress, uint16_t MemAddSize,
                                     uint8_t *pData, uint16_t Size, uint32_t Timeout)
{
    if (hi2c->State != HAL_I2C_STATE_READY) {
        return ERR_BUSY;
    }

    if (HAL_I2C_Mem_Write(hi2c, DevAddress, MemAddress, MemAddSize, pData, Size, Timeout) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    return ERR_OK;
}

/**
 * @brief ADC初始化
 */
error_code_t HAL_ADC_Init_All(void)
{
    ADC_ChannelConfTypeDef sConfig = {0};

    /* 使能ADC时钟 */
    __HAL_RCC_ADC1_CLK_ENABLE();

    /* ADC配置 */
    hadc1.Instance = ADC1;
    hadc1.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
    hadc1.Init.Resolution = ADC_RESOLUTION_12B;
    hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT;
    hadc1.Init.ScanConvMode = ADC_SCAN_DIRECTION_FORWARD;
    hadc1.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
    hadc1.Init.LowPowerAutoWait = DISABLE;
    hadc1.Init.LowPowerAutoPowerOff = DISABLE;
    hadc1.Init.ContinuousConvMode = DISABLE;
    hadc1.Init.DiscontinuousConvMode = DISABLE;
    hadc1.Init.ExternalTrigConv = ADC_EXTERNALTRIGCONV_T7_TRGO;
    hadc1.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_RISING;
    hadc1.Init.DMAContinuousRequests = ENABLE;
    hadc1.Init.Overrun = ADC_OVR_DATA_OVERWRITTEN;
    if (HAL_ADC_Init(&hadc1) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 配置ADC通道 */
    sConfig.Channel = ADC_CHANNEL_0;  // PA0 - CH1
    sConfig.Rank = ADC_RANK_CHANNEL_NUMBER;
    sConfig.SamplingTime = ADC_SAMPLETIME_239CYCLES_5;
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    sConfig.Channel = ADC_CHANNEL_1;  // PA1 - CH2
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    sConfig.Channel = ADC_CHANNEL_2;  // PA2 - CH3
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    sConfig.Channel = ADC_CHANNEL_3;  // PA3 - CH4
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    sConfig.Channel = ADC_CHANNEL_4;  // PA4 - SWA
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    sConfig.Channel = ADC_CHANNEL_5;  // PA5 - SWB
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    sConfig.Channel = ADC_CHANNEL_10; // PC0 - VBAT
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    sConfig.Channel = ADC_CHANNEL_11; // PC1 - BIN (外接电源检测)
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    sConfig.Channel = ADC_CHANNEL_12; // PC2 - VRA
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    sConfig.Channel = ADC_CHANNEL_13; // PC3 - VRB
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 关联DMA */
    __HAL_LINKDMA(&hadc1, DMA_Handle, hdma_adc1);

    /* ADC校准 */
    if (HAL_ADCEx_Calibration_Start(&hadc1) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    return ERR_OK;
}

/**
 * @brief 定时器初始化
 */
error_code_t HAL_TIM_Init_All(void)
{
    TIM_ClockConfigTypeDef sClockSourceConfig = {0};
    TIM_MasterConfigTypeDef sMasterConfig = {0};
    TIM_OC_InitTypeDef sConfigOC = {0};

    /* 使能定时器时钟 */
    __HAL_RCC_TIM1_CLK_ENABLE();
    __HAL_RCC_TIM2_CLK_ENABLE();
    __HAL_RCC_TIM3_CLK_ENABLE();
    __HAL_RCC_TIM6_CLK_ENABLE();
    __HAL_RCC_TIM7_CLK_ENABLE();

    /* TIM1配置 - 蜂鸣器PWM */
    htim1.Instance = TIM1;
    htim1.Init.Prescaler = 47;          // 48MHz / 48 = 1MHz
    htim1.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim1.Init.Period = 999;            // 1MHz / 1000 = 1kHz (可调)
    htim1.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim1.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
    if (HAL_TIM_PWM_Init(&htim1) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* TIM1 PWM通道配置 */
    sConfigOC.OCMode = TIM_OCMODE_PWM1;
    sConfigOC.Pulse = 0;                // 初始占空比0%
    sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
    sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
    if (HAL_TIM_PWM_ConfigChannel(&htim1, &sConfigOC, TIM_CHANNEL_1) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* TIM3配置 - 震动电机PWM */
    htim3.Instance = TIM3;
    htim3.Init.Prescaler = 47;          // 48MHz / 48 = 1MHz
    htim3.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim3.Init.Period = 999;            // 1MHz / 1000 = 1kHz
    htim3.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim3.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
    if (HAL_TIM_PWM_Init(&htim3) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* TIM3 PWM通道配置 */
    sConfigOC.OCMode = TIM_OCMODE_PWM1;
    sConfigOC.Pulse = 0;                // 初始占空比0%
    sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
    sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
    if (HAL_TIM_PWM_ConfigChannel(&htim3, &sConfigOC, TIM_CHANNEL_1) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* TIM2配置 - 微秒定时器 */
    htim2.Instance = TIM2;
    htim2.Init.Prescaler = 47;  // 48MHz / 48 = 1MHz
    htim2.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim2.Init.Period = 0xFFFFFFFF;
    htim2.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim2.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
    if (HAL_TIM_Base_Init(&htim2) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
    if (HAL_TIM_ConfigClockSource(&htim2, &sClockSourceConfig) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* TIM6配置 - 任务调度定时器 */
    htim6.Instance = TIM6;
    htim6.Init.Prescaler = 47;  // 48MHz / 48 = 1MHz
    htim6.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim6.Init.Period = 999;    // 1MHz / 1000 = 1kHz
    htim6.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
    if (HAL_TIM_Base_Init(&htim6) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if (HAL_TIMEx_MasterConfigSynchronization(&htim6, &sMasterConfig) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* TIM7配置 - ADC触发定时器 */
    htim7.Instance = TIM7;
    htim7.Init.Prescaler = 47;   // 48MHz / 48 = 1MHz
    htim7.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim7.Init.Period = 999;     // 1MHz / 1000 = 1000Hz (1ms)
    htim7.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
    if (HAL_TIM_Base_Init(&htim7) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    sMasterConfig.MasterOutputTrigger = TIM_TRGO_UPDATE;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if (HAL_TIMEx_MasterConfigSynchronization(&htim7, &sMasterConfig) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 使能定时器中断 */
    HAL_NVIC_SetPriority(TIM6_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(TIM6_IRQn);

    HAL_NVIC_SetPriority(TIM7_IRQn, 4, 0);
    HAL_NVIC_EnableIRQ(TIM7_IRQn);

    return ERR_OK;
}

/**
 * @brief 启动所有定时器
 */
error_code_t HAL_TIM_Start_All(void)
{
    if (HAL_TIM_Base_Start(&htim2) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    if (HAL_TIM_Base_Start_IT(&htim6) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    if (HAL_TIM_Base_Start(&htim7) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    return ERR_OK;
}

/**
 * @brief 停止所有定时器
 */
error_code_t HAL_TIM_Stop_All(void)
{
    HAL_TIM_Base_Stop(&htim2);
    HAL_TIM_Base_Stop_IT(&htim6);
    HAL_TIM_Base_Stop(&htim7);

    return ERR_OK;
}

/**
 * @brief 启动ADC DMA
 */
error_code_t HAL_ADC_Start_DMA_Safe(void)
{
    if (hadc1.State != HAL_ADC_STATE_READY) {
        return ERR_BUSY;
    }

    extern uint16_t adc_buffer[ADC_CHANNELS];
    if (HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adc_buffer, ADC_CHANNELS) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    return ERR_OK;
}

/**
 * @brief 停止ADC DMA
 */
error_code_t HAL_ADC_Stop_DMA_Safe(void)
{
    if (HAL_ADC_Stop_DMA(&hadc1) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    return ERR_OK;
}

/**
 * @brief USB设备初始化
 */
error_code_t HAL_USB_Init(void)
{
    /* 使能USB时钟 */
    __HAL_RCC_USB_CLK_ENABLE();

    /* USB设备配置 */
    hpcd_USB_FS.Instance = USB;
    hpcd_USB_FS.Init.dev_endpoints = 8;
    hpcd_USB_FS.Init.speed = PCD_SPEED_FULL;
    hpcd_USB_FS.Init.phy_itface = PCD_PHY_EMBEDDED;
    hpcd_USB_FS.Init.low_power_enable = DISABLE;
    hpcd_USB_FS.Init.battery_charging_enable = DISABLE;

    if (HAL_PCD_Init(&hpcd_USB_FS) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 使能USB中断 */
    HAL_NVIC_SetPriority(USB_IRQn, 2, 0);
    HAL_NVIC_EnableIRQ(USB_IRQn);

    return ERR_OK;
}
