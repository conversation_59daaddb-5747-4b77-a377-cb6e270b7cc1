# TIM2配置指南 - CRSF定时器

## 🎯 配置目标

为CRSF协议配置TIM2作为精确定时器：
- **1MHz时钟频率** (1μs精度)
- **比较匹配中断** (TIM_IT_CC1)
- **32位计数器** (最大计数范围)
- **最高中断优先级**

## ⚙️ STM32CubeMX配置步骤

### 1. 启用TIM2
```
Pinout & Configuration → Timers → TIM2
☑ Activated (勾选激活)
```

### 2. 时钟源配置
```
Clock Source: Internal Clock
```

### 3. 通道配置
```
Channel1: Output Compare CH1 No Output
(用于比较匹配中断，不需要外部输出)
```

### 4. 基本参数设置
```
Parameter Settings → Basic Parameters:

Prescaler: 47                      # 48MHz/(47+1) = 1MHz
Counter Mode: Up                   # 向上计数
Counter Period: 0xFFFFFFFF         # 32位最大值 (4294967295)
auto-reload preload: Disable       # 不使用自动重载
Clock Division: No Division        # 不分频
```

### 5. 输出比较通道1设置
```
Parameter Settings → Output Compare Channel 1:

Mode: Output Compare No Output     # 仅中断，无输出
Pulse: 0                          # 初始比较值
Output polarity: High             # 极性(不重要)
Fast Mode: Disable                # 禁用快速模式
```

### 6. NVIC中断配置
```
NVIC Settings:
☑ TIM2 global interrupt: Enabled
Priority: 0                       # 最高优先级
Subpriority: 0
```

## 📋 当前IOC配置

### ✅ 已正确配置的参数：
```ini
# TIM2基本配置
TIM2.Channel-Output\ Compare1\ CH1=TIM_CHANNEL_1
TIM2.IPParameters=Channel-Output Compare1 CH1,Prescaler,Period,Pulse-Output Compare1 CH1
TIM2.Period=0xFFFFFFFF             # 32位最大值 ✅
TIM2.Prescaler=47                  # 1MHz时钟 ✅
TIM2.Pulse-Output\ Compare1\ CH1=0 # 初始比较值 ✅

# 时钟源配置
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT

# 中断优先级
NVIC.TIM2_IRQn=true:0:0            # 最高优先级 ✅
```

## 🔧 时钟计算验证

### 时钟链路：
```
HSE (8MHz) → PLL (×6) → SYSCLK (48MHz) → APB1 (48MHz) → TIM2CLK (48MHz)
```

### 定时器频率：
```
TIM2_Frequency = TIM2CLK / (Prescaler + 1)
TIM2_Frequency = 48MHz / (47 + 1) = 1MHz ✅
```

### 时间精度：
```
Time_Resolution = 1 / TIM2_Frequency = 1μs ✅
```

### 最大计数时间：
```
Max_Time = 0xFFFFFFFF / 1MHz = 4294.967295秒 ≈ 71.6分钟 ✅
```

## 💻 代码中的使用

### 1. 定时器启动
```c
void CLOCK_StartTimer(uint32_t us, timer_callback_t cb)
{
    timer_callback = cb;
    
    /* 获取当前计数值 */
    uint32_t current_count = __HAL_TIM_GET_COUNTER(&htim2);
    
    /* 设置比较值 */
    __HAL_TIM_SET_COMPARE(&htim2, TIM_CHANNEL_1, current_count + us);
    
    /* 清除中断标志并使能中断 */
    __HAL_TIM_CLEAR_FLAG(&htim2, TIM_FLAG_CC1);
    __HAL_TIM_ENABLE_IT(&htim2, TIM_IT_CC1);
}
```

### 2. 中断处理
```c
void TIM2_IRQHandler(void)
{
    if (__HAL_TIM_GET_FLAG(&htim2, TIM_FLAG_CC1) != RESET) {
        if (timer_callback) {
            uint32_t next_us = timer_callback();
            
            __HAL_TIM_CLEAR_FLAG(&htim2, TIM_FLAG_CC1);
            
            if (next_us) {
                /* 设置下次中断时间 */
                uint32_t current_ccr = __HAL_TIM_GET_COMPARE(&htim2, TIM_CHANNEL_1);
                __HAL_TIM_SET_COMPARE(&htim2, TIM_CHANNEL_1, current_ccr + next_us);
                return;
            }
        }
        CLOCK_StopTimer();
    }
}
```

### 3. 微秒计时函数
```c
uint32_t micros(void)
{
    return __HAL_TIM_GET_COUNTER(&htim2);
}
```

## 🎮 CRSF协议中的应用

### 启动CRSF定时器
```c
error_code_t CRSF_Protocol_Start(void)
{
    /* 启动定时器 - 4ms周期 */
    CLOCK_StartTimer(4000, CRSF_SerialCallback);
    
    crsf_state = CRSF_STATE_DATA0;
    protocol_running = true;
    
    return ERR_OK;
}
```

### CRSF状态机回调
```c
uint16_t CRSF_SerialCallback(void)
{
    switch (crsf_state) {
        case CRSF_STATE_DATA0:
            CLOCK_RunMixer();
            crsf_state = CRSF_STATE_DATA1;
            return mixer_runtime;        // 例如: 200μs
            
        case CRSF_STATE_DATA1:
            CRSF_SendRCPacket();
            crsf_state = CRSF_STATE_DATA0;
            return 4000 - mixer_runtime; // 例如: 3800μs
    }
}
```

## 🔍 调试和验证

### 1. 时钟频率验证
```c
void TIM2_FrequencyTest(void)
{
    uint32_t start = __HAL_TIM_GET_COUNTER(&htim2);
    HAL_Delay(1000);  // 1秒延时
    uint32_t end = __HAL_TIM_GET_COUNTER(&htim2);
    
    uint32_t counts = end - start;
    USB_CDC_Printf("TIM2 frequency: %d Hz\n", counts);
    // 应该显示约1000000 Hz (1MHz)
}
```

### 2. 中断精度测试
```c
void TIM2_InterruptTest(void)
{
    static uint32_t last_time = 0;
    uint32_t current_time = __HAL_TIM_GET_COUNTER(&htim2);
    uint32_t interval = current_time - last_time;
    
    USB_CDC_Printf("Interrupt interval: %dus\n", interval);
    last_time = current_time;
}
```

## ⚠️ 注意事项

### 1. 计数器溢出处理
- TIM2是32位计数器，溢出时间很长(71.6分钟)
- 正常使用中不需要考虑溢出问题
- 如需处理溢出，使用64位时间戳

### 2. 中断延迟
- TIM2中断必须是最高优先级
- 中断处理函数应尽可能短
- 避免在中断中执行耗时操作

### 3. 比较值设置
- 比较值必须大于当前计数值
- 建议至少预留10μs的处理时间
- 避免设置过小的间隔时间

## ✅ 配置验证清单

- ✅ **TIM2已激活**: Pinout中已选择
- ✅ **时钟源**: Internal Clock
- ✅ **通道1**: Output Compare CH1 No Output
- ✅ **分频系数**: 47 (1MHz时钟)
- ✅ **计数周期**: 0xFFFFFFFF (32位最大值)
- ✅ **中断优先级**: 0 (最高优先级)
- ✅ **中断使能**: TIM2 global interrupt enabled

---
*配置完成时间: 2025-07-30*
*时钟频率: 1MHz*
*精度: 1μs*
*状态: ✅ 已优化*
