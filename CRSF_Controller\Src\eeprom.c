/**
 * @file eeprom.c
 * @brief EEPROM存储驱动实现 (FT24C128A)
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "eeprom.h"
#include "hal_drivers.h"

/* 存储区域信息表 */
static const eeprom_area_info_t area_info_table[] = {
    {0,     1024,  "System"},      // 系统配置区
    {1024,  1024,  "Calibration"}, // 校准数据区
    {2048,  6144,  "Models"},      // 模型数据区
    {8192,  4096,  "Logs"},        // 日志数据区
    {12288, 4096,  "User"}         // 用户数据区
};

/* 私有变量 */
static bool eeprom_initialized = false;

/* 私有函数声明 */
static error_code_t EEPROM_WaitReady(uint32_t timeout_ms);
static error_code_t EEPROM_WritePageInternal(uint16_t addr, const uint8_t* data, uint16_t length);

/**
 * @brief EEPROM初始化
 */
error_code_t EEPROM_Init(void)
{
    if (eeprom_initialized) {
        return ERR_OK;
    }

    /* I2C已在HAL_I2C_Init_All()中初始化 */
    
    /* 测试EEPROM连接 */
    error_code_t result = EEPROM_Test();
    if (result != ERR_OK) {
        return result;
    }

    eeprom_initialized = true;
    return ERR_OK;
}

/**
 * @brief EEPROM连接测试
 */
error_code_t EEPROM_Test(void)
{
    uint8_t test_data = 0;
    
    /* 尝试读取第一个字节 */
    error_code_t result = EEPROM_ReadByte(0, &test_data);
    if (result != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }
    
    return ERR_OK;
}

/**
 * @brief 检查EEPROM是否就绪
 */
bool EEPROM_IsReady(void)
{
    return eeprom_initialized;
}

/**
 * @brief 读取单个字节
 */
error_code_t EEPROM_ReadByte(uint16_t addr, uint8_t* data)
{
    if (!eeprom_initialized || data == NULL) {
        return ERR_INVALID_PARAM;
    }
    
    if (addr >= EEPROM_TOTAL_SIZE) {
        return ERR_INVALID_PARAM;
    }
    
    /* 发送地址 */
    uint8_t addr_bytes[2] = {(addr >> 8) & 0xFF, addr & 0xFF};
    error_code_t result = HAL_I2C_Transmit_Safe(&hi2c2, EEPROM_I2C_ADDR << 1, addr_bytes, 2, 100);
    if (result != ERR_OK) {
        return result;
    }
    
    /* 读取数据 */
    uint8_t rx_data;
    result = HAL_I2C_Receive_Safe(&hi2c2, EEPROM_I2C_ADDR << 1, &rx_data, 1, 100);
    if (result != ERR_OK) {
        return result;
    }
    
    *data = rx_data;
    return ERR_OK;
}

/**
 * @brief 写入单个字节
 */
error_code_t EEPROM_WriteByte(uint16_t addr, uint8_t data)
{
    if (!eeprom_initialized) {
        return ERR_NOT_READY;
    }
    
    if (addr >= EEPROM_TOTAL_SIZE) {
        return ERR_INVALID_PARAM;
    }
    
    /* 准备数据 */
    uint8_t write_data[3] = {(addr >> 8) & 0xFF, addr & 0xFF, data};
    
    /* 写入数据 */
    error_code_t result = HAL_I2C_Transmit_Safe(&hi2c2, EEPROM_I2C_ADDR << 1, write_data, 3, 100);
    if (result != ERR_OK) {
        return result;
    }
    
    /* 等待写入完成 */
    return EEPROM_WaitReady(EEPROM_WRITE_CYCLE_TIME);
}

/**
 * @brief 读取多个字节
 */
error_code_t EEPROM_ReadBytes(uint16_t addr, uint8_t* data, uint16_t length)
{
    if (!eeprom_initialized || data == NULL || length == 0) {
        return ERR_INVALID_PARAM;
    }
    
    if (addr + length > EEPROM_TOTAL_SIZE) {
        return ERR_INVALID_PARAM;
    }
    
    /* 发送地址 */
    uint8_t addr_bytes[2] = {(addr >> 8) & 0xFF, addr & 0xFF};
    error_code_t result = HAL_I2C_Transmit_Safe(&hi2c2, EEPROM_I2C_ADDR << 1, addr_bytes, 2, 100);
    if (result != ERR_OK) {
        return result;
    }
    
    /* 读取数据 */
    return HAL_I2C_Receive_Safe(&hi2c2, EEPROM_I2C_ADDR << 1, data, length, 1000);
}

/**
 * @brief 写入多个字节
 */
error_code_t EEPROM_WriteBytes(uint16_t addr, const uint8_t* data, uint16_t length)
{
    if (!eeprom_initialized || data == NULL || length == 0) {
        return ERR_INVALID_PARAM;
    }
    
    if (addr + length > EEPROM_TOTAL_SIZE) {
        return ERR_INVALID_PARAM;
    }
    
    uint16_t written = 0;
    
    while (written < length) {
        /* 计算当前页内可写字节数 */
        uint16_t current_addr = addr + written;
        uint16_t page_offset = current_addr % EEPROM_PAGE_SIZE;
        uint16_t page_remaining = EEPROM_PAGE_SIZE - page_offset;
        uint16_t write_length = MIN(page_remaining, length - written);
        
        /* 写入当前页 */
        error_code_t result = EEPROM_WritePageInternal(current_addr, &data[written], write_length);
        if (result != ERR_OK) {
            return result;
        }
        
        written += write_length;
    }
    
    return ERR_OK;
}

/**
 * @brief 读取页数据
 */
error_code_t EEPROM_ReadPage(uint16_t page, uint8_t* data)
{
    if (page >= EEPROM_TOTAL_PAGES) {
        return ERR_INVALID_PARAM;
    }
    
    uint16_t addr = page * EEPROM_PAGE_SIZE;
    return EEPROM_ReadBytes(addr, data, EEPROM_PAGE_SIZE);
}

/**
 * @brief 写入页数据
 */
error_code_t EEPROM_WritePage(uint16_t page, const uint8_t* data)
{
    if (page >= EEPROM_TOTAL_PAGES) {
        return ERR_INVALID_PARAM;
    }
    
    uint16_t addr = page * EEPROM_PAGE_SIZE;
    return EEPROM_WritePageInternal(addr, data, EEPROM_PAGE_SIZE);
}

/**
 * @brief 加载系统配置
 */
error_code_t EEPROM_LoadSystemConfig(system_config_t* config)
{
    if (!config) {
        return ERR_INVALID_PARAM;
    }
    
    /* 读取配置数据 */
    error_code_t result = EEPROM_ReadArea(EEPROM_AREA_SYSTEM, 0, (uint8_t*)config, sizeof(system_config_t));
    if (result != ERR_OK) {
        return result;
    }
    
    /* 验证魔数和校验和 */
    if (config->magic != EEPROM_MAGIC_SYSTEM) {
        return ERR_INVALID_PARAM;
    }
    
    uint16_t calculated_checksum = EEPROM_CalculateChecksum((uint8_t*)config, sizeof(system_config_t) - 2);
    if (config->checksum != calculated_checksum) {
        return ERR_INVALID_PARAM;
    }
    
    return ERR_OK;
}

/**
 * @brief 保存系统配置
 */
error_code_t EEPROM_SaveSystemConfig(const system_config_t* config)
{
    if (!config) {
        return ERR_INVALID_PARAM;
    }
    
    system_config_t temp_config;
    memcpy(&temp_config, config, sizeof(system_config_t));
    
    /* 设置魔数和版本 */
    temp_config.magic = EEPROM_MAGIC_SYSTEM;
    temp_config.version = EEPROM_VERSION_SYSTEM;
    
    /* 计算校验和 */
    temp_config.checksum = EEPROM_CalculateChecksum((uint8_t*)&temp_config, sizeof(system_config_t) - 2);
    
    /* 写入配置数据 */
    return EEPROM_WriteArea(EEPROM_AREA_SYSTEM, 0, (uint8_t*)&temp_config, sizeof(system_config_t));
}

/**
 * @brief 计算校验和
 */
uint16_t EEPROM_CalculateChecksum(const uint8_t* data, uint16_t length)
{
    uint16_t checksum = 0;
    for (uint16_t i = 0; i < length; i++) {
        checksum += data[i];
    }
    return checksum;
}

/**
 * @brief 获取区域信息
 */
const eeprom_area_info_t* EEPROM_GetAreaInfo(eeprom_area_t area)
{
    if (area >= EEPROM_AREA_COUNT) {
        return NULL;
    }
    
    return &area_info_table[area];
}

/**
 * @brief 区域读取
 */
error_code_t EEPROM_ReadArea(eeprom_area_t area, uint16_t offset, uint8_t* data, uint16_t length)
{
    const eeprom_area_info_t* info = EEPROM_GetAreaInfo(area);
    if (!info || offset + length > info->size) {
        return ERR_INVALID_PARAM;
    }
    
    return EEPROM_ReadBytes(info->start_addr + offset, data, length);
}

/**
 * @brief 区域写入
 */
error_code_t EEPROM_WriteArea(eeprom_area_t area, uint16_t offset, const uint8_t* data, uint16_t length)
{
    const eeprom_area_info_t* info = EEPROM_GetAreaInfo(area);
    if (!info || offset + length > info->size) {
        return ERR_INVALID_PARAM;
    }
    
    return EEPROM_WriteBytes(info->start_addr + offset, data, length);
}

/**
 * @brief 等待EEPROM就绪
 */
static error_code_t EEPROM_WaitReady(uint32_t timeout_ms)
{
    uint32_t start_time = millis();
    
    while (millis() - start_time < timeout_ms) {
        /* 尝试发送设备地址，如果ACK则表示就绪 */
        if (HAL_I2C_IsDeviceReady(&hi2c2, EEPROM_I2C_ADDR << 1, 1, 1) == HAL_OK) {
            return ERR_OK;
        }
        delay_ms(1);
    }
    
    return ERR_TIMEOUT;
}

/**
 * @brief 页写入内部函数
 */
static error_code_t EEPROM_WritePageInternal(uint16_t addr, const uint8_t* data, uint16_t length)
{
    if (length > EEPROM_PAGE_SIZE) {
        return ERR_INVALID_PARAM;
    }
    
    /* 准备写入数据 */
    uint8_t write_buffer[EEPROM_PAGE_SIZE + 2];
    write_buffer[0] = (addr >> 8) & 0xFF;
    write_buffer[1] = addr & 0xFF;
    memcpy(&write_buffer[2], data, length);
    
    /* 写入数据 */
    error_code_t result = HAL_I2C_Transmit_Safe(&hi2c2, EEPROM_I2C_ADDR << 1, write_buffer, length + 2, 100);
    if (result != ERR_OK) {
        return result;
    }
    
    /* 等待写入完成 */
    return EEPROM_WaitReady(EEPROM_WRITE_CYCLE_TIME);
}
