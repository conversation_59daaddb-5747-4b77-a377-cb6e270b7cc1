/**
 * @file stm32f0xx_it.c
 * @brief 中断服务程序
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "stm32f0xx_it.h"
#include "hal_drivers.h"

/* 外部变量声明 */
extern TIM_HandleTypeDef htim6;
extern TIM_HandleTypeDef htim7;
extern UART_HandleTypeDef huart1;
extern UART_HandleTypeDef huart2;
extern ADC_HandleTypeDef hadc1;
extern DMA_HandleTypeDef hdma_usart1_rx;
extern DMA_HandleTypeDef hdma_usart1_tx;
extern DMA_HandleTypeDef hdma_adc1;
extern PCD_HandleTypeDef hpcd_USB_FS;

/**
 * @brief 不可屏蔽中断处理程序
 */
void NMI_Handler(void)
{
    /* 用户代码开始 NMI */
    
    /* 用户代码结束 NMI */
}

/**
 * @brief 硬件错误中断处理程序
 */
void HardFault_Handler(void)
{
    /* 用户代码开始 HardFault_IRQn */
    while (1) {
        /* 硬件错误，进入无限循环 */
    }
    /* 用户代码结束 HardFault_IRQn */
}

/**
 * @brief SVC中断处理程序
 */
void SVC_Handler(void)
{
    /* 用户代码开始 SVCall_IRQn */
    
    /* 用户代码结束 SVCall_IRQn */
}

/**
 * @brief PendSV中断处理程序
 */
void PendSV_Handler(void)
{
    /* 用户代码开始 PendSV_IRQn */
    
    /* 用户代码结束 PendSV_IRQn */
}

/**
 * @brief SysTick中断处理程序
 */
void SysTick_Handler(void)
{
    /* 用户代码开始 SysTick_IRQn */
    HAL_IncTick();
    /* 用户代码结束 SysTick_IRQn */
}

/**
 * @brief DMA1 Channel1中断处理程序 (ADC)
 */
void DMA1_Channel1_IRQHandler(void)
{
    /* 用户代码开始 DMA1_Channel1_IRQn */
    HAL_DMA_IRQHandler(&hdma_adc1);
    /* 用户代码结束 DMA1_Channel1_IRQn */
}

/**
 * @brief DMA1 Channel2_3中断处理程序 (USART1)
 */
void DMA1_Channel2_3_IRQHandler(void)
{
    /* 用户代码开始 DMA1_Channel2_3_IRQn */
    HAL_DMA_IRQHandler(&hdma_usart1_tx);
    HAL_DMA_IRQHandler(&hdma_usart1_rx);
    /* 用户代码结束 DMA1_Channel2_3_IRQn */
}

/**
 * @brief TIM6中断处理程序 (任务调度定时器)
 */
void TIM6_IRQHandler(void)
{
    /* 用户代码开始 TIM6_IRQn */
    HAL_TIM_IRQHandler(&htim6);
    /* 用户代码结束 TIM6_IRQn */
}

/**
 * @brief TIM7中断处理程序 (ADC触发定时器)
 */
void TIM7_IRQHandler(void)
{
    /* 用户代码开始 TIM7_IRQn */
    HAL_TIM_IRQHandler(&htim7);
    /* 用户代码结束 TIM7_IRQn */
}

/**
 * @brief USART1中断处理程序 (CRSF通信)
 */
void USART1_IRQHandler(void)
{
    /* 用户代码开始 USART1_IRQn */
    HAL_UART_IRQHandler(&huart1);
    /* 用户代码结束 USART1_IRQn */
}

/**
 * @brief USART2中断处理程序 (调试串口)
 */
void USART2_IRQHandler(void)
{
    /* 用户代码开始 USART2_IRQn */
    HAL_UART_IRQHandler(&huart2);
    /* 用户代码结束 USART2_IRQn */
}

/**
 * @brief I2C1错误中断处理程序
 */
void I2C1_IRQHandler(void)
{
    /* 用户代码开始 I2C1_IRQn */
    HAL_I2C_ER_IRQHandler(&hi2c1);
    /* 用户代码结束 I2C1_IRQn */
}

/**
 * @brief ADC1中断处理程序
 */
void ADC1_IRQHandler(void)
{
    /* 用户代码开始 ADC1_IRQn */
    HAL_ADC_IRQHandler(&hadc1);
    /* 用户代码结束 ADC1_IRQn */
}

/**
 * @brief USB中断处理程序
 */
void USB_IRQHandler(void)
{
    /* 用户代码开始 USB_IRQn */
    HAL_PCD_IRQHandler(&hpcd_USB_FS);
    /* 用户代码结束 USB_IRQn */
}
