/**
 * @file elrs_config.c
 * @brief ELRS配置管理实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "elrs_config.h"
#include "menu_system.h"

/* 全局变量定义 */
elrs_config_context_t elrs_config_context;

/* 包率选项 */
const char* elrs_packet_rate_options[] = {"25Hz", "50Hz", "100Hz", "150Hz", "200Hz", "250Hz", "333Hz", "500Hz"};
const uint8_t elrs_packet_rate_values[] = {0, 1, 2, 3, 4, 5, 6, 7};
const uint8_t elrs_packet_rate_count = sizeof(elrs_packet_rate_options) / sizeof(elrs_packet_rate_options[0]);

/* 发射功率选项 */
const char* elrs_tx_power_options[] = {"10mW", "25mW", "50mW", "100mW", "250mW", "500mW", "1000mW", "2000mW"};
const uint8_t elrs_tx_power_values[] = {0, 1, 2, 3, 4, 5, 6, 7};
const uint8_t elrs_tx_power_count = sizeof(elrs_tx_power_options) / sizeof(elrs_tx_power_options[0]);

/* 遥测比率选项 */
const char* elrs_telem_ratio_options[] = {"Off", "1:128", "1:64", "1:32", "1:16", "1:8", "1:4", "1:2"};
const uint8_t elrs_telem_ratio_values[] = {0, 1, 2, 3, 4, 5, 6, 7};
const uint8_t elrs_telem_ratio_count = sizeof(elrs_telem_ratio_options) / sizeof(elrs_telem_ratio_options[0]);

/* 开关模式选项 */
const char* elrs_switch_mode_options[] = {"Hybrid", "Wide"};
const uint8_t elrs_switch_mode_values[] = {0, 1};
const uint8_t elrs_switch_mode_count = sizeof(elrs_switch_mode_options) / sizeof(elrs_switch_mode_options[0]);

/* 私有变量 */
static bool elrs_config_initialized = false;

/* 私有函数声明 */
static void ELRS_Config_InitParams(void);
static void ELRS_Config_ProcessParamLoading(void);
static elrs_param_t* ELRS_Config_GetParamArray(uint8_t target);
static uint8_t* ELRS_Config_GetParamCount(uint8_t target);

/**
 * @brief ELRS配置初始化
 */
error_code_t ELRS_Config_Init(void)
{
    if (elrs_config_initialized) {
        return ERR_OK;
    }

    /* 初始化配置上下文 */
    memset(&elrs_config_context, 0, sizeof(elrs_config_context));
    
    /* 初始化设备状态 */
    elrs_config_context.device_status.tx_connected = false;
    elrs_config_context.device_status.rx_connected = false;
    strcpy(elrs_config_context.device_status.tx_name, "No TX");
    strcpy(elrs_config_context.device_status.rx_name, "No RX");
    
    /* 初始化参数 */
    ELRS_Config_InitParams();
    
    /* 设置超时时间 */
    elrs_config_context.param_request_timeout = ELRS_CONFIG_PARAM_TIMEOUT;
    
    elrs_config_initialized = true;
    return ERR_OK;
}

/**
 * @brief 启动ELRS配置
 */
error_code_t ELRS_Config_Start(void)
{
    if (!elrs_config_initialized) {
        return ERR_NOT_READY;
    }
    
    /* 开始设备检测 */
    ELRS_Config_CheckDevices();
    
    return ERR_OK;
}

/**
 * @brief 检查设备连接
 */
void ELRS_Config_CheckDevices(void)
{
    uint32_t current_time = millis();
    
    /* 定期ping设备 */
    if (current_time - elrs_config_context.device_status.last_ping_time >= ELRS_CONFIG_PING_INTERVAL) {
        ELRS_Config_PingDevices();
        elrs_config_context.device_status.last_ping_time = current_time;
    }
    
    /* 定期请求ELRS信息 */
    if (elrs_config_context.device_status.tx_connected &&
        current_time - elrs_config_context.device_status.last_info_time >= ELRS_CONFIG_INFO_INTERVAL) {
        ELRS_Config_RequestElrsInfo(ELRS_ADDRESS);
        elrs_config_context.device_status.last_info_time = current_time;
    }
}

/**
 * @brief Ping设备
 */
void ELRS_Config_PingDevices(void)
{
    CRSF_BroadcastPing();
}

/**
 * @brief 请求设备信息
 */
void ELRS_Config_RequestDeviceInfo(uint8_t target)
{
    CRSF_BroadcastPing();
}

/**
 * @brief 请求ELRS信息
 */
void ELRS_Config_RequestElrsInfo(uint8_t target)
{
    CRSF_GetElrsInfo(target);
}

/**
 * @brief 加载参数
 */
error_code_t ELRS_Config_LoadParams(uint8_t target)
{
    if (elrs_config_context.device_status.params_loading) {
        return ERR_BUSY;
    }
    
    elrs_config_context.device_status.params_loading = true;
    elrs_config_context.current_param_loading = 0;
    elrs_config_context.current_chunk_loading = 0;
    elrs_config_context.target_device = target;
    
    /* 开始加载第一个参数 */
    return ELRS_Config_ReadParam(target, 0, 0);
}

/**
 * @brief 读取参数
 */
error_code_t ELRS_Config_ReadParam(uint8_t target, uint8_t param_id, uint8_t chunk)
{
    elrs_config_context.last_param_request_time = millis();
    return CRSF_ReadParam(param_id, chunk, target);
}

/**
 * @brief 写入参数
 */
error_code_t ELRS_Config_WriteParam(uint8_t target, uint8_t param_id, uint8_t value)
{
    /* 更新本地参数值 */
    elrs_param_t* param = ELRS_Config_GetParam(target, param_id);
    if (param) {
        param->value = value;
        elrs_config_context.config_changed = true;
    }
    
    return CRSF_WriteParam(param_id, value, target);
}

/**
 * @brief 获取参数
 */
elrs_param_t* ELRS_Config_GetParam(uint8_t target, uint8_t param_id)
{
    elrs_param_t* params = ELRS_Config_GetParamArray(target);
    if (!params) {
        return NULL;
    }
    
    uint8_t param_count = *ELRS_Config_GetParamCount(target);
    if (param_id >= param_count) {
        return NULL;
    }
    
    return &params[param_id];
}

/**
 * @brief 根据名称查找参数
 */
elrs_param_t* ELRS_Config_FindParam(uint8_t target, const char* name)
{
    if (!name) {
        return NULL;
    }
    
    elrs_param_t* params = ELRS_Config_GetParamArray(target);
    if (!params) {
        return NULL;
    }
    
    uint8_t param_count = *ELRS_Config_GetParamCount(target);
    for (uint8_t i = 0; i < param_count; i++) {
        if (strcmp(params[i].name, name) == 0) {
            return &params[i];
        }
    }
    
    return NULL;
}

/**
 * @brief 设置参数值
 */
error_code_t ELRS_Config_SetParamValue(uint8_t target, uint8_t param_id, uint8_t value)
{
    elrs_param_t* param = ELRS_Config_GetParam(target, param_id);
    if (!param) {
        return ERR_INVALID_PARAM;
    }
    
    if (param->read_only) {
        return ERR_INVALID_PARAM;
    }
    
    if (value < param->min_value || value > param->max_value) {
        return ERR_INVALID_PARAM;
    }
    
    return ELRS_Config_WriteParam(target, param_id, value);
}

/**
 * @brief 获取参数值
 */
uint8_t ELRS_Config_GetParamValue(uint8_t target, uint8_t param_id)
{
    elrs_param_t* param = ELRS_Config_GetParam(target, param_id);
    if (!param) {
        return 0;
    }
    
    return param->value;
}

/**
 * @brief 检查TX是否连接
 */
bool ELRS_Config_IsTxConnected(void)
{
    return elrs_config_context.device_status.tx_connected;
}

/**
 * @brief 检查RX是否连接
 */
bool ELRS_Config_IsRxConnected(void)
{
    return elrs_config_context.device_status.rx_connected;
}

/**
 * @brief 获取TX名称
 */
const char* ELRS_Config_GetTxName(void)
{
    return elrs_config_context.device_status.tx_name;
}

/**
 * @brief 获取RX名称
 */
const char* ELRS_Config_GetRxName(void)
{
    return elrs_config_context.device_status.rx_name;
}

/**
 * @brief 设置包率
 */
error_code_t ELRS_Config_SetPacketRate(uint8_t rate_index)
{
    if (rate_index >= elrs_packet_rate_count) {
        return ERR_INVALID_PARAM;
    }
    
    /* 查找包率参数 */
    elrs_param_t* param = ELRS_Config_FindParam(ELRS_ADDRESS, "Packet Rate");
    if (!param) {
        return ERR_NOT_READY;
    }
    
    return ELRS_Config_SetParamValue(ELRS_ADDRESS, param->param_id, elrs_packet_rate_values[rate_index]);
}

/**
 * @brief 设置发射功率
 */
error_code_t ELRS_Config_SetTxPower(uint8_t power_index)
{
    if (power_index >= elrs_tx_power_count) {
        return ERR_INVALID_PARAM;
    }
    
    /* 查找功率参数 */
    elrs_param_t* param = ELRS_Config_FindParam(ELRS_ADDRESS, "TX Power");
    if (!param) {
        return ERR_NOT_READY;
    }
    
    return ELRS_Config_SetParamValue(ELRS_ADDRESS, param->param_id, elrs_tx_power_values[power_index]);
}

/**
 * @brief 设置遥测比率
 */
error_code_t ELRS_Config_SetTelemRatio(uint8_t ratio_index)
{
    if (ratio_index >= elrs_telem_ratio_count) {
        return ERR_INVALID_PARAM;
    }
    
    /* 查找遥测比率参数 */
    elrs_param_t* param = ELRS_Config_FindParam(ELRS_ADDRESS, "Telem Ratio");
    if (!param) {
        return ERR_NOT_READY;
    }
    
    return ELRS_Config_SetParamValue(ELRS_ADDRESS, param->param_id, elrs_telem_ratio_values[ratio_index]);
}

/**
 * @brief 处理设备信息事件
 */
void ELRS_Config_OnDeviceInfo(crsf_device_t* device)
{
    if (!device) {
        return;
    }
    
    if (device->address == ELRS_ADDRESS) {
        /* TX模块 */
        elrs_config_context.device_status.tx_connected = true;
        strncpy(elrs_config_context.device_status.tx_name, device->name, 
                sizeof(elrs_config_context.device_status.tx_name) - 1);
        elrs_config_context.device_status.tx_serial = device->serial_number;
        elrs_config_context.device_status.tx_total_params = device->number_of_params;
        
        /* 更新菜单显示 */
        Menu_SetTxConnected(true, device->name);
        
        /* 开始加载参数 */
        if (!elrs_config_context.device_status.params_loading) {
            ELRS_Config_LoadParams(ELRS_ADDRESS);
        }
    } else if (device->address == ELRS_RX_ADDRESS) {
        /* RX模块 */
        elrs_config_context.device_status.rx_connected = true;
        strncpy(elrs_config_context.device_status.rx_name, device->name, 
                sizeof(elrs_config_context.device_status.rx_name) - 1);
        elrs_config_context.device_status.rx_serial = device->serial_number;
        
        /* 更新菜单显示 */
        Menu_SetRxConnected(true, device->name);
    }
}

/**
 * @brief ELRS配置任务
 */
void ELRS_Config_Task(void* parameters)
{
    (void)parameters;
    
    /* 检查设备连接 */
    ELRS_Config_CheckDevices();
    
    /* 处理参数加载 */
    ELRS_Config_ProcessParamLoading();
    
    /* 处理超时 */
    ELRS_Config_ProcessTimeouts();
    
    /* 更新设备状态 */
    ELRS_Config_UpdateDeviceStatus();
}

/**
 * @brief 初始化参数
 */
static void ELRS_Config_InitParams(void)
{
    /* 清空参数数组 */
    memset(elrs_config_context.tx_params, 0, sizeof(elrs_config_context.tx_params));
    memset(elrs_config_context.rx_params, 0, sizeof(elrs_config_context.rx_params));
}

/**
 * @brief 获取参数数组
 */
static elrs_param_t* ELRS_Config_GetParamArray(uint8_t target)
{
    if (target == ELRS_ADDRESS) {
        return elrs_config_context.tx_params;
    } else if (target == ELRS_RX_ADDRESS) {
        return elrs_config_context.rx_params;
    }
    
    return NULL;
}

/**
 * @brief 获取参数数量指针
 */
static uint8_t* ELRS_Config_GetParamCount(uint8_t target)
{
    if (target == ELRS_ADDRESS) {
        return &elrs_config_context.device_status.tx_params_loaded;
    } else if (target == ELRS_RX_ADDRESS) {
        return &elrs_config_context.device_status.rx_params_loaded;
    }
    
    return NULL;
}
