# CRSF Controller 快速入门指南

## 🚀 **5分钟快速上手**

### 第一步：获取代码
```bash
git clone <repository_url>
cd CRSF_Controller
```

### 第二步：硬件连接
```
最小系统连接：
- STM32F072CBT6开发板
- PA9/PA10连接CRSF模块 (UART1)
- PA0-PA3连接摇杆ADC
- PD0-PD4连接按键 (上拉)
- PB10/PB11连接OLED (I2C)
- PA11/PA12连接USB
```

### 第三步：编译烧录
```bash
# 编译
make clean
make all

# 烧录 (使用ST-Link)
make flash

# 或使用OpenOCD
openocd -f interface/stlink.cfg -f target/stm32f0x.cfg -c "program build/CRSF_Controller.elf verify reset exit"
```

### 第四步：功能验证
```
1. 上电后LED4闪烁 (系统运行)
2. USB连接电脑，识别为虚拟串口
3. 串口工具连接，查看调试信息
4. 摇杆操作，观察CRSF输出
5. 按键操作，进入菜单系统
```

## 📋 **常用配置**

### 修改CRSF波特率
```c
// 在hal_drivers.c中修改
huart1.Init.BaudRate = 420000;  // 420K波特率
```

### 修改ADC通道映射
```c
// 在mixer.c中修改
mixer_inputs[MIXER_INPUT_CH1] = ADC_Input_GetCalibratedValue(ADC_CH1);
```

### 修改按键映射
```c
// 在button_input.c中修改按键引脚
#define KEY1_PIN    GPIO_PIN_0
#define KEY1_PORT   GPIOD
```

## 🔧 **调试技巧**

### 查看系统状态
```c
// 通过USB串口查看
USB_CDC_Printf("System OK, CPU: %d%%, Free RAM: %dKB\n", cpu_usage, free_ram);
```

### 监控CRSF时序
```c
// 添加时序监控代码
static uint32_t last_time = 0;
uint32_t current_time = micros();
uint32_t period = current_time - last_time;
if (abs(period - 4000) > 50) {
    USB_CDC_Printf("CRSF timing error: %dus\n", period);
}
```

### 检查ADC数据
```c
// 输出ADC原始值
for (int i = 0; i < 4; i++) {
    uint16_t raw = ADC_Input_GetRawValue(i);
    uint16_t cal = ADC_Input_GetCalibratedValue(i);
    USB_CDC_Printf("CH%d: raw=%d, cal=%d\n", i+1, raw, cal);
}
```

## ⚠️ **注意事项**

1. **时钟配置**: 确保系统时钟为48MHz
2. **引脚冲突**: 检查引脚是否与其他功能冲突
3. **电源稳定**: 确保3.3V电源稳定
4. **接地良好**: 所有模块共地
5. **信号完整性**: 高速信号线要短且屏蔽

## 🎯 **下一步**

- 阅读完整的[移植说明书](PORTING_GUIDE.md)
- 查看[架构设计文档](DEVIATION_ARCHITECTURE.md)
- 了解[USB功能说明](USB_CDC_GUIDE.md)
- 学习[音效系统](SOUND_MIGRATION.md)

## 📞 **技术支持**

如果遇到问题，请：
1. 检查硬件连接
2. 查看编译错误信息
3. 使用调试器单步调试
4. 查看相关文档
5. 提交Issue描述问题

祝您使用愉快！🎮
