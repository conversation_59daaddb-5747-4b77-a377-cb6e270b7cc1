#MicroXplorer Configuration settings - do not modify
ADC.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_0
ADC.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_1
ADC.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_2
ADC.Channel-3\#ChannelRegularConversion=ADC_CHANNEL_3
ADC.Channel-4\#ChannelRegularConversion=ADC_CHANNEL_4
ADC.Channel-5\#ChannelRegularConversion=ADC_CHANNEL_5
ADC.Channel-6\#ChannelRegularConversion=ADC_CHANNEL_6
ADC.Channel-7\#ChannelRegularConversion=ADC_CHANNEL_7
ADC.Channel-8\#ChannelRegularConversion=ADC_CHANNEL_8
ADC.Channel-9\#ChannelRegularConversion=ADC_CHANNEL_10
ADC.Channel-10\#ChannelRegularConversion=ADC_CHANNEL_11
ADC.Channel-11\#ChannelRegularConversion=ADC_CHANNEL_12
ADC.Channel-12\#ChannelRegularConversion=ADC_CHANNEL_13
ADC.ClockPrescaler=ADC_CLOCK_SYNC_PCLK_DIV4
ADC.ContinuousConvMode=ENABLE
ADC.DMAContinuousRequests=ENABLE
ADC.DataAlign=ADC_DATAALIGN_RIGHT
ADC.DiscontinuousConvMode=DISABLE
ADC.EOCSelection=ADC_EOC_SEQ_CONV
ADC.ExternalTrigConv=ADC_EXTERNALTRIGCONV_T15_TRGO
ADC.ExternalTrigConvEdge=ADC_EXTERNALTRIGCONVEDGE_RISING
ADC.IPParameters=ClockPrescaler,Resolution,DataAlign,ScanConvMode,ContinuousConvMode,DiscontinuousConvMode,ExternalTrigConvEdge,ExternalTrigConv,DMAContinuousRequests,EOCSelection,NbrOfConversion,InjectedNbrOfConversion,Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,Rank-3\#ChannelRegularConversion,Channel-3\#ChannelRegularConversion,SamplingTime-3\#ChannelRegularConversion,Rank-4\#ChannelRegularConversion,Channel-4\#ChannelRegularConversion,SamplingTime-4\#ChannelRegularConversion,Rank-5\#ChannelRegularConversion,Channel-5\#ChannelRegularConversion,SamplingTime-5\#ChannelRegularConversion,Rank-6\#ChannelRegularConversion,Channel-6\#ChannelRegularConversion,SamplingTime-6\#ChannelRegularConversion,Rank-7\#ChannelRegularConversion,Channel-7\#ChannelRegularConversion,SamplingTime-7\#ChannelRegularConversion,Rank-8\#ChannelRegularConversion,Channel-8\#ChannelRegularConversion,SamplingTime-8\#ChannelRegularConversion,Rank-9\#ChannelRegularConversion,Channel-9\#ChannelRegularConversion,SamplingTime-9\#ChannelRegularConversion,Rank-10\#ChannelRegularConversion,Channel-10\#ChannelRegularConversion,SamplingTime-10\#ChannelRegularConversion,Rank-11\#ChannelRegularConversion,Channel-11\#ChannelRegularConversion,SamplingTime-11\#ChannelRegularConversion,Rank-12\#ChannelRegularConversion,Channel-12\#ChannelRegularConversion,SamplingTime-12\#ChannelRegularConversion,NbrOfConversionFlag
ADC.InjectedNbrOfConversion=0
ADC.NbrOfConversion=13
ADC.NbrOfConversionFlag=1
ADC.Rank-0\#ChannelRegularConversion=1
ADC.Rank-1\#ChannelRegularConversion=2
ADC.Rank-2\#ChannelRegularConversion=3
ADC.Rank-3\#ChannelRegularConversion=4
ADC.Rank-4\#ChannelRegularConversion=5
ADC.Rank-5\#ChannelRegularConversion=6
ADC.Rank-6\#ChannelRegularConversion=7
ADC.Rank-7\#ChannelRegularConversion=8
ADC.Rank-8\#ChannelRegularConversion=9
ADC.Rank-9\#ChannelRegularConversion=10
ADC.Rank-10\#ChannelRegularConversion=11
ADC.Rank-11\#ChannelRegularConversion=12
ADC.Rank-12\#ChannelRegularConversion=13
ADC.Resolution=ADC_RESOLUTION_12B
ADC.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_239CYCLES_5
ADC.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_239CYCLES_5
ADC.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_239CYCLES_5
ADC.SamplingTime-3\#ChannelRegularConversion=ADC_SAMPLETIME_239CYCLES_5
ADC.SamplingTime-4\#ChannelRegularConversion=ADC_SAMPLETIME_239CYCLES_5
ADC.SamplingTime-5\#ChannelRegularConversion=ADC_SAMPLETIME_239CYCLES_5
ADC.SamplingTime-6\#ChannelRegularConversion=ADC_SAMPLETIME_239CYCLES_5
ADC.SamplingTime-7\#ChannelRegularConversion=ADC_SAMPLETIME_239CYCLES_5
ADC.SamplingTime-8\#ChannelRegularConversion=ADC_SAMPLETIME_239CYCLES_5
ADC.SamplingTime-9\#ChannelRegularConversion=ADC_SAMPLETIME_239CYCLES_5
ADC.SamplingTime-10\#ChannelRegularConversion=ADC_SAMPLETIME_239CYCLES_5
ADC.SamplingTime-11\#ChannelRegularConversion=ADC_SAMPLETIME_239CYCLES_5
ADC.SamplingTime-12\#ChannelRegularConversion=ADC_SAMPLETIME_239CYCLES_5
ADC.ScanConvMode=ADC_SCAN_ENABLE
CAD.formats=
CAD.pinconfig=
CAD.provider=
CRC.DefaultInitValueUse=DEFAULT_INIT_VALUE_ENABLE
CRC.DefaultPolynomialUse=DEFAULT_POLYNOMIAL_ENABLE
CRC.GeneratingPolynomial=7
CRC.IPParameters=DefaultPolynomialUse,DefaultInitValueUse,GeneratingPolynomial
IWDG.IPParameters=Prescaler,Reload
IWDG.Prescaler=IWDG_PRESCALER_32
IWDG.Reload=2312
Dma.ADC.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC.0.Instance=DMA1_Channel1
Dma.ADC.0.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC.0.MemInc=DMA_MINC_ENABLE
Dma.ADC.0.Mode=DMA_CIRCULAR
Dma.ADC.0.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC.0.PeriphInc=DMA_PINC_DISABLE
Dma.ADC.0.Priority=DMA_PRIORITY_HIGH
Dma.ADC.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.I2C2_RX.4.Direction=DMA_PERIPH_TO_MEMORY
Dma.I2C2_RX.4.Instance=DMA1_Channel5
Dma.I2C2_RX.4.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.I2C2_RX.4.MemInc=DMA_MINC_ENABLE
Dma.I2C2_RX.4.Mode=DMA_NORMAL
Dma.I2C2_RX.4.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.I2C2_RX.4.PeriphInc=DMA_PINC_DISABLE
Dma.I2C2_RX.4.Priority=DMA_PRIORITY_LOW
Dma.I2C2_RX.4.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.I2C2_TX.3.Direction=DMA_MEMORY_TO_PERIPH
Dma.I2C2_TX.3.Instance=DMA1_Channel4
Dma.I2C2_TX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.I2C2_TX.3.MemInc=DMA_MINC_ENABLE
Dma.I2C2_TX.3.Mode=DMA_NORMAL
Dma.I2C2_TX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.I2C2_TX.3.PeriphInc=DMA_PINC_DISABLE
Dma.I2C2_TX.3.Priority=DMA_PRIORITY_LOW
Dma.I2C2_TX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.Request0=ADC
Dma.Request1=USART1_TX
Dma.Request2=USART1_RX
Dma.Request3=I2C2_TX
Dma.Request4=I2C2_RX
Dma.RequestsNb=5
Dma.USART1_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.2.Instance=DMA1_Channel3
Dma.USART1_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.2.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.2.Mode=DMA_CIRCULAR
Dma.USART1_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.2.Priority=DMA_PRIORITY_MEDIUM
Dma.USART1_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.USART1_TX.1.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART1_TX.1.Instance=DMA1_Channel2
Dma.USART1_TX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_TX.1.MemInc=DMA_MINC_ENABLE
Dma.USART1_TX.1.Mode=DMA_NORMAL
Dma.USART1_TX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_TX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_TX.1.Priority=DMA_PRIORITY_MEDIUM
Dma.USART1_TX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority

File.Version=6
GPIO.groupedBy=Group By Peripherals

I2C2.I2C_Mode=I2C_Fast
I2C2.IPParameters=I2C_Mode,Timing
I2C2.Timing=0x2000090E

KeepUserPlacement=false
Mcu.CPN=STM32F072VBT6
Mcu.Family=STM32F0
Mcu.IP0=ADC
Mcu.IP1=CRC
Mcu.IP10=TIM1
Mcu.IP11=TIM2
Mcu.IP12=TIM3
Mcu.IP13=TIM15
Mcu.IP14=USART1
Mcu.IP15=USB
Mcu.IP16=USB_DEVICE


Mcu.IP2=DMA
Mcu.IP3=I2C2
Mcu.IP4=IWDG
Mcu.IP5=NVIC
Mcu.IP6=PWR
Mcu.IP7=RCC
Mcu.IP8=SYS
Mcu.IP9=TIM1
Mcu.IPNb=18
Mcu.Name=STM32F072V(8-B)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PC14-OSC32_IN
Mcu.Pin1=PC15-OSC32_OUT
Mcu.Pin2=PF0-OSC_IN
Mcu.Pin3=PF1-OSC_OUT
Mcu.Pin4=PA2
Mcu.Pin5=PA3
Mcu.Pin6=PA4
Mcu.Pin7=PA5
Mcu.Pin8=PA6
Mcu.Pin9=PA7
Mcu.Pin10=PC4
Mcu.Pin11=PC5
Mcu.Pin12=PB0
Mcu.Pin13=PB1
Mcu.Pin14=PB2
Mcu.Pin15=PE7
Mcu.Pin16=PE8
Mcu.Pin17=PE9
Mcu.Pin18=PE10
Mcu.Pin19=PE11
Mcu.Pin20=PE12
Mcu.Pin21=PE13
Mcu.Pin22=PE14
Mcu.Pin23=PE15
Mcu.Pin24=PB10
Mcu.Pin25=PB11
Mcu.PinsNb=26
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F072VBTx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.DMA1_Channel1_IRQn=true\:5\:0\:true\:false\:true\:false\:true\:true
NVIC.DMA1_Channel2_3_IRQn=true\:4\:0\:true\:false\:true\:false\:true\:true
NVIC.DMA1_Channel4_5_6_7_IRQn=true\:6\:0\:true\:false\:true\:false\:true\:true

NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.I2C2_IRQn=true\:6\:0\:true\:false\:true\:true\:true\:true
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SVC_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:true
NVIC.SysTick_IRQn=true\:0\:0\:true\:false\:true\:true\:true\:false
NVIC.USART1_IRQn=true\:4\:0\:true\:false\:true\:true\:true\:true
NVIC.EXTI0_1_IRQn=true\:1\:0\:true\:false\:true\:true\:true\:true
NVIC.EXTI2_3_IRQn=true\:3\:0\:true\:false\:true\:true\:true\:true
NVIC.EXTI4_15_IRQn=true\:2\:0\:true\:false\:true\:true\:true\:true

NVIC.TIM2_IRQn=true\:0\:0\:true\:false\:true\:true\:true\:true
NVIC.TIM15_IRQn=true\:5\:0\:true\:false\:true\:true\:true\:true
NVIC.USB_IRQn=true\:7\:0\:true\:false\:true\:false\:true\:true

PA0.GPIOParameters=GPIO_Label
PA0.GPIO_Label=ADC_CH1
PA0.Locked=true
PA0.Mode=IN0
PA0.Signal=ADC_IN0
PA1.GPIOParameters=GPIO_Label
PA1.GPIO_Label=ADC_CH2
PA1.Locked=true
PA1.Mode=IN1
PA1.Signal=ADC_IN1
PA2.GPIOParameters=GPIO_Label
PA2.GPIO_Label=ADC_CH3
PA2.Locked=true
PA2.Mode=IN2
PA2.Signal=ADC_IN2
PA3.GPIOParameters=GPIO_Label
PA3.GPIO_Label=ADC_CH4
PA3.Locked=true
PA3.Mode=IN3
PA3.Signal=ADC_IN3
PC0.GPIOParameters=GPIO_Label
PC0.GPIO_Label=ADC_CH10
PC0.Locked=true
PC0.Mode=IN10
PC0.Signal=ADC_IN10
PC1.GPIOParameters=GPIO_Label
PC1.GPIO_Label=ADC_CH11
PC1.Locked=true
PC1.Mode=IN11
PC1.Signal=ADC_IN11
PC2.GPIOParameters=GPIO_Label
PC2.GPIO_Label=ADC_CH12
PC2.Locked=true
PC2.Mode=IN12
PC2.Signal=ADC_IN12
PC3.GPIOParameters=GPIO_Label
PC3.GPIO_Label=ADC_CH13
PC3.Locked=true
PC3.Mode=IN13
PC3.Signal=ADC_IN13
PA4.GPIOParameters=GPIO_Label
PA4.GPIO_Label=ADC_SWA
PA4.Locked=true
PA4.Mode=IN4
PA4.Signal=ADC_IN4
PA5.GPIOParameters=GPIO_Label
PA5.GPIO_Label=ADC_SWB
PA5.Locked=true
PA5.Mode=IN5
PA5.Signal=ADC_IN5
PA6.GPIOParameters=GPIO_Label
PA6.GPIO_Label=ADC_VRA
PA6.Locked=true
PA6.Mode=IN6
PA6.Signal=ADC_IN6
PA7.GPIOParameters=GPIO_Label
PA7.GPIO_Label=ADC_VRB
PA7.Locked=true
PA7.Mode=IN7
PA7.Signal=ADC_IN7

PB0.GPIOParameters=GPIO_Label
PB0.GPIO_Label=ADC_VBAT
PB0.Locked=true
PB0.Mode=IN8
PB0.Signal=ADC_IN8
PB1.GPIOParameters=GPIO_Label,GPIO_PuPd,GPIO_ModeDefaultEXTI
PB1.GPIO_Label=EXTI_MIXER
PB1.GPIO_PuPd=GPIO_PULLUP
PB1.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PB1.Locked=true
PB1.Signal=GPXTI1
PB2.GPIOParameters=GPIO_Label,GPIO_PuPd,GPIO_ModeDefaultEXTI
PB2.GPIO_Label=EXTI_SOUND
PB2.GPIO_PuPd=GPIO_PULLUP
PB2.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PB2.Locked=true
PB2.Signal=GPXTI2
PB3.GPIOParameters=GPIO_Label,GPIO_PuPd,GPIO_ModeDefaultEXTI
PB3.GPIO_Label=EXTI_BTN1
PB3.GPIO_PuPd=GPIO_PULLUP
PB3.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PB3.Locked=true
PB3.Signal=GPXTI3
PB4.GPIOParameters=GPIO_Label,GPIO_PuPd,GPIO_ModeDefaultEXTI
PB4.GPIO_Label=EXTI_BTN2
PB4.GPIO_PuPd=GPIO_PULLUP
PB4.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PB4.Locked=true
PB4.Signal=GPXTI4
PB6.GPIOParameters=GPIO_Label
PB6.GPIO_Label=USART1_TX
PB6.Locked=true
PB6.Mode=Asynchronous
PB6.Signal=USART1_TX
PB7.GPIOParameters=GPIO_Label
PB7.GPIO_Label=USART1_RX
PB7.Locked=true
PB7.Mode=Asynchronous
PB7.Signal=USART1_RX
PB8.GPIOParameters=GPIO_Label
PB8.GPIO_Label=LED4
PB8.Locked=true
PB8.Signal=GPIO_Output
PB10.GPIOParameters=GPIO_Label
PB10.GPIO_Label=I2C_SCL
PB10.Locked=true
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.GPIOParameters=GPIO_Label
PB11.GPIO_Label=I2C_SDA
PB11.Locked=true
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PE7.GPIOParameters=GPIO_Label
PE7.GPIO_Label=GPIO_EXT7
PE7.Locked=true
PE7.Signal=GPIO_Output
PE8.GPIOParameters=GPIO_Label
PE8.GPIO_Label=GPIO_EXT8
PE8.Locked=true
PE8.Signal=GPIO_Output
PE9.GPIOParameters=GPIO_Label
PE9.GPIO_Label=GPIO_EXT9
PE9.Locked=true
PE9.Signal=GPIO_Output
PE10.GPIOParameters=GPIO_Label
PE10.GPIO_Label=GPIO_EXT10
PE10.Locked=true
PE10.Signal=GPIO_Output
PE11.GPIOParameters=GPIO_Label
PE11.GPIO_Label=GPIO_EXT11
PE11.Locked=true
PE11.Signal=GPIO_Output
PE12.GPIOParameters=GPIO_Label
PE12.GPIO_Label=GPIO_EXT12
PE12.Locked=true
PE12.Signal=GPIO_Output
PE13.GPIOParameters=GPIO_Label
PE13.GPIO_Label=GPIO_EXT13
PE13.Locked=true
PE13.Signal=GPIO_Output
PE14.GPIOParameters=GPIO_Label
PE14.GPIO_Label=GPIO_EXT14
PE14.Locked=true
PE14.Signal=GPIO_Output
PE15.GPIOParameters=GPIO_Label
PE15.GPIO_Label=GPIO_EXT15
PE15.Locked=true
PE15.Signal=GPIO_Output

PC4.GPIOParameters=GPIO_Label
PC4.GPIO_Label=LED5
PC4.Locked=true
PC4.Signal=GPIO_Output
PC5.GPIOParameters=GPIO_Label
PC5.GPIO_Label=ADC_VBAT
PC5.Locked=true
PC5.Mode=IN15
PC5.Signal=ADC_IN15
PC6.GPIOParameters=GPIO_Label,GPIO_PuPd
PC6.GPIO_Label=KEY3
PC6.GPIO_PuPd=GPIO_PULLUP
PC6.Locked=true
PC6.Signal=GPIO_Input
PC7.GPIOParameters=GPIO_Label,GPIO_PuPd
PC7.GPIO_Label=KEY2
PC7.GPIO_PuPd=GPIO_PULLUP
PC7.Locked=true
PC7.Signal=GPIO_Input
PC8.GPIOParameters=GPIO_Label,GPIO_PuPd
PC8.GPIO_Label=KEY1
PC8.GPIO_PuPd=GPIO_PULLUP
PC8.Locked=true
PC8.Signal=GPIO_Input
PC9.GPIOParameters=GPIO_Label,GPIO_PuPd
PC9.GPIO_Label=KEY0
PC9.GPIO_PuPd=GPIO_PULLUP
PC9.Locked=true
PC9.Signal=GPIO_Input
PD11.GPIOParameters=GPIO_Label
PD11.GPIO_Label=PWR_SW
PD11.Locked=true
PD11.Signal=GPIO_Output
PD12.GPIOParameters=GPIO_Label
PD12.GPIO_Label=PWR_OFF
PD12.Locked=true
PD12.Signal=GPIO_Output
PD13.GPIOParameters=GPIO_Label,GPIO_PuPd
PD13.GPIO_Label=STDBY
PD13.GPIO_PuPd=GPIO_PULLUP
PD13.Locked=true
PD13.Signal=GPIO_Input
PD14.GPIOParameters=GPIO_Label,GPIO_PuPd
PD14.GPIO_Label=KEY5
PD14.GPIO_PuPd=GPIO_PULLUP
PD14.Locked=true
PD14.Signal=GPIO_Input
PD15.GPIOParameters=GPIO_Label,GPIO_PuPd
PD15.GPIO_Label=KEY4
PD15.GPIO_PuPd=GPIO_PULLUP
PD15.Locked=true
PD15.Signal=GPIO_Input
PA8.GPIOParameters=GPIO_Label
PA8.GPIO_Label=TIM1_CH1
PA8.Locked=true
PA8.Signal=S_TIM1_CH1
PA11.GPIOParameters=GPIO_Label
PA11.GPIO_Label=USB_DM
PA11.Locked=true
PA11.Mode=Device
PA11.Signal=USB_DM
PA12.GPIOParameters=GPIO_Label
PA12.GPIO_Label=USB_DP
PA12.Locked=true
PA12.Mode=Device
PA12.Signal=USB_DP
PA13.GPIOParameters=GPIO_Label
PA13.GPIO_Label=SYS_SWDIO
PA13.Locked=true
PA13.Mode=Serial_Wire
PA13.Signal=SYS_SWDIO
PE3.GPIOParameters=GPIO_Label
PE3.GPIO_Label=VIBRATOR_PWM
PE3.Locked=true
PE3.Signal=S_TIM3_CH1

PC14-OSC32_IN.Locked=true
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Locked=true
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PF0-OSC_IN.GPIOParameters=GPIO_Label
PF0-OSC_IN.GPIO_Label=MCU_OSC_IN
PF0-OSC_IN.Locked=true
PF0-OSC_IN.Mode=HSE-External-Oscillator
PF0-OSC_IN.Signal=RCC_OSC_IN
PF1-OSC_OUT.GPIOParameters=GPIO_Label
PF1-OSC_OUT.GPIO_Label=MCU_OSC_OUT
PF1-OSC_OUT.Locked=true
PF1-OSC_OUT.Mode=HSE-External-Oscillator
PF1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F072VBTx
ProjectManager.FirmwarePackage=STM32Cube FW_F0 V1.11.5
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=CRSF_Controller.ioc
ProjectManager.ProjectName=CRSF_Controller
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=Makefile
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false-HAL-true,2-MX_DMA_Init-DMA-false-HAL-true,3-MX_ADC_Init-ADC-false-HAL-true,4-MX_CRC_Init-CRC-false-HAL-true,5-MX_I2C2_Init-I2C2-false-HAL-true,6-MX_TIM1_Init-TIM1-false-HAL-true,7-MX_TIM2_Init-TIM2-false-HAL-true,8-MX_TIM3_Init-TIM3-false-HAL-true,9-MX_TIM15_Init-TIM15-false-HAL-true,10-MX_USART1_UART_Init-USART1-false-HAL-true,11-MX_USB_DEVICE_Init-USB_DEVICE-false-HAL-false,12-MX_IWDG_Init-IWDG-false-HAL-true
RCC.AHBFreq_Value=48000000
RCC.APB1Freq_Value=48000000
RCC.APB1TimFreq_Value=48000000
RCC.CECFreq_Value=32786.88524590164
RCC.FCLKCortexFreq_Value=48000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=48000000
RCC.HSI48_Value=48000000
RCC.HSICECFreq_Value=32786.88524590164
RCC.HSI_VALUE=8000000
RCC.I2SFreq_Value=48000000
RCC.IPParameters=AHBFreq_Value,APB1Freq_Value,APB1TimFreq_Value,CECFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSI48_Value,HSICECFreq_Value,HSI_VALUE,I2SFreq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,PLLSourceVirtual,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USART1Freq_Value,USART2Freq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=48000000
RCC.PLLCLKFreq_Value=48000000
RCC.PLLMCOFreq_Value=48000000
RCC.PLLMUL=RCC_PLL_MUL6
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SYSCLKFreq_VALUE=48000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=48000000
RCC.USART1Freq_Value=48000000
RCC.USART2Freq_Value=48000000
RCC.USBFreq_Value=48000000
RCC.VCOOutput2Freq_Value=8000000

SH.GPXTI1.0=GPIO_EXTI1
SH.GPXTI1.ConfNb=1
SH.GPXTI2.0=GPIO_EXTI2
SH.GPXTI2.ConfNb=1
SH.GPXTI3.0=GPIO_EXTI3
SH.GPXTI3.ConfNb=1
SH.GPXTI4.0=GPIO_EXTI4
SH.GPXTI4.ConfNb=1
SH.S_TIM1_CH1.0=TIM1_CH1,PWM Generation1 CH1
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,PWM Generation1 CH1
SH.S_TIM3_CH1.ConfNb=1
TIM1.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM1.IPParameters=Channel-PWM Generation1 CH1,Prescaler,Period,Pulse-PWM Generation1 CH1
TIM1.Period=999
TIM1.Prescaler=47
TIM1.Pulse-PWM\ Generation1\ CH1=0
TIM2.Channel-Output\ Compare1\ CH1=TIM_CHANNEL_1
TIM2.IPParameters=Channel-Output Compare1 CH1,Prescaler,Period,Pulse-Output Compare1 CH1
TIM2.Period=0xFFFFFFFF
TIM2.Prescaler=47
TIM2.Pulse-Output\ Compare1\ CH1=0
TIM3.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM3.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM3.IPParameters=Channel-PWM Generation1 CH1,Channel-PWM Generation2 CH2,Prescaler,Period,Pulse-PWM Generation1 CH1,Pulse-PWM Generation2 CH2
TIM3.Period=999
TIM3.Prescaler=47
TIM3.Pulse-PWM\ Generation1\ CH1=0
TIM3.Pulse-PWM\ Generation2\ CH2=0
TIM15.IPParameters=Prescaler,Period,TIM_MasterOutputTrigger
TIM15.Period=999
TIM15.Prescaler=47
TIM15.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
USART1.BaudRate=420000
USART1.IPParameters=VirtualMode-Asynchronous,BaudRate
USART1.VirtualMode-Asynchronous=VM_ASYNC
USB_DEVICE.CLASS_NAME_FS=CDC
USB_DEVICE.IPParameters=VID,PID,MANUFACTURER_STRING,PRODUCT_STRING_CDC_FS,CLASS_NAME_FS
USB_DEVICE.MANUFACTURER_STRING=CRSF Team
USB_DEVICE.PID=22336
USB_DEVICE.PRODUCT_STRING_CDC_FS=CRSF Controller VBT6
USB_DEVICE.VID=1155






VP_CRC_VS_CRC.Mode=CRC_Activate
VP_CRC_VS_CRC.Signal=CRC_VS_CRC
VP_IWDG_VS_IWDG.Mode=IWDG_Activate
VP_IWDG_VS_IWDG.Signal=IWDG_VS_IWDG
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
VP_TIM15_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM15_VS_ClockSourceINT.Signal=TIM15_VS_ClockSourceINT
VP_USB_DEVICE_VS_USB_DEVICE_CDC_FS.Mode=CDC_FS
VP_USB_DEVICE_VS_USB_DEVICE_CDC_FS.Signal=USB_DEVICE_VS_USB_DEVICE_CDC_FS
board=custom
