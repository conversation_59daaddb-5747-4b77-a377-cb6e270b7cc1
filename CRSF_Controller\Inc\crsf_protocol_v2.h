/**
 * @file crsf_protocol_v2.h
 * @brief CRSF协议状态机实现 (基于deviation设计)
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __CRSF_PROTOCOL_V2_H
#define __CRSF_PROTOCOL_V2_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"
#include "clock_system.h"

/* CRSF协议状态 */
typedef enum {
    CRSF_STATE_DATA0 = 0,   // 准备数据阶段
    CRSF_STATE_DATA1,       // 发送数据阶段
    CRSF_STATE_IDLE
} crsf_state_t;

/* CRSF帧周期定义 */
#define CRSF_FRAME_PERIOD_MIN   2000    // 2ms最小周期
#define CRSF_FRAME_PERIOD_MAX   14000   // 14ms最大周期
#define CRSF_FRAME_PERIOD       4000    // 4ms默认周期

/* CRSF数据包大小 */
#define CRSF_PACKET_SIZE        26      // RC数据包大小
#define CRSF_MAX_PACKET_SIZE    64      // 最大包大小

/* 混音器运行时间限制 */
#define MIXER_RUNTIME_MAX       2000    // 2ms最大运行时间
#define MIXER_RUNTIME_STEP      50      // 50us步进

/* 全局变量声明 */
extern volatile crsf_state_t crsf_state;
extern volatile uint16_t mixer_runtime;
extern volatile int32_t correction;
extern volatile uint32_t updateInterval;

/* 函数声明 */

/* 协议初始化 */
error_code_t CRSF_Protocol_Init(void);
error_code_t CRSF_Protocol_Start(void);
error_code_t CRSF_Protocol_Stop(void);

/* 状态机处理 */
uint16_t CRSF_SerialCallback(void);
void CRSF_ProcessTelemetry(void);

/* 数据包构建 */
uint8_t CRSF_BuildRCDataPacket(uint8_t* packet);
uint8_t CRSF_BuildTelemetryPacket(uint8_t* packet);

/* 时序管理 */
uint32_t CRSF_GetUpdateInterval(void);
void CRSF_SetUpdateInterval(uint32_t interval_us);
void CRSF_AdjustTiming(int32_t correction_us);

/* 数据接收处理 */
void CRSF_SerialReceive(uint8_t data, uint8_t status);
void CRSF_ProcessRxData(void);
bool CRSF_HasRxData(void);
uint16_t CRSF_GetRxDataCount(void);

/* 状态查询 */
crsf_state_t CRSF_GetState(void);
bool CRSF_IsTransmitting(void);
uint16_t CRSF_GetMixerRuntime(void);

/* 调试功能 */
#if DEBUG_ENABLED
void CRSF_PrintState(void);
void CRSF_PrintTiming(void);
#endif

#ifdef __cplusplus
}
#endif

#endif /* __CRSF_PROTOCOL_V2_H */
