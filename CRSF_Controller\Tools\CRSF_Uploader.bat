@echo off
chcp 65001 >nul
title CRSF Controller 固件升级工具

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                CRSF Controller 固件升级工具                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.x
    echo    下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查pyserial是否安装
python -c "import serial" >nul 2>&1
if errorlevel 1 (
    echo 📦 正在安装pyserial库...
    pip install pyserial
    if errorlevel 1 (
        echo ❌ 安装pyserial失败，请手动安装: pip install pyserial
        pause
        exit /b 1
    )
)

REM 检查固件文件
if "%~1"=="" (
    echo 📁 请选择固件文件 (.bin):
    echo    拖拽固件文件到此窗口，或者输入文件路径
    set /p firmware_file="固件文件路径: "
) else (
    set firmware_file=%~1
)

if not exist "%firmware_file%" (
    echo ❌ 错误: 固件文件不存在: %firmware_file%
    pause
    exit /b 1
)

echo.
echo 📋 固件信息:
echo    文件: %firmware_file%
for %%A in ("%firmware_file%") do echo    大小: %%~zA 字节
echo.

REM 检查设备连接
echo 🔍 正在查找CRSF Controller设备...
python -c "
import serial.tools.list_ports
found = False
for port in serial.tools.list_ports.comports():
    if 'CRSF' in str(port.description) or 'STM32' in str(port.description):
        print(f'✅ 找到设备: {port.device} - {port.description}')
        found = True
        break
if not found:
    print('⚠️  未找到设备，请确保:')
    print('   1. 设备已连接USB线')
    print('   2. 设备处于升级模式')
    print('   3. 驱动程序已正确安装')
"

echo.
echo 📖 进入升级模式的方法:
echo    方法1: 关机状态下同时按住 KEY1 + KEY2，然后上电
echo    方法2: 在菜单中选择"固件升级"选项
echo    方法3: 通过USB发送升级命令
echo.

set /p confirm="确认开始升级? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo 升级已取消
    pause
    exit /b 0
)

echo.
echo 🚀 开始固件升级...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.

REM 运行Python升级工具
python firmware_uploader.py "%firmware_file%"

if errorlevel 1 (
    echo.
    echo ❌ 固件升级失败！
    echo.
    echo 🔧 故障排除建议:
    echo    1. 检查设备是否正确进入升级模式
    echo    2. 确认USB连接稳定
    echo    3. 重新启动设备并重试
    echo    4. 检查固件文件是否正确
    echo.
) else (
    echo.
    echo ✅ 固件升级成功！
    echo.
    echo 📝 升级完成后:
    echo    1. 设备会自动重启
    echo    2. 检查新固件功能是否正常
    echo    3. 如有问题可通过引导程序恢复
    echo.
)

echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
pause
