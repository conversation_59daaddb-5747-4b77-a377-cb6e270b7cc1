# CRSF Controller 模块依赖关系

## 📊 **模块依赖图**

```mermaid
graph TD
    A[main_v2.c] --> B[clock_system]
    A --> C[crsf_protocol_v2]
    A --> D[mixer]
    A --> E[adc_input]
    A --> F[button_input]
    A --> G[oled_display]
    A --> H[menu_system]
    A --> I[sound]
    A --> J[calibration]
    A --> K[usb_cdc]
    A --> L[hal_drivers]
    
    B --> L
    C --> B
    C --> D
    C --> L
    D --> E
    D --> J
    E --> L
    F --> L
    G --> L
    H --> F
    H --> G
    H --> J
    I --> B
    I --> L
    J --> E
    J --> G
    K --> L
    
    L --> M[STM32 HAL]
    K --> N[USB Middleware]
```

## 🔗 **依赖关系详解**

### 核心依赖
```
main_v2.c (主程序)
├── clock_system (时钟系统) - 核心调度
├── crsf_protocol_v2 (CRSF协议) - 主要功能
├── mixer (混音器) - 数据处理
└── hal_drivers (硬件驱动) - 底层支持
```

### 输入模块
```
adc_input (ADC输入)
├── hal_drivers (GPIO/ADC/DMA配置)
└── calibration (校准数据)

button_input (按键输入)
└── hal_drivers (GPIO配置)
```

### 输出模块
```
oled_display (显示输出)
└── hal_drivers (I2C配置)

sound (音效输出)
├── clock_system (定时控制)
└── hal_drivers (PWM配置)

usb_cdc (USB输出)
├── hal_drivers (USB配置)
└── USB Middleware (USB库)
```

### 应用模块
```
menu_system (菜单系统)
├── button_input (按键事件)
├── oled_display (界面显示)
└── calibration (校准功能)

calibration (校准系统)
├── adc_input (数据采集)
└── oled_display (界面显示)
```

## 📋 **编译顺序**

### 1. 底层驱动
```makefile
hal_drivers.c          # 硬件抽象层
```

### 2. 核心系统
```makefile
clock_system.c         # 时钟和中断系统
```

### 3. 输入模块
```makefile
adc_input.c           # ADC输入处理
button_input.c        # 按键输入处理
```

### 4. 处理模块
```makefile
mixer.c               # 混音器处理
calibration.c         # 校准系统
```

### 5. 输出模块
```makefile
oled_display.c        # 显示输出
sound.c               # 音效输出
usb_cdc.c             # USB输出
usbd_desc.c           # USB描述符
usbd_cdc_if.c         # USB接口
```

### 6. 协议模块
```makefile
crsf_protocol_v2.c    # CRSF协议处理
```

### 7. 应用模块
```makefile
menu_system.c         # 菜单系统
```

### 8. 主程序
```makefile
main_v2.c             # 主程序入口
```

## 🔄 **初始化顺序**

### 系统启动流程
```c
1. HAL_Init()                    // HAL库初始化
2. SystemClock_Config()          // 系统时钟配置
3. HAL_GPIO_Init_All()           // GPIO初始化
4. HAL_UART_Init_All()           // UART初始化
5. HAL_TIM_Init_All()            // 定时器初始化
6. HAL_I2C_Init_All()            // I2C初始化
7. HAL_ADC_Init_All()            // ADC初始化
8. HAL_USB_Init()                // USB初始化
9. ADC_Input_Init()              // ADC输入模块
10. Button_Input_Init()          // 按键输入模块
11. OLED_Init()                  // OLED显示模块
12. Menu_Init()                  // 菜单系统
13. MIXER_Init()                 // 混音器系统
14. SOUND_Init()                 // 音效系统
15. USB_CDC_Init()               // USB CDC
16. CRSF_Protocol_Init()         // CRSF协议
17. CLOCK_Init()                 // 时钟系统 (最后)
18. CRSF_Protocol_Start()        // 启动协议
```

## 📦 **模块接口**

### 标准接口模式
```c
// 每个模块都遵循相同的接口模式
error_code_t MODULE_Init(void);        // 初始化
error_code_t MODULE_Start(void);       // 启动
error_code_t MODULE_Stop(void);        // 停止
void MODULE_Task(void* parameters);    // 任务函数
error_code_t MODULE_Config(...);       // 配置函数
```

### 数据流接口
```c
// 输入模块
uint16_t INPUT_GetValue(channel_t ch);

// 处理模块  
void PROCESS_SetInput(input_t input);
output_t PROCESS_GetOutput(void);

// 输出模块
error_code_t OUTPUT_Send(data_t data);
```

## 🔧 **移植适配点**

### 硬件相关
```c
hal_drivers.h/c        # 引脚定义、时钟配置
config.h               # 系统参数配置
```

### 协议相关
```c
crsf_protocol_v2.h/c   # 波特率、帧格式
mixer.h/c              # 通道映射、混音规则
```

### 外设相关
```c
adc_input.h/c          # ADC通道、采样率
button_input.h/c       # 按键映射、消抖时间
oled_display.h/c       # I2C地址、显示参数
sound.h/c              # PWM频率、音效定义
```

## ⚠️ **注意事项**

### 循环依赖避免
- 使用前向声明避免头文件循环包含
- 通过回调函数解耦模块间依赖
- 使用事件机制代替直接调用

### 内存管理
- 静态分配避免动态内存问题
- 合理规划全局变量和局部变量
- 注意栈空间使用

### 实时性保证
- 关键路径避免复杂依赖
- 中断处理函数保持简洁
- 使用软件中断分离紧急和非紧急任务

这个模块化设计确保了系统的可维护性和可移植性，每个模块都有清晰的职责和接口。
