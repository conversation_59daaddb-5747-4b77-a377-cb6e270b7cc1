/**
 * @file adc_input.h
 * @brief ADC输入处理接口
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __ADC_INPUT_H
#define __ADC_INPUT_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"

/* ADC通道映射 */
typedef enum {
    ADC_CH1 = 0,            // DMA[0] - 摇杆CH1
    ADC_CH2,                // DMA[1] - 摇杆CH2
    ADC_CH3,                // DMA[2] - 摇杆CH3(油门)
    ADC_CH4,                // DMA[3] - 摇杆CH4
    ADC_SWA,                // DMA[4] - 三段开关A，飞行模式选择
    ADC_SWB,                // DMA[5] - 三段开关B，相机参数选择
    ADC_VBAT,               // DMA[6] - 电池电压
    ADC_BIN,                // DMA[7] - BIN输入，外接电源检测
    ADC_VRA,                // DMA[8] - 电位器A，云台俯仰拨轮
    ADC_VRB,                // DMA[9] - 电位器B，相机参数调整拨轮
    ADC_CH_COUNT
} adc_channel_t;

/* 三段开关位置定义 */
typedef enum {
    SWITCH_POS_LOW = 0,     // 下位
    SWITCH_POS_MID,         // 中位
    SWITCH_POS_HIGH,        // 上位
    SWITCH_POS_UNKNOWN      // 未知位置
} switch_position_t;

/* ADC校准数据结构 */
typedef struct {
    uint16_t min_value;     // 最小值
    uint16_t center_value;  // 中点值
    uint16_t max_value;     // 最大值
    bool reversed;          // 是否反向
    uint16_t deadband;      // 死区
} adc_calibration_t;

/* ADC滤波器类型 */
typedef enum {
    ADC_FILTER_NONE = 0,
    ADC_FILTER_AVERAGE,
    ADC_FILTER_MEDIAN,
    ADC_FILTER_LOW_PASS
} adc_filter_type_t;

/* ADC配置结构 */
typedef struct {
    adc_filter_type_t filter_type;
    uint8_t filter_samples;     // 滤波样本数
    uint16_t update_rate_hz;    // 更新频率
    bool auto_calibration;      // 自动校准
} adc_config_t;

/* ADC状态结构 */
typedef struct {
    uint16_t raw_values[ADC_CH_COUNT];      // 原始ADC值
    uint16_t filtered_values[ADC_CH_COUNT]; // 滤波后的值
    uint16_t calibrated_values[ADC_CH_COUNT]; // 校准后的值
    bool calibration_active;                // 校准状态
    uint32_t sample_count;                  // 采样计数
    uint32_t last_update_time;              // 上次更新时间
} adc_status_t;

/* 全局变量声明 */
extern uint16_t adc_buffer[ADC_CHANNELS];
extern adc_calibration_t adc_calibration[ADC_CH_COUNT];
extern adc_config_t adc_config;
extern adc_status_t adc_status;

/* 函数声明 */

/* 初始化和配置 */
error_code_t ADC_Input_Init(void);
error_code_t ADC_Input_Start(void);
error_code_t ADC_Input_Stop(void);
error_code_t ADC_Input_Configure(const adc_config_t* config);

/* 数据读取 */
uint16_t ADC_Input_GetRawValue(adc_channel_t channel);
uint16_t ADC_Input_GetFilteredValue(adc_channel_t channel);
uint16_t ADC_Input_GetCalibratedValue(adc_channel_t channel);
void ADC_Input_GetAllValues(rc_input_t* rc_input);

/* 校准功能 */
error_code_t ADC_Input_StartCalibration(adc_channel_t channel);
error_code_t ADC_Input_SetCalibrationPoint(adc_channel_t channel, uint16_t value, uint8_t point);
error_code_t ADC_Input_FinishCalibration(adc_channel_t channel);
error_code_t ADC_Input_LoadCalibration(void);
error_code_t ADC_Input_SaveCalibration(void);
error_code_t ADC_Input_ResetCalibration(adc_channel_t channel);

/* 开机校准 */
bool ADC_Input_NeedBootCalibration(void);
error_code_t ADC_Input_BootCalibration(void);
bool ADC_Input_IsBootCalibrationComplete(void);

/* 菜单校准 */
error_code_t ADC_Input_MenuCalibration(adc_channel_t channel);
error_code_t ADC_Input_CalibrateSticks(void);
error_code_t ADC_Input_CalibrateSwitches(void);

/* 三段开关检测 */
switch_position_t ADC_Input_GetSwitchPosition(adc_channel_t channel);
const char* ADC_Input_GetSwitchPositionString(switch_position_t pos);

/* 滤波功能 */
void ADC_Input_SetFilter(adc_channel_t channel, adc_filter_type_t type, uint8_t samples);
uint16_t ADC_Input_ApplyFilter(adc_channel_t channel, uint16_t new_value);

/* 状态查询 */
bool ADC_Input_IsCalibrated(adc_channel_t channel);
bool ADC_Input_IsCalibrationActive(void);
adc_status_t* ADC_Input_GetStatus(void);
uint32_t ADC_Input_GetSampleRate(void);

/* 中断回调 */
void ADC_Input_ConversionComplete(void);
void ADC_Input_ConversionHalfComplete(void);

/* 任务函数 */
void ADC_Input_Task(void* parameters);

/* 工具函数 */
uint16_t ADC_Input_ApplyCalibration(adc_channel_t channel, uint16_t raw_value);
uint16_t ADC_Input_ApplyDeadband(uint16_t value, uint16_t center, uint16_t deadband);
bool ADC_Input_IsValueInRange(uint16_t value, uint16_t min, uint16_t max);

/* 调试功能 */
#if DEBUG_ENABLED
void ADC_Input_PrintValues(void);
void ADC_Input_PrintCalibration(adc_channel_t channel);
void ADC_Input_PrintStatus(void);
#endif

/* 校准点定义 */
#define ADC_CALIB_POINT_MIN     0
#define ADC_CALIB_POINT_CENTER  1
#define ADC_CALIB_POINT_MAX     2

/* 默认校准值 */
#define ADC_DEFAULT_MIN         0
#define ADC_DEFAULT_CENTER      2048
#define ADC_DEFAULT_MAX         4095
#define ADC_DEFAULT_DEADBAND    20

/* 滤波器参数 */
#define ADC_FILTER_MAX_SAMPLES  16
#define ADC_FILTER_DEFAULT_SAMPLES 4

#ifdef __cplusplus
}
#endif

#endif /* __ADC_INPUT_H */
