# CRSF Controller USB固件升级指南

## 📖 **概述**

本指南详细介绍如何为CRSF Controller实现USB固件升级功能，包括引导程序设计、升级协议、PC端工具等完整解决方案。

## 🏗️ **系统架构**

### Flash布局设计
```
STM32F072 Flash (128KB) 布局:
┌─────────────────────────────────────────┐ 0x08000000
│  Bootloader (8KB)                       │ 引导程序
├─────────────────────────────────────────┤ 0x08002000
│  Application (112KB)                    │ 应用程序
├─────────────────────────────────────────┤ 0x0801E000
│  Configuration (4KB)                    │ 配置数据
├─────────────────────────────────────────┤ 0x0801F000
│  Backup (4KB)                          │ 备份区域
└─────────────────────────────────────────┘ 0x08020000
```

### 升级流程图
```mermaid
graph TD
    A[上电启动] --> B{检查升级标志}
    B -->|是| C[进入Bootloader]
    B -->|否| D{检查按键组合}
    D -->|是| C
    D -->|否| E{检查应用程序}
    E -->|有效| F[跳转到应用程序]
    E -->|无效| C
    C --> G[初始化USB CDC]
    G --> H[等待升级命令]
    H --> I[处理升级数据]
    I --> J[写入Flash]
    J --> K[校验数据]
    K --> L[升级完成]
    L --> M[复位到应用程序]
```

## 🔧 **引导程序实现**

### 1. 引导程序结构
```c
Bootloader/
├── Inc/
│   └── bootloader.h              # 引导程序接口
├── Src/
│   ├── bootloader.c              # 主要逻辑
│   ├── flash_operations.c        # Flash操作
│   └── usb_bootloader.c          # USB通信
└── Linker/
    └── bootloader.ld             # 链接脚本
```

### 2. 关键功能模块

#### 2.1 启动检查
```c
void Bootloader_Main(void)
{
    bool enter_upgrade = false;
    
    // 检查升级标志
    if (Bootloader_CheckUpgradeFlag()) {
        enter_upgrade = true;
    }
    
    // 检查按键组合 (KEY1 + KEY2)
    if (Button_Combination_Pressed()) {
        enter_upgrade = true;
    }
    
    // 检查应用程序有效性
    if (!Bootloader_IsValidApplication()) {
        enter_upgrade = true;
    }
    
    if (enter_upgrade) {
        Bootloader_EnterUpgradeMode();
    } else {
        Bootloader_JumpToApplication();
    }
}
```

#### 2.2 Flash操作
```c
// 擦除应用程序区域
upgrade_error_t Bootloader_EraseFlash(uint32_t start_addr, uint32_t size);

// 写入固件数据
upgrade_error_t Bootloader_WriteFlash(uint32_t addr, const uint8_t* data, uint16_t length);

// 校验固件数据
upgrade_error_t Bootloader_VerifyFlash(uint32_t addr, const uint8_t* data, uint16_t length);

// 应用程序有效性检查
bool Bootloader_IsValidApplication(void);
```

#### 2.3 USB通信协议
```c
// 升级数据包结构
typedef struct {
    uint16_t command;               // 命令类型
    uint16_t length;                // 数据长度
    uint32_t address;               // 目标地址
    uint32_t checksum;              // 校验和
    uint8_t data[256];              // 数据内容
} upgrade_packet_t;

// 升级命令定义
#define UPGRADE_CMD_ENTER    0x55AA  // 进入升级模式
#define UPGRADE_CMD_ERASE    0x1234  // 擦除Flash
#define UPGRADE_CMD_WRITE    0x5678  // 写入数据
#define UPGRADE_CMD_VERIFY   0x9ABC  // 校验数据
#define UPGRADE_CMD_EXIT     0xAA55  // 退出升级模式
#define UPGRADE_CMD_RESET    0xDEF0  // 复位系统
```

## 🖥️ **PC端升级工具**

### 1. Python升级工具
```python
# 使用方法
python firmware_uploader.py firmware.bin

# 指定串口
python firmware_uploader.py -p COM3 firmware.bin

# 指定波特率
python firmware_uploader.py -b 115200 firmware.bin
```

### 2. 升级工具功能
- **自动设备检测**: 自动查找CRSF Controller设备
- **固件校验**: MD5校验确保文件完整性
- **进度显示**: 实时显示升级进度
- **错误处理**: 详细的错误信息和恢复机制
- **安全保护**: 多重校验防止升级失败

### 3. 升级流程
```python
def upload_firmware(self, firmware_file):
    # 1. 读取固件文件
    firmware_data = read_firmware_file(firmware_file)
    
    # 2. 连接设备
    self.connect()
    
    # 3. 进入升级模式
    self.enter_upgrade_mode(len(firmware_data))
    
    # 4. 擦除Flash
    self.erase_flash()
    
    # 5. 写入固件
    self.write_firmware(firmware_data)
    
    # 6. 校验固件
    self.verify_firmware(firmware_data)
    
    # 7. 退出升级模式
    self.exit_upgrade_mode()
    
    # 8. 复位设备
    self.reset_device()
```

## 🔨 **编译配置**

### 1. 引导程序Makefile
```makefile
# Bootloader编译配置
TARGET = bootloader
BUILD_DIR = build_bootloader

# 源文件
C_SOURCES = \
Bootloader/Src/bootloader.c \
Bootloader/Src/flash_operations.c \
Bootloader/Src/usb_bootloader.c \
# ... HAL库文件

# 链接脚本
LDSCRIPT = Bootloader/Linker/bootloader.ld

# 编译标志
CFLAGS += -DBOOTLOADER_BUILD
```

### 2. 应用程序修改
```c
// 在应用程序中添加升级标志设置
void Application_RequestUpgrade(void)
{
    // 设置升级标志
    uint32_t* flag_ptr = (uint32_t*)0x20003FF0;
    *flag_ptr = 0x12345678;
    
    // 复位到引导程序
    NVIC_SystemReset();
}

// 在应用程序开始处添加标识
const uint32_t app_signature __attribute__((section(".app_signature"))) = 0x43525346; // "CRSF"
```

### 3. 链接脚本修改
```ld
/* 引导程序链接脚本 */
MEMORY
{
  FLASH (rx) : ORIGIN = 0x08000000, LENGTH = 8K
  RAM (xrw)  : ORIGIN = 0x20000000, LENGTH = 16K
}

/* 应用程序链接脚本 */
MEMORY
{
  FLASH (rx) : ORIGIN = 0x08002000, LENGTH = 112K
  RAM (xrw)  : ORIGIN = 0x20000000, LENGTH = 16K
}

/* 应用程序标识段 */
.app_signature 0x08002100 :
{
  KEEP(*(.app_signature))
} > FLASH
```

## 🚀 **使用指南**

### 1. 编译引导程序
```bash
# 编译引导程序
cd CRSF_Controller
make -f Makefile.bootloader clean
make -f Makefile.bootloader all

# 烧录引导程序 (仅首次需要)
make -f Makefile.bootloader flash
```

### 2. 编译应用程序
```bash
# 编译应用程序
make clean
make all

# 生成升级文件
arm-none-eabi-objcopy -O binary build/CRSF_Controller.elf firmware.bin
```

### 3. 固件升级操作

#### 3.1 通过菜单升级
```c
// 在菜单系统中添加升级选项
void Menu_FirmwareUpgrade(void)
{
    OLED_Clear();
    OLED_WriteString("Firmware Upgrade");
    OLED_WriteString("Connect USB and");
    OLED_WriteString("run upgrade tool");
    OLED_Update();
    
    // 设置升级标志并重启
    Application_RequestUpgrade();
}
```

#### 3.2 通过按键组合升级
```
1. 关机状态下同时按住 KEY1 + KEY2
2. 上电，保持按键3秒
3. 释放按键，设备进入升级模式
4. LED常亮表示等待升级
5. 运行PC端升级工具
```

#### 3.3 通过USB命令升级
```c
// 通过USB CDC发送升级命令
void USB_RequestUpgrade(void)
{
    USB_CDC_Printf("UPGRADE_REQUEST\n");
    HAL_Delay(100);
    Application_RequestUpgrade();
}
```

### 4. PC端升级步骤
```bash
# 1. 安装Python依赖
pip install pyserial

# 2. 设备进入升级模式
# (通过按键组合或菜单操作)

# 3. 运行升级工具
python Tools/firmware_uploader.py firmware.bin

# 4. 等待升级完成
# 设备会自动重启到新固件
```

## 🛡️ **安全机制**

### 1. 多重校验
- **CRC32校验**: 固件完整性校验
- **地址检查**: 防止写入错误地址
- **大小检查**: 防止超出Flash范围
- **签名验证**: 应用程序有效性验证

### 2. 备份恢复
```c
// 升级前备份关键数据
upgrade_error_t Bootloader_BackupApplication(void);

// 升级失败时恢复
upgrade_error_t Bootloader_RestoreApplication(void);
```

### 3. 超时保护
- **升级超时**: 5分钟无操作自动退出
- **通信超时**: 5秒无响应重试
- **写入超时**: 单次操作超时检测

### 4. 错误恢复
```c
// 升级失败处理
if (upgrade_failed) {
    // 1. 恢复备份数据
    Bootloader_RestoreApplication();
    
    // 2. 清除升级标志
    Bootloader_SetUpgradeFlag(false);
    
    // 3. 跳转到应用程序
    Bootloader_JumpToApplication();
}
```

## 🔧 **故障排除**

### 1. 常见问题

#### 问题1: 设备无法进入升级模式
```
原因: 按键组合不正确或应用程序正常
解决: 
1. 确认按键组合 (KEY1 + KEY2)
2. 检查应用程序是否设置升级标志
3. 检查引导程序是否正确烧录
```

#### 问题2: USB设备无法识别
```
原因: USB驱动或硬件问题
解决:
1. 检查USB线缆连接
2. 确认USB时钟配置 (HSI48)
3. 检查USB描述符配置
```

#### 问题3: 升级过程中断
```
原因: 通信错误或Flash操作失败
解决:
1. 重新进入升级模式
2. 检查Flash是否损坏
3. 使用备份恢复功能
```

### 2. 调试技巧
```c
// 添加调试输出
#ifdef BOOTLOADER_DEBUG
#define DEBUG_PRINT(fmt, ...) printf(fmt, ##__VA_ARGS__)
#else
#define DEBUG_PRINT(fmt, ...)
#endif

// 监控升级状态
void Bootloader_PrintStatus(void)
{
    DEBUG_PRINT("Status: %d, Error: %d\n", 
                upgrade_state.status, 
                upgrade_state.error);
    DEBUG_PRINT("Progress: %d/%d bytes\n", 
                upgrade_state.written_size, 
                upgrade_state.total_size);
}
```

## 📋 **测试验证**

### 1. 功能测试
- [ ] 引导程序启动检查
- [ ] 按键组合进入升级模式
- [ ] USB设备识别
- [ ] 固件上传和校验
- [ ] 升级完成后正常启动

### 2. 异常测试
- [ ] 升级过程中断电
- [ ] 错误固件文件
- [ ] 通信中断恢复
- [ ] Flash写入失败处理

### 3. 性能测试
- [ ] 升级速度 (目标: <2分钟)
- [ ] 内存使用 (引导程序 <8KB)
- [ ] 启动时间 (正常启动 <1秒)

## 🎯 **总结**

USB固件升级系统为CRSF Controller提供了：

✅ **便捷升级**: 通过USB线即可升级固件  
✅ **安全可靠**: 多重校验和备份恢复机制  
✅ **用户友好**: 简单的操作流程和清晰的状态指示  
✅ **开发高效**: 自动化的升级工具和完整的错误处理  
✅ **维护方便**: 远程升级支持和详细的日志记录  

这套完整的USB固件升级解决方案确保了CRSF Controller的可维护性和用户体验！
