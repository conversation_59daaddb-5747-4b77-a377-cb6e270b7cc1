# CRSF Controller USB虚拟串口功能说明

## 🔌 USB CDC功能概述

本遥控器集成了USB CDC (Communication Device Class) 虚拟串口功能，实现以下特性：
- **调试信息输出**: 实时系统状态和调试信息
- **参数配置**: 通过串口命令配置系统参数
- **固件升级**: 支持通过USB进行固件更新
- **数据监控**: 实时监控CRSF数据和系统状态

## 🔧 硬件连接

### USB接口 (PA11/PA12)
```
PA11 - USB_DM (USB D-)
PA12 - USB_DP (USB D+)
```

### 双重功能
- **数据通信**: USB虚拟串口
- **电源充电**: 5V充电输入

## 💻 软件特性

### 1. 虚拟串口规格
- **设备类型**: CDC (Communication Device Class)
- **波特率**: 115200 bps (虚拟，实际无限制)
- **数据位**: 8位
- **停止位**: 1位
- **校验位**: 无
- **流控**: 无

### 2. 缓冲区配置
```c
#define USB_CDC_RX_BUFFER_SIZE      512     // 接收缓冲区
#define USB_CDC_TX_BUFFER_SIZE      512     // 发送缓冲区
#define USB_CDC_MAX_PACKET_SIZE     64      // 最大包大小
```

### 3. 设备识别信息
```
厂商ID (VID): 0x0483
产品ID (PID): 0x5740
制造商: "CRSF Controller"
产品名: "CRSF Controller CDC"
序列号: 基于芯片唯一ID生成
```

## 🖥️ 使用方法

### Windows系统
1. **驱动安装**: Windows 10/11自动识别，无需额外驱动
2. **设备管理器**: 在"端口(COM和LPT)"中找到设备
3. **串口工具**: 使用PuTTY、串口助手等工具连接

### Linux系统
1. **设备识别**: 自动识别为 `/dev/ttyACMx`
2. **权限设置**: `sudo chmod 666 /dev/ttyACM0`
3. **终端连接**: `screen /dev/ttyACM0 115200`

### macOS系统
1. **设备识别**: 自动识别为 `/dev/cu.usbmodemXXXX`
2. **终端连接**: `screen /dev/cu.usbmodemXXXX 115200`

## 📊 调试信息输出

### 系统启动信息
```
CRSF Controller v1.0 Started
Build: Dec 25 2024 10:30:00
System initialized successfully
ADC calibration loaded
ELRS TX connected: ExpressLRS TX
```

### 实时状态信息
```
[INFO] CRSF: Packet sent, channels: 1500,1500,1000,1500
[INFO] ELRS: Link quality: 100%, RSSI: -45dBm
[INFO] Power: Battery 85%, USB connected
[WARN] ADC: Channel 2 out of range: 4095
[ERROR] EEPROM: Write failed at address 0x1000
```

### 性能统计
```
=== System Statistics ===
Uptime: 00:15:32
CPU Usage: 45%
Free RAM: 8.5KB
CRSF Packets: 15000 sent, 12000 received
USB Bytes: 2.5MB sent, 1.2MB received
```

## 🎛️ 串口命令接口

### 基础命令
```
help                    - 显示帮助信息
version                 - 显示版本信息
status                  - 显示系统状态
reset                   - 软件复位
```

### 配置命令
```
config list             - 列出所有配置项
config get <key>        - 获取配置值
config set <key> <val>  - 设置配置值
config save             - 保存配置到EEPROM
config load             - 从EEPROM加载配置
```

### 校准命令
```
calib start             - 开始校准
calib status            - 校准状态
calib reset             - 重置校准
calib save              - 保存校准数据
```

### 监控命令
```
monitor adc             - 监控ADC数据
monitor crsf            - 监控CRSF数据
monitor power           - 监控电源状态
monitor stop            - 停止监控
```

## 🔍 API接口说明

### 发送函数
```c
// 发送原始数据
error_code_t USB_CDC_Transmit(const uint8_t* data, uint16_t length);

// 发送字符串
error_code_t USB_CDC_TransmitString(const char* str);

// 格式化发送
error_code_t USB_CDC_Printf(const char* format, ...);
```

### 接收函数
```c
// 接收数据
uint16_t USB_CDC_Receive(uint8_t* data, uint16_t max_length);

// 检查可用数据
uint16_t USB_CDC_Available(void);
```

### 状态查询
```c
// 连接状态
bool USB_CDC_IsConnected(void);
bool USB_CDC_IsConfigured(void);

// 传输状态
bool USB_CDC_IsTxBusy(void);
```

## 🛠️ 开发调试

### 调试信息重定向
```c
// printf自动重定向到USB CDC
printf("Debug info: %d\n", value);

// 直接使用USB CDC
USB_CDC_Printf("CRSF packet: %02X %02X\n", data[0], data[1]);
```

### 实时数据监控
```c
// 在任务中输出实时数据
void Debug_Task(void* parameters) {
    static uint32_t last_time = 0;
    uint32_t current_time = millis();
    
    if (current_time - last_time >= 1000) {  // 每秒输出
        USB_CDC_Printf("ADC: %d,%d,%d,%d\n", 
                      adc_values[0], adc_values[1], 
                      adc_values[2], adc_values[3]);
        last_time = current_time;
    }
}
```

## 📈 性能特点

### 传输性能
- **理论速度**: USB 2.0 Full Speed (12Mbps)
- **实际速度**: 约1Mbps (受MCU处理能力限制)
- **延迟**: < 1ms
- **可靠性**: 硬件CRC校验，无数据丢失

### 资源占用
- **RAM使用**: 约1KB (缓冲区)
- **Flash使用**: 约8KB (USB库)
- **CPU占用**: < 5% (正常通信)

## 🔧 故障排除

### 常见问题

#### 1. 设备无法识别
**现象**: 电脑无法识别USB设备
**原因**: USB硬件连接或驱动问题
**解决**:
- 检查USB线缆连接
- 更换USB端口
- 重新插拔设备
- 检查设备管理器

#### 2. 串口无法打开
**现象**: 串口工具提示端口被占用
**原因**: 其他程序占用端口
**解决**:
- 关闭其他串口工具
- 重新插拔USB设备
- 重启电脑

#### 3. 数据传输异常
**现象**: 数据丢失或乱码
**原因**: 缓冲区溢出或传输错误
**解决**:
- 降低数据发送频率
- 增加接收处理速度
- 检查USB连接稳定性

#### 4. 调试信息不显示
**现象**: 串口工具无输出
**原因**: printf重定向问题
**解决**:
- 检查DEBUG_ENABLED宏定义
- 确认USB CDC已初始化
- 检查串口工具设置

## 💡 使用建议

### 最佳实践
1. **连接稳定**: 使用质量好的USB线缆
2. **数据量控制**: 避免大量连续输出
3. **错误处理**: 检查传输返回值
4. **缓冲区管理**: 及时处理接收数据

### 开发技巧
1. **条件编译**: 使用宏控制调试输出
2. **分级日志**: 区分INFO、WARN、ERROR级别
3. **性能监控**: 定期输出系统状态
4. **命令解析**: 实现简单的命令行接口

这个USB CDC虚拟串口功能为CRSF遥控器提供了强大的调试和配置能力，大大提升了开发效率和用户体验！
