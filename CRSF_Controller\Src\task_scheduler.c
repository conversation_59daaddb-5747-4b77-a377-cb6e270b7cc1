/**
 * @file task_scheduler.c
 * @brief 任务调度器实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "task_scheduler.h"
#include "hal_drivers.h"

/* 私有变量 */
static task_control_block_t tasks[MAX_TASKS];
static scheduler_stats_t scheduler_stats;
static uint8_t current_task_id = TASK_ID_INVALID;
static uint8_t next_task_id = 1;
static bool scheduler_running = false;
static uint32_t scheduler_start_time = 0;
static uint32_t last_idle_time = 0;

/* 私有函数声明 */
static void TaskScheduler_IdleTask(void* parameters);
static uint8_t TaskScheduler_FindNextTask(void);
static void TaskScheduler_UpdateStats(void);
static void TaskScheduler_RunTask(uint8_t task_id);

/**
 * @brief 调度器初始化
 */
error_code_t TaskScheduler_Init(void)
{
    /* 清空任务表 */
    memset(tasks, 0, sizeof(tasks));
    memset(&scheduler_stats, 0, sizeof(scheduler_stats));
    
    /* 创建空闲任务 */
    tasks[TASK_ID_IDLE].task_id = TASK_ID_IDLE;
    strcpy(tasks[TASK_ID_IDLE].name, "IDLE");
    tasks[TASK_ID_IDLE].function = TaskScheduler_IdleTask;
    tasks[TASK_ID_IDLE].parameters = NULL;
    tasks[TASK_ID_IDLE].state = TASK_STATE_READY;
    tasks[TASK_ID_IDLE].priority = TASK_PRIORITY_IDLE;
    tasks[TASK_ID_IDLE].period_ms = 0;
    tasks[TASK_ID_IDLE].enabled = true;
    
    scheduler_stats.total_tasks = 1;
    current_task_id = TASK_ID_INVALID;
    next_task_id = 1;
    scheduler_running = false;
    
    return ERR_OK;
}

/**
 * @brief 启动调度器
 */
error_code_t TaskScheduler_Start(void)
{
    if (scheduler_running) {
        return ERR_BUSY;
    }
    
    scheduler_running = true;
    scheduler_start_time = millis();
    last_idle_time = micros();
    
    /* 启动调度定时器 */
    return HAL_TIM_Start_All();
}

/**
 * @brief 停止调度器
 */
error_code_t TaskScheduler_Stop(void)
{
    scheduler_running = false;
    
    /* 停止调度定时器 */
    return HAL_TIM_Stop_All();
}

/**
 * @brief 调度器时钟节拍 (由TIM6中断调用)
 */
void TaskScheduler_Tick(void)
{
    if (!scheduler_running) {
        return;
    }
    
    scheduler_stats.scheduler_ticks++;
    
    /* 查找下一个要运行的任务 */
    uint8_t next_task = TaskScheduler_FindNextTask();
    
    if (next_task != TASK_ID_INVALID && next_task != current_task_id) {
        current_task_id = next_task;
        scheduler_stats.context_switches++;
        
        /* 运行任务 */
        TaskScheduler_RunTask(current_task_id);
    }
    
    /* 更新统计信息 */
    TaskScheduler_UpdateStats();
}

/**
 * @brief 创建任务
 */
uint8_t TaskScheduler_CreateTask(const char* name, 
                                task_function_t function, 
                                void* parameters,
                                task_priority_t priority,
                                uint32_t period_ms)
{
    if (next_task_id >= MAX_TASKS || function == NULL) {
        return TASK_ID_INVALID;
    }
    
    uint8_t task_id = next_task_id++;
    task_control_block_t* task = &tasks[task_id];
    
    task->task_id = task_id;
    strncpy(task->name, name, sizeof(task->name) - 1);
    task->name[sizeof(task->name) - 1] = '\0';
    task->function = function;
    task->parameters = parameters;
    task->state = TASK_STATE_READY;
    task->priority = priority;
    task->period_ms = period_ms;
    task->last_run_time = 0;
    task->next_run_time = millis() + period_ms;
    task->run_count = 0;
    task->max_exec_time = 0;
    task->total_exec_time = 0;
    task->enabled = true;
    
    scheduler_stats.total_tasks++;
    
    return task_id;
}

/**
 * @brief 删除任务
 */
error_code_t TaskScheduler_DeleteTask(uint8_t task_id)
{
    if (task_id >= MAX_TASKS || task_id == TASK_ID_IDLE) {
        return ERR_INVALID_PARAM;
    }
    
    task_control_block_t* task = &tasks[task_id];
    if (task->state == TASK_STATE_DELETED) {
        return ERR_INVALID_PARAM;
    }
    
    task->state = TASK_STATE_DELETED;
    task->enabled = false;
    scheduler_stats.total_tasks--;
    
    return ERR_OK;
}

/**
 * @brief 挂起任务
 */
error_code_t TaskScheduler_SuspendTask(uint8_t task_id)
{
    if (task_id >= MAX_TASKS || task_id == TASK_ID_IDLE) {
        return ERR_INVALID_PARAM;
    }
    
    task_control_block_t* task = &tasks[task_id];
    if (task->state == TASK_STATE_DELETED) {
        return ERR_INVALID_PARAM;
    }
    
    task->state = TASK_STATE_SUSPENDED;
    scheduler_stats.suspended_tasks++;
    
    return ERR_OK;
}

/**
 * @brief 恢复任务
 */
error_code_t TaskScheduler_ResumeTask(uint8_t task_id)
{
    if (task_id >= MAX_TASKS) {
        return ERR_INVALID_PARAM;
    }
    
    task_control_block_t* task = &tasks[task_id];
    if (task->state != TASK_STATE_SUSPENDED) {
        return ERR_INVALID_PARAM;
    }
    
    task->state = TASK_STATE_READY;
    task->next_run_time = millis() + task->period_ms;
    scheduler_stats.suspended_tasks--;
    
    return ERR_OK;
}

/**
 * @brief 设置任务周期
 */
error_code_t TaskScheduler_SetTaskPeriod(uint8_t task_id, uint32_t period_ms)
{
    if (task_id >= MAX_TASKS) {
        return ERR_INVALID_PARAM;
    }
    
    task_control_block_t* task = &tasks[task_id];
    if (task->state == TASK_STATE_DELETED) {
        return ERR_INVALID_PARAM;
    }
    
    task->period_ms = period_ms;
    task->next_run_time = millis() + period_ms;
    
    return ERR_OK;
}

/**
 * @brief 获取任务信息
 */
task_control_block_t* TaskScheduler_GetTask(uint8_t task_id)
{
    if (task_id >= MAX_TASKS) {
        return NULL;
    }
    
    return &tasks[task_id];
}

/**
 * @brief 获取任务数量
 */
uint8_t TaskScheduler_GetTaskCount(void)
{
    return scheduler_stats.total_tasks;
}

/**
 * @brief 获取当前运行任务ID
 */
uint8_t TaskScheduler_GetRunningTaskId(void)
{
    return current_task_id;
}

/**
 * @brief 获取调度器统计信息
 */
scheduler_stats_t* TaskScheduler_GetStats(void)
{
    return &scheduler_stats;
}

/**
 * @brief 空闲任务
 */
static void TaskScheduler_IdleTask(void* parameters)
{
    (void)parameters;
    
    /* 空闲任务可以执行低功耗操作 */
    __WFI(); // 等待中断
}

/**
 * @brief 查找下一个要运行的任务
 */
static uint8_t TaskScheduler_FindNextTask(void)
{
    uint32_t current_time = millis();
    uint8_t highest_priority_task = TASK_ID_IDLE;
    task_priority_t highest_priority = TASK_PRIORITY_IDLE;
    
    /* 遍历所有任务，找到优先级最高且到期的任务 */
    for (uint8_t i = 1; i < MAX_TASKS; i++) {
        task_control_block_t* task = &tasks[i];
        
        if (task->state != TASK_STATE_READY || !task->enabled) {
            continue;
        }
        
        /* 检查任务是否到期 */
        if (task->period_ms == 0 || current_time >= task->next_run_time) {
            if (task->priority > highest_priority) {
                highest_priority = task->priority;
                highest_priority_task = i;
            }
        }
    }
    
    return highest_priority_task;
}

/**
 * @brief 运行任务
 */
static void TaskScheduler_RunTask(uint8_t task_id)
{
    if (task_id >= MAX_TASKS) {
        return;
    }
    
    task_control_block_t* task = &tasks[task_id];
    if (task->function == NULL || !task->enabled) {
        return;
    }
    
    uint32_t start_time = micros();
    task->last_run_time = millis();
    task->state = TASK_STATE_RUNNING;
    
    /* 执行任务函数 */
    task->function(task->parameters);
    
    uint32_t exec_time = micros() - start_time;
    task->total_exec_time += exec_time;
    if (exec_time > task->max_exec_time) {
        task->max_exec_time = exec_time;
    }
    
    task->run_count++;
    task->state = TASK_STATE_READY;
    
    /* 计算下次运行时间 */
    if (task->period_ms > 0) {
        task->next_run_time = task->last_run_time + task->period_ms;
    }
}

/**
 * @brief 更新统计信息
 */
static void TaskScheduler_UpdateStats(void)
{
    static uint32_t last_stats_update = 0;
    uint32_t current_time = millis();
    
    if (current_time - last_stats_update >= 1000) { // 每秒更新一次
        last_stats_update = current_time;
        
        /* 计算CPU使用率 */
        uint32_t total_time = scheduler_stats.busy_time_us + scheduler_stats.idle_time_us;
        if (total_time > 0) {
            scheduler_stats.cpu_usage_percent = (scheduler_stats.busy_time_us * 100) / total_time;
        }
        
        /* 重置计时器 */
        scheduler_stats.busy_time_us = 0;
        scheduler_stats.idle_time_us = 0;
    }
}
