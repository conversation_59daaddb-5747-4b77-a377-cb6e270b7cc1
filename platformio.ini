; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env_common]
lib_deps_common = 
	olikraus/U8g2@^2.32.15
	madhephaestus/ESP32Encoder@^0.9.0
	ottowinter/ESPAsyncWebServer-esphome @ 3.0.0

[env_common_s]
lib_deps_common = 
	

[env:esp32dev]
extends = env_common
platform = espressif32
board = esp32dev
framework = arduino
;monitor_filters=hexlify
monitor_speed = 115200
upload_port = COM10
monitor_port = COM10
build_flags = 
	-DTARGET_ESP32
lib_deps =
	${env_common.lib_deps_common}

[env:esp32_S2]
extends = env_common
platform = espressif32
board = nodemcu-32-s2 ; ~/.platformio/platforms/espressif32/boards/
board_build.mcu = esp32s2
framework = arduino
monitor_speed = 115200
upload_port = /dev/cu.usbserial-1410
monitor_port = /dev/cu.usbserial-1410
build_flags = 
	-DTARGET_ESP32_S
	-DTARGET_ESP32_S2
lib_deps = 
	${env_common.lib_deps_common}
	${env_common_s.lib_deps_common}

[env:esp32_S3]
extends = env_common
platform = espressif32
board = nodemcu-32-s3 ; ~/.platformio/platforms/espressif32/boards/
board_build.mcu = esp32s3
framework = arduino
monitor_speed = 115200
upload_port = /dev/cu.usbserial-1460
monitor_port = /dev/cu.usbserial-1460
build_flags = 
	-DTARGET_ESP32_S
	-DTARGET_ESP32_S3
lib_deps =
	${env_common.lib_deps_common}
	${env_common_s.lib_deps_common}
