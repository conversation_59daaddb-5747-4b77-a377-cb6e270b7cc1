#!/usr/bin/env python3
"""
验证IOC配置文件的完整性和正确性
"""

import re
import sys
from pathlib import Path

def verify_ioc_config(ioc_file_path):
    """验证IOC配置文件"""
    ioc_path = Path(ioc_file_path)
    
    if not ioc_path.exists():
        print(f"❌ IOC文件不存在: {ioc_file_path}")
        return False
    
    print(f"🔍 验证IOC配置文件: {ioc_file_path}")
    print("=" * 60)
    
    with open(ioc_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查基本配置
    checks = {
        'MCU配置': r'Mcu\.Family=STM32F0',
        'ADC配置': r'ADC\..*=',
        'GPIO配置': r'P[A-F]\d+\..*=',
        '时钟配置': r'RCC\..*=',
        'USB配置': r'USB.*=',
        'I2C配置': r'I2C.*=',
        'TIM配置': r'TIM.*=',
    }
    
    results = {}
    for check_name, pattern in checks.items():
        matches = re.findall(pattern, content)
        results[check_name] = len(matches)
        print(f"✅ {check_name}: {len(matches)} 项配置")
    
    # 检查引脚配置
    pin_configs = re.findall(r'(P[A-F]\d+)\..*=', content)
    unique_pins = set(pin_configs)
    print(f"\n📌 配置的引脚总数: {len(unique_pins)}")
    
    # 按端口分组显示
    ports = {}
    for pin in unique_pins:
        port = pin[:2]  # PA, PB, PC, etc.
        if port not in ports:
            ports[port] = []
        ports[port].append(pin)
    
    for port, pins in sorted(ports.items()):
        print(f"   {port}: {len(pins)} 个引脚 - {', '.join(sorted(pins))}")
    
    # 检查特殊引脚
    special_pins = re.findall(r'(P[A-F]\d+-[A-Z_]+)\..*=', content)
    if special_pins:
        print(f"\n🔧 特殊功能引脚: {len(set(special_pins))}")
        for pin in sorted(set(special_pins)):
            print(f"   {pin}")
    
    # 检查项目配置
    project_configs = re.findall(r'ProjectManager\..*=(.+)', content)
    if project_configs:
        print(f"\n⚙️ 项目配置:")
        for config in project_configs[:5]:  # 只显示前5个
            print(f"   {config}")
    
    print(f"\n✅ IOC配置验证完成")
    return True

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python verify_ioc_config.py <ioc_file_path>")
        sys.exit(1)
    
    ioc_file = sys.argv[1]
    verify_ioc_config(ioc_file)
