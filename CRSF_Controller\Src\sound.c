/**
 * @file sound.c
 * @brief 音效系统实现 (基于deviation设计移植)
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "sound.h"
#include "clock_system.h"
#include "hal_drivers.h"

/* 全局变量定义 */
TIM_HandleTypeDef htim_sound;
TIM_HandleTypeDef htim_vibrator;

/* 私有变量 */
static sound_callback_t sound_callback = NULL;
static bool sound_initialized = false;
static bool vibrator_initialized = false;

/**
 * @brief 音效系统初始化 (移植自deviation)
 */
void SOUND_Init(void)
{
    if (sound_initialized) {
        return;
    }

    /* 使能定时器时钟 */
    __HAL_RCC_TIM1_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();

    /* 配置GPIO */
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = SOUND_GPIO_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = SOUND_GPIO_AF;
    HAL_GPIO_Init(SOUND_GPIO_PORT, &GPIO_InitStruct);

    /* 定时器基本配置 */
    htim_sound.Instance = SOUND_TIM;
    htim_sound.Init.Prescaler = (HAL_RCC_GetPCLK2Freq() / 12000000) - 1;  // 12MHz时钟
    htim_sound.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim_sound.Init.Period = 65535;  // 最大周期
    htim_sound.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim_sound.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;

    if (HAL_TIM_PWM_Init(&htim_sound) != HAL_OK) {
        Error_Handler();
    }

    /* PWM通道配置 */
    TIM_OC_InitTypeDef sConfigOC = {0};
    sConfigOC.OCMode = TIM_OCMODE_PWM1;
    sConfigOC.Pulse = 0x8000;  // 50%占空比
    sConfigOC.OCPolarity = TIM_OCPOLARITY_LOW;  // 低电平有效
    sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;

    if (HAL_TIM_PWM_ConfigChannel(&htim_sound, &sConfigOC, SOUND_CHANNEL) != HAL_OK) {
        Error_Handler();
    }

    sound_initialized = true;

    /* 初始化震动电机 */
    VIBRATINGMOTOR_Init();
}

/**
 * @brief 震动电机初始化
 */
void VIBRATINGMOTOR_Init(void)
{
    if (vibrator_initialized) {
        return;
    }

    /* 使能定时器时钟 */
    __HAL_RCC_TIM3_CLK_ENABLE();
    __HAL_RCC_GPIOE_CLK_ENABLE();

    /* 配置GPIO */
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = VIBRATOR_GPIO_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = VIBRATOR_GPIO_AF;
    HAL_GPIO_Init(VIBRATOR_GPIO_PORT, &GPIO_InitStruct);

    /* 定时器基本配置 */
    htim_vibrator.Instance = VIBRATOR_TIM;
    htim_vibrator.Init.Prescaler = (HAL_RCC_GetPCLK1Freq() / 1000000) - 1;  // 1MHz
    htim_vibrator.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim_vibrator.Init.Period = 1000;  // 1kHz PWM
    htim_vibrator.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim_vibrator.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;

    if (HAL_TIM_PWM_Init(&htim_vibrator) != HAL_OK) {
        Error_Handler();
    }

    /* PWM通道配置 */
    TIM_OC_InitTypeDef sConfigOC = {0};
    sConfigOC.OCMode = TIM_OCMODE_PWM1;
    sConfigOC.Pulse = 0;  // 初始关闭
    sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
    sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;

    if (HAL_TIM_PWM_ConfigChannel(&htim_vibrator, &sConfigOC, VIBRATOR_CHANNEL) != HAL_OK) {
        Error_Handler();
    }

    vibrator_initialized = true;
}

/**
 * @brief 设置音效频率和音量 (移植自deviation)
 */
void SOUND_SetFrequency(uint32_t frequency, uint32_t volume)
{
    if (!sound_initialized) {
        return;
    }

    if (frequency == 0) {
        volume = 0;
        frequency = 220;  // 默认频率
    }

    if (volume == 0) {
        /* 停止PWM输出但保持定时器运行 */
        HAL_TIM_PWM_Stop(&htim_sound, SOUND_CHANNEL);
    } else {
        /* 启动PWM输出 */
        HAL_TIM_PWM_Start(&htim_sound, SOUND_CHANNEL);
    }

    /* 计算周期和占空比 */
    /* period = 12000000 / frequency */
    uint32_t period = 12000000 / frequency;
    
    /* 使用三次方来近似指数音量控制 */
    uint32_t duty_cycle = (period >> 1) * volume / 100 * volume / 100 * volume / 100;

    /* 更新定时器参数 */
    __HAL_TIM_SET_AUTORELOAD(&htim_sound, period);
    __HAL_TIM_SET_COMPARE(&htim_sound, SOUND_CHANNEL, duty_cycle);
}

/**
 * @brief 启动音效 (带震动)
 */
void SOUND_Start(uint32_t msec, sound_callback_t next_note_cb, uint8_t vibrate)
{
    SOUND_StartWithoutVibrating(msec, next_note_cb);
    if (vibrate) {
        /* 使用时钟系统控制震动时长 */
        CLOCK_StartVibrator(msec);
    }
}

/**
 * @brief 启动音效 (不带震动)
 */
void SOUND_StartWithoutVibrating(uint32_t msec, sound_callback_t next_note_cb)
{
    if (!sound_initialized) {
        return;
    }

    /* 设置回调和启动定时器 */
    CLOCK_SetCallback(PRIORITY_SOUND, msec);
    sound_callback = next_note_cb;
    
    /* 启动定时器 */
    HAL_TIM_Base_Start(&htim_sound);
}

/**
 * @brief 停止音效
 */
void SOUND_Stop(void)
{
    if (!sound_initialized) {
        return;
    }

    /* 清除回调 */
    CLOCK_ClearCallback(PRIORITY_SOUND);
    sound_callback = NULL;

    /* 停止定时器和PWM */
    HAL_TIM_Base_Stop(&htim_sound);
    HAL_TIM_PWM_Stop(&htim_sound, SOUND_CHANNEL);

    /* 停止震动 */
    CLOCK_StopVibrator();
}

/**
 * @brief 启动震动电机
 */
void VIBRATINGMOTOR_Start(void)
{
    if (!vibrator_initialized) {
        return;
    }

    /* 设置50%占空比 */
    __HAL_TIM_SET_COMPARE(&htim_vibrator, VIBRATOR_CHANNEL, 500);
    HAL_TIM_PWM_Start(&htim_vibrator, VIBRATOR_CHANNEL);
}

/**
 * @brief 停止震动电机
 */
void VIBRATINGMOTOR_Stop(void)
{
    if (!vibrator_initialized) {
        return;
    }

    HAL_TIM_PWM_Stop(&htim_vibrator, VIBRATOR_CHANNEL);
    __HAL_TIM_SET_COMPARE(&htim_vibrator, VIBRATOR_CHANNEL, 0);
}

/**
 * @brief 音效回调处理 (移植自deviation)
 */
uint32_t SOUND_Callback(void)
{
    if (sound_callback == NULL) {
        /* 允许单音调播放 */
        SOUND_Stop();
        return 0;
    }

    uint32_t msec = sound_callback();
    if (!msec) {
        SOUND_Stop();
    }
    return msec;
}

/**
 * @brief 播放单音调
 */
void SOUND_PlayTone(uint32_t frequency, uint32_t duration_ms)
{
    SOUND_SetFrequency(frequency, SOUND_VOLUME_MID);
    SOUND_StartWithoutVibrating(duration_ms, NULL);
}

/**
 * @brief 预定义音效
 */
void SOUND_PlayBeep(void)
{
    SOUND_Start(SOUND_DURATION_SHORT, SOUND_BeepSequence, 0);
}

void SOUND_PlayDoubleBeep(void)
{
    SOUND_Start(SOUND_DURATION_SHORT, SOUND_BeepSequence, 0);
}

void SOUND_PlaySuccess(void)
{
    SOUND_Start(SOUND_DURATION_SHORT, SOUND_SuccessSequence, 1);
}

void SOUND_PlayWarning(void)
{
    SOUND_Start(SOUND_DURATION_MID, SOUND_WarningSequence, 1);
}

void SOUND_PlayError(void)
{
    SOUND_Start(SOUND_DURATION_LONG, SOUND_ErrorSequence, 1);
}

void SOUND_PlayStartup(void)
{
    SOUND_Start(SOUND_DURATION_MID, SOUND_StartupSequence, 1);
}

void SOUND_PlayShutdown(void)
{
    SOUND_Start(SOUND_DURATION_LONG, SOUND_ShutdownSequence, 1);
}

/**
 * @brief 音效序列回调函数
 */
uint16_t SOUND_BeepSequence(void)
{
    static uint8_t step = 0;
    
    switch (step++) {
        case 0:
            SOUND_SetFrequency(SOUND_FREQ_BEEP, SOUND_VOLUME_MID);
            return SOUND_DURATION_SHORT;
        default:
            step = 0;
            return 0;  // 结束
    }
}

uint16_t SOUND_SuccessSequence(void)
{
    static uint8_t step = 0;
    
    switch (step++) {
        case 0:
            SOUND_SetFrequency(SOUND_FREQ_C4, SOUND_VOLUME_MID);
            return 100;
        case 1:
            SOUND_SetFrequency(SOUND_FREQ_E4, SOUND_VOLUME_MID);
            return 100;
        case 2:
            SOUND_SetFrequency(SOUND_FREQ_G4, SOUND_VOLUME_MID);
            return 200;
        default:
            step = 0;
            return 0;
    }
}

uint16_t SOUND_WarningSequence(void)
{
    static uint8_t step = 0;
    
    switch (step++) {
        case 0:
        case 2:
            SOUND_SetFrequency(SOUND_FREQ_WARNING, SOUND_VOLUME_HIGH);
            return 200;
        case 1:
        case 3:
            SOUND_SetFrequency(0, 0);  // 静音
            return 100;
        default:
            step = 0;
            return 0;
    }
}

uint16_t SOUND_ErrorSequence(void)
{
    static uint8_t step = 0;
    
    switch (step++) {
        case 0:
        case 2:
        case 4:
            SOUND_SetFrequency(SOUND_FREQ_ERROR, SOUND_VOLUME_MAX);
            return 150;
        case 1:
        case 3:
        case 5:
            SOUND_SetFrequency(0, 0);
            return 100;
        default:
            step = 0;
            return 0;
    }
}

uint16_t SOUND_StartupSequence(void)
{
    static uint8_t step = 0;
    
    switch (step++) {
        case 0:
            SOUND_SetFrequency(SOUND_FREQ_C4, SOUND_VOLUME_LOW);
            return 150;
        case 1:
            SOUND_SetFrequency(SOUND_FREQ_E4, SOUND_VOLUME_MID);
            return 150;
        case 2:
            SOUND_SetFrequency(SOUND_FREQ_G4, SOUND_VOLUME_HIGH);
            return 300;
        default:
            step = 0;
            return 0;
    }
}

uint16_t SOUND_ShutdownSequence(void)
{
    static uint8_t step = 0;

    switch (step++) {
        case 0:
            SOUND_SetFrequency(SOUND_FREQ_G4, SOUND_VOLUME_HIGH);
            return 150;
        case 1:
            SOUND_SetFrequency(SOUND_FREQ_E4, SOUND_VOLUME_MID);
            return 150;
        case 2:
            SOUND_SetFrequency(SOUND_FREQ_C4, SOUND_VOLUME_LOW);
            return 300;
        default:
            step = 0;
            return 0;
    }
}

/**
 * @brief 高级震动控制函数
 */
void VIBRATOR_Pulse(uint32_t duration_ms)
{
    CLOCK_StartVibrator(duration_ms);
}

void VIBRATOR_DoublePulse(void)
{
    /* 使用音效系统实现双震动模式 */
    static uint8_t double_step = 0;

    switch (double_step++) {
        case 0:
            CLOCK_StartVibrator(100);  // 第一次震动100ms
            /* 设置定时器在150ms后触发第二次震动 */
            CLOCK_SetCallback(PRIORITY_SOUND, 150);
            break;
        case 1:
            CLOCK_StartVibrator(100);  // 第二次震动100ms
            double_step = 0;  // 重置
            break;
    }
}

void VIBRATOR_TriplePulse(void)
{
    /* 使用音效系统实现三震动模式 */
    static uint8_t triple_step = 0;

    switch (triple_step++) {
        case 0:
            CLOCK_StartVibrator(80);   // 第一次震动
            CLOCK_SetCallback(PRIORITY_SOUND, 120);
            break;
        case 1:
            CLOCK_StartVibrator(80);   // 第二次震动
            CLOCK_SetCallback(PRIORITY_SOUND, 120);
            break;
        case 2:
            CLOCK_StartVibrator(80);   // 第三次震动
            triple_step = 0;  // 重置
            break;
    }
}

void VIBRATOR_Pattern(uint32_t* pattern, uint8_t count)
{
    /* 自定义震动模式 */
    static uint8_t pattern_step = 0;
    static uint32_t* current_pattern = NULL;
    static uint8_t pattern_count = 0;

    if (pattern && count > 0) {
        /* 开始新模式 */
        current_pattern = pattern;
        pattern_count = count;
        pattern_step = 0;
    }

    if (current_pattern && pattern_step < pattern_count) {
        if (pattern_step % 2 == 0) {
            /* 偶数步骤：震动 */
            CLOCK_StartVibrator(current_pattern[pattern_step]);
        } else {
            /* 奇数步骤：暂停 */
            CLOCK_StopVibrator();
        }

        pattern_step++;
        if (pattern_step < pattern_count) {
            CLOCK_SetCallback(PRIORITY_SOUND, current_pattern[pattern_step - 1]);
        } else {
            /* 模式结束 */
            current_pattern = NULL;
            pattern_count = 0;
            pattern_step = 0;
        }
    }
}
