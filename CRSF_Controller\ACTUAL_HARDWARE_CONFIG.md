# CRSF控制器实际硬件配置

## 📋 **完整引脚配置表**

### **按键输入 (GPIO输入，上拉)**
| 引脚 | 功能 | 描述 | 菜单模式功能 |
|------|------|------|-------------|
| PC9  | KEY0 | 一键起落按键 | 确定键 |
| PC8  | KEY1 | 双控对频按键 | 退出键 |
| PC7  | KEY2 | 自动返航按键 | 上键 |
| PC6  | KEY3 | 起落架收放 | 下键 |
| PD15 | KEY4 | 快门键 | 右键 |
| PD14 | KEY5 | 照片/视频切换 | 左键 |
| PD11 | PWR_SW | 电源开关按键 | - |

### **ADC输入 (模拟输入)**
| 引脚 | ADC通道 | 功能 | 描述 |
|------|---------|------|------|
| PA0  | ADC_CHANNEL_0 | CH1 | 摇杆通道1 |
| PA1  | ADC_CHANNEL_1 | CH2 | 摇杆通道2 |
| PA2  | ADC_CHANNEL_2 | CH3 | 摇杆通道3 |
| PA3  | ADC_CHANNEL_3 | CH4 | 摇杆通道4 |
| PA4  | ADC_CHANNEL_4 | SWA | 三段开关A |
| PA5  | ADC_CHANNEL_5 | SWB | 三段开关B |
| PC0  | ADC_CHANNEL_10 | VBAT | 电池电压 |
| PC1  | ADC_CHANNEL_11 | BIN | 外接电源检测 |
| PC2  | ADC_CHANNEL_12 | VRA | 电位器A |
| PC3  | ADC_CHANNEL_13 | VRB | 电位器B |

### **PWM输出**
| 引脚 | 定时器 | 功能 | 描述 |
|------|--------|------|------|
| PA8  | TIM1_CH1 | BUZZER_PWM | 蜂鸣器PWM输出 |
| PE3  | TIM3_CH1 | VIBRATOR_PWM | 震动电机PWM输出 |

### **数字输出**
| 引脚 | 功能 | 描述 |
|------|------|------|
| PD12 | PWR_OFF | 电源开关控制 |
| PB8  | LED4 | 蓝色LED |
| PC4  | LED5 | 红色LED |

### **数字输入**
| 引脚 | 功能 | 描述 |
|------|------|------|
| PD13 | STDBY | 充电STDBY引脚 |

### **通信接口**
| 引脚 | 功能 | 描述 |
|------|------|------|
| PA9  | USART1_TX | CRSF发送 |
| PA10 | USART1_RX | CRSF接收 |
| PB10 | I2C2_SCL | I2C时钟 (OLED + EEPROM) |
| PB11 | I2C2_SDA | I2C数据 (OLED + EEPROM) |
| PA11 | USB_DM | USB数据- (同时作为充电口) |
| PA12 | USB_DP | USB数据+ (同时作为充电口) |

### **外部中断**
| 引脚 | 中断线 | 功能 | 描述 |
|------|--------|------|------|
| PB1  | EXTI1 | 混音器触发 | 混音器计算触发 |
| PB3  | EXTI3 | 一次性任务 | 一次性任务处理 |
| PB4  | EXTI4 | UART处理 | UART数据处理 |

### **调试接口**
| 引脚 | 功能 | 描述 |
|------|------|------|
| PA13 | SWDIO | SWD数据 |
| PA14 | SWCLK | SWD时钟 |

### **时钟源**
| 引脚 | 功能 | 描述 |
|------|------|------|
| PF0  | OSC_IN | 8MHz主晶振输入 |
| PF1  | OSC_OUT | 8MHz主晶振输出 |
| PC14 | OSC32_IN | 32K RTC晶振输入 |
| PC15 | OSC32_OUT | 32K RTC晶振输出 |

## 🎮 **按键功能映射**

### **正常模式**
- **KEY0 (PC9)**: 一键起落
- **KEY1 (PC8)**: 双控对频
- **KEY2 (PC7)**: 自动返航
- **KEY3 (PC6)**: 起落架收放 (长按收起，短按放下)
- **KEY4 (PD15)**: 快门
- **KEY5 (PD14)**: 照片/视频切换
- **PWR_SW (PD11)**: 电源开关

### **菜单模式**
- **KEY0 (PC9)**: 确定键
- **KEY1 (PC8)**: 退出键
- **KEY2 (PC7)**: 上键
- **KEY3 (PC6)**: 下键
- **KEY4 (PD15)**: 右键
- **KEY5 (PD14)**: 左键

### **组合键**
- **KEY4 + KEY5**: 进入/退出菜单模式

## 🔌 **外设连接**

### **I2C总线 (I2C2)**
- **SSD1306 OLED显示屏**: 地址 0x3C
- **FT24C128A EEPROM**: 地址 0x50

### **USB接口**
- **功能**: USB通信 + 充电口
- **外接电源检测**: PC1 (BIN)
- **充电状态**: PD13 (STDBY)

### **音频反馈**
- **蜂鸣器**: PA8 (TIM1_CH1 PWM)
- **震动电机**: PE3 (TIM3_CH1 PWM)

### **状态指示**
- **蓝色LED**: PB8 (LED4)
- **红色LED**: PC4 (LED5)

## ⚙️ **配置验证**

所有引脚配置已通过自动验证脚本验证：
```bash
python Scripts/verify_pinout_config.py STM32CubeMX/CRSF_Controller.ioc
```

**验证结果**: ✅ 29/29 个关键引脚配置正确

## 📝 **注意事项**

1. **按键配置**: 所有按键使用内部上拉，按下时为低电平
2. **ADC采样**: 10个ADC通道用于摇杆、开关和电压监测
3. **PWM输出**: 蜂鸣器和震动电机使用不同的定时器
4. **I2C总线**: 共享给OLED显示和EEPROM存储
5. **USB功能**: 同时支持通信和充电
6. **外部中断**: 用于高优先级任务处理

## 🔄 **与原配置的差异**

此配置基于用户提供的实际硬件信息，与之前基于引脚图的推测配置有以下主要差异：

1. **按键布局**: 实际使用PC6-PC9 + PD14-PD15，而非PE0-PE5
2. **蜂鸣器位置**: 实际在PA8，而非PB8
3. **震动电机**: 实际在PE3 (TIM3_CH1)
4. **电源控制**: 实际使用PD11-PD13，而非PE6-PE8
5. **LED配置**: 实际使用PB8和PC4

所有配置现已更新为实际硬件配置，确保软硬件完全匹配。
