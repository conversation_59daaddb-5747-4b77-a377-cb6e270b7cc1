# CRSF Controller 完整校准指南

## 📖 **校准概述**

CRSF Controller的校准系统现在支持**完整的8通道校准**，包括：
- **4个摇杆通道** (CH1-CH4): 右摇杆X/Y，左摇杆X/Y
- **2个电位器通道** (VRA/VRB): 可调电位器A和B
- **2个开关通道** (SWA/SWB): 三段开关A和B

## 🎯 **校准通道详解**

### 摇杆通道 (ADC_CH1 - ADC_CH4)
```c
ADC_CH1 (PA0): 右摇杆X轴 - 副翼控制 (Aileron)
ADC_CH2 (PA1): 右摇杆Y轴 - 升降控制 (Elevator)  
ADC_CH3 (PA2): 左摇杆Y轴 - 油门控制 (Throttle)
ADC_CH4 (PA3): 左摇杆X轴 - 方向控制 (Rudder)

校准参数:
- 中心值: 摇杆居中时的ADC值
- 最小值: 摇杆推到极限位置的最小ADC值
- 最大值: 摇杆推到极限位置的最大ADC值
- 死区: 20 (中心附近的死区范围)
```

### 电位器通道 (ADC_VRA - ADC_VRB) ⭐ **新增**
```c
ADC_VRA (PA6): 电位器A - 可调参数1 (如舵机行程、PID参数)
ADC_VRB (PA7): 电位器B - 可调参数2 (如相机云台、LED亮度)

校准参数:
- 中心值: 电位器居中时的ADC值
- 最小值: 电位器逆时针转到底的ADC值
- 最大值: 电位器顺时针转到底的ADC值
- 死区: 5 (小死区，保证精确控制)
```

### 开关通道 (ADC_SWA - ADC_SWB)
```c
ADC_SWA (PA4): 三段开关A - 飞行模式切换
ADC_SWB (PA5): 三段开关B - 辅助功能切换

校准参数:
- 下位值: 开关拨到下位时的ADC值
- 中位值: 开关拨到中位时的ADC值  
- 上位值: 开关拨到上位时的ADC值
- 死区: 0 (开关无死区)
```

## 🔄 **校准流程**

### 完整校准步骤 (8步)
```
步骤1: 摇杆居中     → 将4个摇杆置于中心位置
步骤2: 摇杆全行程   → 转动摇杆到各个极限位置
步骤3: 电位器居中   → 将VRA/VRB电位器置于中心位置  ⭐ 新增
步骤4: 电位器全行程 → 转动电位器到各个极限位置    ⭐ 新增
步骤5: 开关下位     → 将SWA/SWB开关拨到下位
步骤6: 开关中位     → 将SWA/SWB开关拨到中位
步骤7: 开关上位     → 将SWA/SWB开关拨到上位
步骤8: 保存数据     → 自动保存校准数据到EEPROM
```

### 开机自动校准
```c
触发条件:
- 首次开机 (EEPROM中无校准数据)
- 校准数据损坏 (CRC校验失败)
- 手动触发 (开机时按住KEY1+KEY2)

自动处理:
1. 自动采集摇杆中心位置 (保持摇杆居中2秒)
2. 自动采集电位器当前位置作为中心位置 ⭐ 新增
3. 提示用户进行全行程校准
4. 自动保存校准数据
```

### 手动精确校准
```c
进入方式:
- 菜单 → 校准设置 → 开始校准
- 开机时按住KEY1+KEY2+KEY3

手动步骤:
1. 按照屏幕提示逐步操作
2. 每步完成后按确认键继续
3. 可以随时按取消键退出
4. 支持重新校准单个步骤
```

## 🛠️ **校准代码实现**

### 电位器校准处理函数
```c
static void Calibration_ProcessPotentiometerCalibration(void)
{
    uint16_t vra_value = ADC_Input_GetRawValue(ADC_VRA);
    uint16_t vrb_value = ADC_Input_GetRawValue(ADC_VRB);
    
    if (calib_context.step == CALIB_STEP_CENTER_POTS) {
        /* 电位器居中校准 */
        if (电位器稳定2秒) {
            pot_center_values[0] = vra_value;
            pot_center_values[1] = vrb_value;
            
            /* 初始化最大最小值 */
            pot_min_values[0] = pot_max_values[0] = vra_value;
            pot_min_values[1] = pot_max_values[1] = vrb_value;
            
            /* 进入全行程校准 */
            calib_context.step = CALIB_STEP_MOVE_POTS;
        }
    } else if (calib_context.step == CALIB_STEP_MOVE_POTS) {
        /* 电位器全行程校准 */
        
        /* 更新最大最小值 */
        if (vra_value < pot_min_values[0]) pot_min_values[0] = vra_value;
        if (vra_value > pot_max_values[0]) pot_max_values[0] = vra_value;
        if (vrb_value < pot_min_values[1]) pot_min_values[1] = vrb_value;
        if (vrb_value > pot_max_values[1]) pot_max_values[1] = vrb_value;
        
        /* 检查是否完成全行程校准 */
        if (Calibration_CheckPotentiometerMovement()) {
            calib_context.step = CALIB_STEP_SWITCH_LOW;
        }
    }
}
```

### 电位器行程检查函数
```c
static bool Calibration_CheckPotentiometerMovement(void)
{
    /* 检查电位器行程是否足够 */
    uint16_t vra_range = pot_max_values[0] - pot_min_values[0];
    uint16_t vrb_range = pot_max_values[1] - pot_min_values[1];
    
    bool vra_sufficient = vra_range > CALIB_MIN_RANGE;  // 最小行程要求
    bool vrb_sufficient = vrb_range > CALIB_MIN_RANGE;
    
    if (vra_sufficient && vrb_sufficient) {
        if (稳定2秒) {
            return true;  // 校准完成
        }
    }
    
    return false;
}
```

### 校准数据保存
```c
void Calibration_SaveData(void)
{
    calibration_data_t calib_data;
    
    /* 保存摇杆校准数据 (CH1-CH4) */
    for (uint8_t ch = 0; ch < 4; ch++) {
        calib_data.adc_calibration[ch].min_value = stick_min_values[ch];
        calib_data.adc_calibration[ch].max_value = stick_max_values[ch];
        calib_data.adc_calibration[ch].center_value = stick_center_values[ch];
        calib_data.adc_calibration[ch].deadband = 20;  // 摇杆死区
    }
    
    /* 保存开关校准数据 (SWA/SWB) */
    for (uint8_t ch = 4; ch < 6; ch++) {
        calib_data.adc_calibration[ch].min_value = switch_positions[ch-4][0];
        calib_data.adc_calibration[ch].center_value = switch_positions[ch-4][1];
        calib_data.adc_calibration[ch].max_value = switch_positions[ch-4][2];
        calib_data.adc_calibration[ch].deadband = 0;  // 开关无死区
    }
    
    /* 保存电位器校准数据 (VRA/VRB) ⭐ 新增 */
    for (uint8_t ch = 6; ch < 8; ch++) {
        calib_data.adc_calibration[ch].min_value = pot_min_values[ch-6];
        calib_data.adc_calibration[ch].center_value = pot_center_values[ch-6];
        calib_data.adc_calibration[ch].max_value = pot_max_values[ch-6];
        calib_data.adc_calibration[ch].deadband = 5;  // 电位器小死区
    }
    
    /* 保存到EEPROM */
    EEPROM_WriteCalibration(&calib_data);
}
```

## 📱 **用户界面**

### 校准界面显示
```
┌─────────────────────────────┐
│ 步骤3: 电位器居中           │
├─────────────────────────────┤
│ 将电位器VRA/VRB置于中心位置 │
│                             │
│ VRA: [====●====] 2048       │
│ VRB: [===●=====] 1856       │
│                             │
│ 请保持位置稳定...           │
│ 剩余时间: 2秒               │
└─────────────────────────────┘

┌─────────────────────────────┐
│ 步骤4: 电位器全行程         │
├─────────────────────────────┤
│ 转动电位器到各个极限位置    │
│                             │
│ VRA: [●─────────] 156-3890  │
│ VRB: [──────●───] 234-3654  │
│                             │
│ 行程: VRA ✓  VRB ✓         │
│ 完成后自动进入下一步        │
└─────────────────────────────┘
```

### 校准状态指示
```c
校准状态LED:
- LED1: 电源指示 (常亮)
- LED2: 校准进行中 (闪烁)
- LED3: 当前步骤完成 (短亮)
- LED4: 校准错误 (快闪)

音效提示:
- 开始校准: 双音调
- 步骤完成: 短哔声
- 校准完成: 成功音效
- 校准错误: 错误音效

震动反馈:
- 步骤切换: 单次震动
- 校准完成: 双次震动
- 校准错误: 三次震动
```

## 🔧 **校准验证**

### 校准数据检查
```c
bool Calibration_ValidateData(void)
{
    /* 检查摇杆校准数据 */
    for (uint8_t ch = 0; ch < 4; ch++) {
        if (stick_max_values[ch] - stick_min_values[ch] < CALIB_MIN_RANGE) {
            return false;  // 行程不足
        }
        if (stick_center_values[ch] < stick_min_values[ch] || 
            stick_center_values[ch] > stick_max_values[ch]) {
            return false;  // 中心值异常
        }
    }
    
    /* 检查电位器校准数据 ⭐ 新增 */
    for (uint8_t ch = 0; ch < 2; ch++) {
        if (pot_max_values[ch] - pot_min_values[ch] < CALIB_MIN_RANGE) {
            return false;  // 行程不足
        }
        if (pot_center_values[ch] < pot_min_values[ch] || 
            pot_center_values[ch] > pot_max_values[ch]) {
            return false;  // 中心值异常
        }
    }
    
    /* 检查开关校准数据 */
    for (uint8_t ch = 0; ch < 2; ch++) {
        if (switch_positions[ch][0] >= switch_positions[ch][1] ||
            switch_positions[ch][1] >= switch_positions[ch][2]) {
            return false;  // 开关位置异常
        }
    }
    
    return true;
}
```

### 校准精度测试
```c
void Calibration_TestAccuracy(void)
{
    /* 测试摇杆精度 */
    for (uint8_t ch = 0; ch < 4; ch++) {
        uint16_t raw = ADC_Input_GetRawValue(ch);
        uint16_t calibrated = ADC_Input_GetCalibratedValue(ch);
        printf("CH%d: raw=%d, cal=%d\n", ch+1, raw, calibrated);
    }
    
    /* 测试电位器精度 ⭐ 新增 */
    uint16_t vra_raw = ADC_Input_GetRawValue(ADC_VRA);
    uint16_t vra_cal = ADC_Input_GetCalibratedValue(ADC_VRA);
    uint16_t vrb_raw = ADC_Input_GetRawValue(ADC_VRB);
    uint16_t vrb_cal = ADC_Input_GetCalibratedValue(ADC_VRB);
    
    printf("VRA: raw=%d, cal=%d\n", vra_raw, vra_cal);
    printf("VRB: raw=%d, cal=%d\n", vrb_raw, vrb_cal);
    
    /* 测试开关状态 */
    printf("SWA: %s\n", Switch_GetPositionName(ADC_SWA));
    printf("SWB: %s\n", Switch_GetPositionName(ADC_SWB));
}
```

## 🎯 **校准总结**

### 更新内容 ⭐
1. **新增电位器校准**: VRA和VRB电位器的完整校准支持
2. **扩展校准步骤**: 从6步扩展到8步校准流程
3. **完善校准数据**: 支持8通道完整校准数据保存
4. **优化用户界面**: 添加电位器校准的界面提示
5. **增强数据验证**: 包含电位器数据的完整性检查

### 校准优势 ✅
- **完整覆盖**: 支持所有8个输入通道的校准
- **精确控制**: 电位器5级死区，摇杆20级死区
- **自动化程度高**: 开机自动校准，减少用户操作
- **数据安全**: EEPROM存储，CRC校验，多重备份
- **用户友好**: 清晰的界面提示和音效反馈

现在CRSF Controller的校准系统已经**完整支持所有输入通道**，为用户提供了专业级的校准体验！
