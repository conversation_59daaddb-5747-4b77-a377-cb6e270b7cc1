#!/usr/bin/env python3
"""
CRSF Controller 固件升级工具
支持通过USB CDC进行固件升级
"""

import sys
import time
import struct
import serial
import serial.tools.list_ports
from pathlib import Path
import argparse
import hashlib

# 升级命令定义
UPGRADE_CMD_ENTER = 0x55AA
UPGRADE_CMD_EXIT = 0xAA55
UPGRADE_CMD_ERASE = 0x1234
UPGRADE_CMD_WRITE = 0x5678
UPGRADE_CMD_VERIFY = 0x9ABC
UPGRADE_CMD_RESET = 0xDEF0

# 升级状态定义
UPGRADE_STATUS_IDLE = 0
UPGRADE_STATUS_READY = 1
UPGRADE_STATUS_ERASING = 2
UPGRADE_STATUS_WRITING = 3
UPGRADE_STATUS_VERIFYING = 4
UPGRADE_STATUS_COMPLETE = 5
UPGRADE_STATUS_ERROR = 6

# 错误代码定义
UPGRADE_ERROR_NONE = 0
UPGRADE_ERROR_INVALID_CMD = 1
UPGRADE_ERROR_INVALID_ADDR = 2
UPGRADE_ERROR_INVALID_SIZE = 3
UPGRADE_ERROR_ERASE_FAILED = 4
UPGRADE_ERROR_WRITE_FAILED = 5
UPGRADE_ERROR_VERIFY_FAILED = 6
UPGRADE_ERROR_TIMEOUT = 7

class FirmwareUploader:
    def __init__(self, port=None, baudrate=115200):
        self.port = port
        self.baudrate = baudrate
        self.serial = None
        self.chunk_size = 256
        
    def find_device(self):
        """自动查找CRSF Controller设备"""
        ports = serial.tools.list_ports.comports()
        for port in ports:
            if "CRSF Controller" in str(port.description) or \
               "STM32" in str(port.description):
                return port.device
        return None
    
    def connect(self):
        """连接设备"""
        if not self.port:
            self.port = self.find_device()
            if not self.port:
                print("错误: 未找到CRSF Controller设备")
                return False
        
        try:
            self.serial = serial.Serial(self.port, self.baudrate, timeout=5)
            print(f"已连接到设备: {self.port}")
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.serial:
            self.serial.close()
            self.serial = None
    
    def send_command(self, command, address=0, data=b''):
        """发送升级命令"""
        # 计算校验和
        checksum = sum(data) & 0xFFFFFFFF
        
        # 构建数据包
        packet = struct.pack('<HHII', command, len(data), address, checksum)
        packet += data
        packet += b'\x00' * (256 - len(data))  # 填充到256字节
        
        # 发送数据包
        self.serial.write(packet)
        self.serial.flush()
        
        # 等待响应
        response = self.serial.read(272)  # sizeof(upgrade_packet_t)
        if len(response) < 12:
            return None, UPGRADE_ERROR_TIMEOUT
        
        # 解析响应
        resp_cmd, resp_len, resp_addr, resp_error = struct.unpack('<HHII', response[:12])
        resp_data = response[12:12+resp_len] if resp_len > 0 else b''
        
        return resp_data, resp_error
    
    def enter_upgrade_mode(self, firmware_size):
        """进入升级模式"""
        print("进入升级模式...")
        data = struct.pack('<I', firmware_size)
        resp_data, error = self.send_command(UPGRADE_CMD_ENTER, data=data)
        
        if error != UPGRADE_ERROR_NONE:
            print(f"进入升级模式失败: 错误代码 {error}")
            return False
        
        print("已进入升级模式")
        return True
    
    def erase_flash(self):
        """擦除Flash"""
        print("擦除Flash...")
        resp_data, error = self.send_command(UPGRADE_CMD_ERASE)
        
        if error != UPGRADE_ERROR_NONE:
            print(f"擦除Flash失败: 错误代码 {error}")
            return False
        
        print("Flash擦除完成")
        return True
    
    def write_firmware(self, firmware_data):
        """写入固件"""
        print("写入固件...")
        
        total_size = len(firmware_data)
        written_size = 0
        address = 0x08002000  # APPLICATION_START_ADDR
        
        while written_size < total_size:
            # 计算本次写入大小
            chunk_size = min(self.chunk_size, total_size - written_size)
            chunk_data = firmware_data[written_size:written_size + chunk_size]
            
            # 发送写入命令
            resp_data, error = self.send_command(UPGRADE_CMD_WRITE, address + written_size, chunk_data)
            
            if error != UPGRADE_ERROR_NONE:
                print(f"写入失败: 地址 0x{address + written_size:08X}, 错误代码 {error}")
                return False
            
            written_size += chunk_size
            
            # 显示进度
            progress = (written_size * 100) // total_size
            print(f"\r写入进度: {progress}% ({written_size}/{total_size})", end='', flush=True)
        
        print("\n固件写入完成")
        return True
    
    def verify_firmware(self, firmware_data):
        """校验固件"""
        print("校验固件...")
        
        total_size = len(firmware_data)
        verified_size = 0
        address = 0x08002000  # APPLICATION_START_ADDR
        
        while verified_size < total_size:
            # 计算本次校验大小
            chunk_size = min(self.chunk_size, total_size - verified_size)
            chunk_data = firmware_data[verified_size:verified_size + chunk_size]
            
            # 发送校验命令
            resp_data, error = self.send_command(UPGRADE_CMD_VERIFY, address + verified_size, chunk_data)
            
            if error != UPGRADE_ERROR_NONE:
                print(f"校验失败: 地址 0x{address + verified_size:08X}, 错误代码 {error}")
                return False
            
            verified_size += chunk_size
            
            # 显示进度
            progress = (verified_size * 100) // total_size
            print(f"\r校验进度: {progress}% ({verified_size}/{total_size})", end='', flush=True)
        
        print("\n固件校验完成")
        return True
    
    def exit_upgrade_mode(self):
        """退出升级模式"""
        print("退出升级模式...")
        resp_data, error = self.send_command(UPGRADE_CMD_EXIT)
        
        if error != UPGRADE_ERROR_NONE:
            print(f"退出升级模式失败: 错误代码 {error}")
            return False
        
        print("已退出升级模式")
        return True
    
    def reset_device(self):
        """复位设备"""
        print("复位设备...")
        try:
            resp_data, error = self.send_command(UPGRADE_CMD_RESET)
            print("设备已复位")
        except:
            # 复位后设备会断开连接，这是正常的
            print("设备已复位")
    
    def upload_firmware(self, firmware_file):
        """上传固件"""
        # 读取固件文件
        try:
            with open(firmware_file, 'rb') as f:
                firmware_data = f.read()
        except Exception as e:
            print(f"读取固件文件失败: {e}")
            return False
        
        print(f"固件文件: {firmware_file}")
        print(f"固件大小: {len(firmware_data)} 字节")
        
        # 计算MD5
        md5_hash = hashlib.md5(firmware_data).hexdigest()
        print(f"固件MD5: {md5_hash}")
        
        # 连接设备
        if not self.connect():
            return False
        
        try:
            # 升级流程
            if not self.enter_upgrade_mode(len(firmware_data)):
                return False
            
            if not self.erase_flash():
                return False
            
            if not self.write_firmware(firmware_data):
                return False
            
            if not self.verify_firmware(firmware_data):
                return False
            
            if not self.exit_upgrade_mode():
                return False
            
            # 等待一下再复位
            time.sleep(1)
            self.reset_device()
            
            print("固件升级成功!")
            return True
            
        except Exception as e:
            print(f"升级过程中出错: {e}")
            return False
        
        finally:
            self.disconnect()

def main():
    parser = argparse.ArgumentParser(description='CRSF Controller 固件升级工具')
    parser.add_argument('firmware', help='固件文件路径 (.bin)')
    parser.add_argument('-p', '--port', help='串口设备 (自动检测)')
    parser.add_argument('-b', '--baudrate', type=int, default=115200, help='波特率 (默认: 115200)')
    
    args = parser.parse_args()
    
    # 检查固件文件
    if not Path(args.firmware).exists():
        print(f"错误: 固件文件不存在: {args.firmware}")
        return 1
    
    # 创建上传器
    uploader = FirmwareUploader(args.port, args.baudrate)
    
    # 上传固件
    if uploader.upload_firmware(args.firmware):
        return 0
    else:
        return 1

if __name__ == '__main__':
    sys.exit(main())
