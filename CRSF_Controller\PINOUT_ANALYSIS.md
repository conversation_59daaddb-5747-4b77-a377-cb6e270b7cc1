# STM32F072VBT6 引脚配置分析

## 📌 **实际硬件引脚配置** (用户提供)

### **ADC输入 (模拟输入)**
```c
// 左侧引脚 (ADC输入)
PA0  - ADC_IN0  (CH1 - 摇杆通道1)
PA1  - ADC_IN1  (CH2 - 摇杆通道2) 
PA2  - ADC_IN2  (CH3 - 摇杆通道3)
PA3  - ADC_IN3  (CH4 - 摇杆通道4)
PA4  - ADC_IN4  (SWA - 三段开关A)
PA5  - ADC_IN5  (SWB - 三段开关B)

// 底部引脚 (ADC输入)
PC0  - ADC_IN10 (VBAT - 电池电压)
PC1  - ADC_IN11 (BIN - 外接电源)
PC2  - ADC_IN12 (VRA - 电位器A)
PC3  - ADC_IN13 (VRB - 电位器B)
```

### **数字输入/输出**
```c
// 右侧引脚 (按键输入)
PC6  - KEY3 (下键)
PC7  - KEY2 (上键)  
PC8  - KEY1 (菜单键)
PC9  - KEY0 (确认键)

// 右侧引脚 (功能输出)
PB8  - TIM1_CH1 (蜂鸣器PWM)
PB9  - 未标记
PB10 - I2C2_SCL (OLED时钟)
PB11 - I2C2_SDA (OLED数据)

// 右侧引脚 (状态指示)
PE0  - KEY0 (重复?)
PE1  - KEY1 (重复?)
PE2  - KEY2 (重复?)
PE3  - KEY3 (重复?)
PE4  - KEY4
PE5  - KEY5
PE6  - STBY (待机)
PE7  - PWR_OFF (关机)
PE8  - PWR_ON (开机)
```

### **通信接口**
```c
// 顶部引脚 (UART)
PA9  - USART1_TX (CRSF发送)
PA10 - USART1_RX (CRSF接收)

// 顶部引脚 (USB)
PA11 - USB_DM
PA12 - USB_DP

// 顶部引脚 (调试)
PA13 - SWDIO
PA14 - SWCLK
```

### **时钟和电源**
```c
// 左侧引脚 (晶振)
PF0  - OSC_IN (8MHz)
PF1  - OSC_OUT

// 左侧引脚 (32K晶振)  
PC14 - OSC32_IN
PC15 - OSC32_OUT

// 电源引脚
VDD, VSS, VDDA, VSSA, VBAT等
```

### **软件中断引脚**
```c
// 左侧引脚 (EXTI)
PB1  - EXTI1 (混音器)
PB3  - EXTI3 (一次性任务)
PB4  - EXTI4 (UART处理)
```

## 🔍 **与当前配置的对比**

### ✅ **已正确配置**
- ADC通道 (PA0-PA5, PC0-PC3) ✅
- UART通信 (PA9-PA10) ✅  
- I2C接口 (PB10-PB11) ✅
- USB接口 (PA11-PA12) ✅
- 软件中断 (PB1, PB3, PB4) ✅

### ⚠️ **需要更新的配置**
- 按键引脚映射 (PC6-PC9, PE0-PE8)
- PWM输出引脚 (PB8)
- 状态控制引脚 (PE6-PE8)

## 📋 **更新计划**

1. **更新按键配置**: PC6-PC9 → KEY0-KEY3 ✅
2. **更新扩展按键**: PE0-PE5 → KEY0-KEY5 (扩展) ✅
3. **更新控制引脚**: PE6-PE8 → STBY/PWR_OFF/PWR_ON ✅
4. **更新PWM配置**: PB8 → TIM1_CH1 (蜂鸣器) ✅
5. **验证所有配置**: 确保无引脚冲突 ✅

## ✅ **已完成的更新**

### **STM32CubeMX配置文件 (.ioc)**
- ✅ PC6-PC9: 从TIM3 PWM通道改为GPIO输入 (按键)
- ✅ PE0-PE5: 从GPIO输出改为GPIO输入 (扩展按键)
- ✅ PE6-PE8: 保持GPIO输出，更新标签为控制引脚
- ✅ PB8: 从I2C1_SCL改为TIM1_CH1 (蜂鸣器PWM)

### **代码配置更新**
- ✅ config.h: 添加新的按键和控制引脚定义
- ✅ button_input.h: 更新按键枚举和GPIO映射
- ✅ button_input.c: 更新按键状态处理和组合键检测
- ✅ sound.h: 更新蜂鸣器GPIO从PA8改为PB8

### **功能映射更新**
```c
// 主按键 (PC6-PC9)
PC9 → KEY0 (确定键)
PC8 → KEY1 (菜单键)
PC7 → KEY2 (上键)
PC6 → KEY3 (下键)

// 扩展按键 (PE0-PE5)
PE0 → KEY0_EXT (扩展确定键)
PE1 → KEY1_EXT (扩展菜单键)
PE2 → KEY2_EXT (扩展上键)
PE3 → KEY3_EXT (扩展下键)
PE4 → KEY4 (左键)
PE5 → KEY5 (右键)

// 控制引脚 (PE6-PE8)
PE6 → STBY (待机控制)
PE7 → PWR_OFF (关机控制)
PE8 → PWR_ON (开机控制)

// PWM输出
PB8 → BUZZER_PWM (蜂鸣器TIM1_CH1)
```

### **组合键更新**
- 进入菜单: KEY1 + KEY2 (菜单键 + 上键)
- 退出菜单: KEY1 + KEY3 (菜单键 + 下键)

## 🔧 **下一步建议**

1. **重新生成代码**: 在STM32CubeMX中重新生成代码
2. **编译测试**: 编译项目检查是否有错误
3. **功能测试**: 测试按键输入和蜂鸣器输出
4. **I2C检查**: 确认I2C2 (PB10/PB11) 仍正常工作
