/**
 * @file mixer.h
 * @brief 混音器模块接口
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __MIXER_H
#define __MIXER_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"

/* 混音器通道数 */
#define MIXER_NUM_CHANNELS      16      // 最大输出通道数
#define MIXER_NUM_INPUTS        10      // 输入通道数 (ADC通道)

/* 通道值范围 */
#define MIXER_CHAN_MIN          172     // CRSF最小值
#define MIXER_CHAN_MAX          1811    // CRSF最大值
#define MIXER_CHAN_CENTER       992     // CRSF中心值
#define MIXER_CHAN_RANGE        819     // 单边范围

/* 输入通道定义 */
typedef enum {
    MIXER_INPUT_CH1 = 0,    // 摇杆通道1
    MIXER_INPUT_CH2,        // 摇杆通道2
    MIXER_INPUT_CH3,        // 摇杆通道3 (油门)
    MIXER_INPUT_CH4,        // 摇杆通道4
    MIXER_INPUT_SWA,        // 三段开关A
    MIXER_INPUT_SWB,        // 三段开关B
    MIXER_INPUT_VRA,        // 电位器A
    MIXER_INPUT_VRB,        // 电位器B
    MIXER_INPUT_COUNT
} mixer_input_t;

/* 输出通道定义 */
typedef enum {
    MIXER_OUTPUT_CH1 = 0,   // CRSF通道1 (副翼)
    MIXER_OUTPUT_CH2,       // CRSF通道2 (升降)
    MIXER_OUTPUT_CH3,       // CRSF通道3 (油门)
    MIXER_OUTPUT_CH4,       // CRSF通道4 (方向)
    MIXER_OUTPUT_CH5,       // CRSF通道5 (AUX1)
    MIXER_OUTPUT_CH6,       // CRSF通道6 (AUX2)
    MIXER_OUTPUT_CH7,       // CRSF通道7 (AUX3)
    MIXER_OUTPUT_CH8,       // CRSF通道8 (AUX4)
    MIXER_OUTPUT_COUNT
} mixer_output_t;

/* 混音器规则 */
typedef struct {
    mixer_input_t input;    // 输入通道
    int16_t weight;         // 权重 (-100 到 +100)
    int16_t offset;         // 偏移量
    bool enabled;           // 是否启用
} mixer_rule_t;

/* 通道配置 */
typedef struct {
    mixer_rule_t rules[MIXER_INPUT_COUNT];  // 混音规则
    int16_t min_limit;      // 最小限制
    int16_t max_limit;      // 最大限制
    int16_t center_trim;    // 中心微调
    bool reversed;          // 是否反向
    bool enabled;           // 通道是否启用
} mixer_channel_t;

/* 混音器配置 */
typedef struct {
    mixer_channel_t channels[MIXER_OUTPUT_COUNT];
    bool failsafe_enabled;  // 失控保护启用
    uint16_t failsafe_values[MIXER_OUTPUT_COUNT];  // 失控保护值
} mixer_config_t;

/* 全局变量声明 */
extern mixer_config_t mixer_config;
extern uint16_t mixer_inputs[MIXER_INPUT_COUNT];
extern uint16_t mixer_outputs[MIXER_OUTPUT_COUNT];

/* 函数声明 */

/* 初始化和配置 */
error_code_t MIXER_Init(void);
error_code_t MIXER_LoadConfig(void);
error_code_t MIXER_SaveConfig(void);
void MIXER_ResetConfig(void);

/* 混音器计算 */
void MIXER_CalcChannels(void);
void MIXER_UpdateInputs(void);
void MIXER_SetInput(mixer_input_t input, uint16_t value);
uint16_t MIXER_GetOutput(mixer_output_t output);

/* 通道配置 */
void MIXER_SetChannelRule(mixer_output_t output, mixer_input_t input, int16_t weight, int16_t offset);
void MIXER_EnableChannel(mixer_output_t output, bool enabled);
void MIXER_SetChannelLimits(mixer_output_t output, int16_t min_limit, int16_t max_limit);
void MIXER_SetChannelTrim(mixer_output_t output, int16_t trim);
void MIXER_SetChannelReverse(mixer_output_t output, bool reversed);

/* 失控保护 */
void MIXER_EnableFailsafe(bool enabled);
void MIXER_SetFailsafeValue(mixer_output_t output, uint16_t value);
void MIXER_ActivateFailsafe(void);
void MIXER_DeactivateFailsafe(void);
bool MIXER_IsFailsafeActive(void);

/* 工具函数 */
int16_t MIXER_ScaleInput(uint16_t adc_value);
uint16_t MIXER_ScaleOutput(int16_t mixer_value);
int16_t MIXER_ApplyLimits(int16_t value, int16_t min_limit, int16_t max_limit);

/* 调试功能 */
#if DEBUG_ENABLED
void MIXER_PrintConfig(void);
void MIXER_PrintChannels(void);
void MIXER_TestMixer(void);
#endif

/* 默认配置值 */
#define MIXER_DEFAULT_WEIGHT    100
#define MIXER_DEFAULT_OFFSET    0
#define MIXER_DEFAULT_MIN       -100
#define MIXER_DEFAULT_MAX       100
#define MIXER_DEFAULT_TRIM      0

#ifdef __cplusplus
}
#endif

#endif /* __MIXER_H */
