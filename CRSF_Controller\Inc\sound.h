/**
 * @file sound.h
 * @brief 音效系统接口 (基于deviation设计)
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __SOUND_H
#define __SOUND_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"

/* 音效定时器配置 */
#define SOUND_TIM               TIM1
#define SOUND_CHANNEL           TIM_CHANNEL_1
#define SOUND_GPIO_PORT         GPIOA
#define SOUND_GPIO_PIN          GPIO_PIN_8
#define SOUND_GPIO_AF           GPIO_AF2_TIM1

/* 震动电机配置 */
#define VIBRATOR_TIM            TIM3
#define VIBRATOR_CHANNEL        TIM_CHANNEL_1
#define VIBRATOR_GPIO_PORT      GPIOE
#define VIBRATOR_GPIO_PIN       GPIO_PIN_3
#define VIBRATOR_GPIO_AF        GPIO_AF2_TIM3

/* 音效回调函数类型 */
typedef uint16_t (*sound_callback_t)(void);

/* 全局变量声明 */
extern TIM_HandleTypeDef htim_sound;
extern TIM_HandleTypeDef htim_vibrator;

/* 函数声明 */

/* 初始化 */
void SOUND_Init(void);
void VIBRATINGMOTOR_Init(void);

/* 音效控制 */
void SOUND_SetFrequency(uint32_t frequency, uint32_t volume);
void SOUND_Start(uint32_t msec, sound_callback_t next_note_cb, uint8_t vibrate);
void SOUND_StartWithoutVibrating(uint32_t msec, sound_callback_t next_note_cb);
void SOUND_Stop(void);

/* 震动控制 */
void VIBRATINGMOTOR_Start(void);
void VIBRATINGMOTOR_Stop(void);

/* 高级震动控制 */
void VIBRATOR_Pulse(uint32_t duration_ms);          // 单次震动
void VIBRATOR_DoublePulse(void);                    // 双震动
void VIBRATOR_TriplePulse(void);                    // 三震动
void VIBRATOR_Pattern(uint32_t* pattern, uint8_t count);  // 自定义模式

/* 回调处理 */
uint32_t SOUND_Callback(void);

/* 预定义音效 */
void SOUND_PlayTone(uint32_t frequency, uint32_t duration_ms);
void SOUND_PlayBeep(void);
void SOUND_PlayDoubleBeep(void);
void SOUND_PlaySuccess(void);
void SOUND_PlayWarning(void);
void SOUND_PlayError(void);
void SOUND_PlayStartup(void);
void SOUND_PlayShutdown(void);

/* 音效序列 */
uint16_t SOUND_BeepSequence(void);
uint16_t SOUND_SuccessSequence(void);
uint16_t SOUND_WarningSequence(void);
uint16_t SOUND_ErrorSequence(void);
uint16_t SOUND_StartupSequence(void);
uint16_t SOUND_ShutdownSequence(void);

/* 音调定义 (Hz) */
#define SOUND_FREQ_C4           262
#define SOUND_FREQ_D4           294
#define SOUND_FREQ_E4           330
#define SOUND_FREQ_F4           349
#define SOUND_FREQ_G4           392
#define SOUND_FREQ_A4           440
#define SOUND_FREQ_B4           494
#define SOUND_FREQ_C5           523

#define SOUND_FREQ_LOW          400
#define SOUND_FREQ_MID          800
#define SOUND_FREQ_HIGH         1600
#define SOUND_FREQ_BEEP         1000
#define SOUND_FREQ_WARNING      600
#define SOUND_FREQ_ERROR        300

/* 音量定义 (0-100) */
#define SOUND_VOLUME_OFF        0
#define SOUND_VOLUME_LOW        25
#define SOUND_VOLUME_MID        50
#define SOUND_VOLUME_HIGH       75
#define SOUND_VOLUME_MAX        100

/* 时长定义 (ms) */
#define SOUND_DURATION_SHORT    100
#define SOUND_DURATION_MID      200
#define SOUND_DURATION_LONG     500

#ifdef __cplusplus
}
#endif

#endif /* __SOUND_H */
