# CRSF Controller 硬件功能说明

## 🔧 新增硬件功能

### 1. 蜂鸣器驱动 (PA8 - TIM1_CH1)
- **功能**: 音效反馈和提示音
- **驱动**: PWM控制，可调频率和音量
- **预定义音效**:
  - 开机/关机音
  - 按键音
  - 菜单进入/退出音
  - 警告/错误音
  - 成功提示音
  - 低电量警告音

### 2. 震动电机驱动 (PE3 - TIM3_CH1)
- **功能**: 触觉反馈
- **驱动**: PWM控制，可调强度
- **震动模式**:
  - 短震动 (按键反馈)
  - 长震动 (警告)
  - 双震动/三震动 (特殊事件)
  - 脉冲震动 (连续提醒)

### 3. 更新的ADC配置 (10通道)
```
PA0 - CH1      (通道1)
PA1 - CH2      (通道2) 
PA2 - CH3      (通道3)
PA3 - CH4      (通道4)
PA4 - SWA      (开关A)
PA5 - SWB      (开关B)
PC0 - VBAT     (电池电压检测)
PC1 - BIN      (外接电源检测)
PC2 - VRA      (电位器A)
PC3 - VRB      (电位器B)
```

### 4. 新的按键配置
```
PC9  - KEY0 (一键起落/确定键)
PC8  - KEY1 (双控对频/退出键)
PC7  - KEY2 (自动返航/上键)
PC6  - KEY3 (起落架收放/下键)
PD15 - KEY4 (快门/右键)
PD14 - KEY5 (照片视频切换/左键)
PD11 - PWR_SW (电源开关)
```

### 5. LED指示灯
```
PB8 - LED4 (蓝色LED)
PC4 - LED5 (红色LED)
```

### 6. I2C设备 (PB10/PB11 - I2C2)
- **SSD1306 OLED显示屏**: 128x64像素
- **FT24C128A EEPROM**: 16KB存储容量

### 7. USB接口 (PA11/PA12)
- **功能**: 虚拟串口调试 + 充电
- **用途**: 
  - 调试信息输出
  - 固件升级
  - 电池充电

### 8. 电源管理
- **电源开关控制**: PD12 (PWR_OFF)
- **充电状态检测**: PD13 (STDBY)
- **电池电压监测**: PC0 (VBAT)
- **外接电源检测**: PC1 (BIN)

## 🎮 按键操作模式

### 普通模式 (飞行模式)
- **KEY0**: 一键起落
- **KEY1**: 双控对频
- **KEY2**: 自动返航
- **KEY3**: 起落架收放 (长按收起，短按放下)
- **KEY4**: 快门
- **KEY5**: 照片/视频切换

### 菜单模式
- **进入**: 同时按下 KEY4 + KEY5
- **退出**: 再次同时按下 KEY4 + KEY5
- **导航**:
  - KEY0: 确定
  - KEY1: 退出/返回
  - KEY2: 上
  - KEY3: 下
  - KEY4: 右
  - KEY5: 左

## 💾 EEPROM存储分区

### 存储区域划分 (16KB总容量)
```
0x0000-0x03FF (1KB)   - 系统配置区
0x0400-0x07FF (1KB)   - 校准数据区
0x0800-0x1FFF (6KB)   - 模型数据区
0x2000-0x2FFF (4KB)   - 日志数据区
0x3000-0x3FFF (4KB)   - 用户数据区
```

### 系统配置内容
- 显示设置 (背光、对比度、自动关机)
- 音效设置 (蜂鸣器、震动强度)
- CRSF设置 (波特率、更新频率、模型ID)
- 语言设置

### 校准数据内容
- 10通道ADC校准数据
- 每通道包含: 最小值、中心值、最大值、反向标志、死区

## ⚡ 电源管理功能

### 电源状态监测
- **电池供电**: 正常工作模式
- **USB供电**: 连接电脑时
- **外部供电**: 外接电源适配器
- **充电中**: 正在充电
- **充电完成**: 充电完成 (STDBY信号)
- **低电量**: 电压低于阈值
- **危险电量**: 强制关机保护

### 自动管理功能
- **自动关机**: 无操作10分钟后自动关机
- **低电量警告**: 定期蜂鸣和震动提醒
- **充电状态指示**: LED和音效提示
- **电量百分比显示**: 实时电量显示

## 🔊 音效和震动反馈

### 系统事件反馈
- **开机**: 启动音 + 长震动
- **关机**: 关机音 + 长震动
- **按键**: 短哔声 + 短震动
- **菜单进入**: 高音 + 短震动
- **菜单退出**: 低音 + 短震动

### 警告反馈
- **低电量**: 警告音 + 警告震动 (每30秒)
- **连接丢失**: 错误音 + 错误震动
- **连接恢复**: 成功音 + 双震动
- **危险电量**: 连续报警音

## 🎯 主要改进

### 1. 硬件兼容性
- 完全适配您的遥控器硬件配置
- 支持所有实际连接的外设
- 正确的引脚映射和时钟配置

### 2. 用户体验
- 丰富的音效和触觉反馈
- 直观的按键操作逻辑
- 菜单模式和飞行模式切换

### 3. 电源管理
- 智能电源状态监测
- 自动充电管理
- 低功耗设计

### 4. 数据存储
- 完整的配置数据持久化
- 校准数据自动保存
- 分区管理，数据安全

### 5. 调试支持
- USB虚拟串口调试
- 完整的错误处理
- 状态信息输出

这些功能使您的CRSF遥控器具备了完整的商业级功能，包括专业的用户界面、可靠的电源管理和丰富的反馈机制。
