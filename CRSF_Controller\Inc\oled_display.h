/**
 * @file oled_display.h
 * @brief OLED显示驱动接口
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __OLED_DISPLAY_H
#define __OLED_DISPLAY_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"

/* OLED显示参数 */
#define OLED_WIDTH              128
#define OLED_HEIGHT             64
#define OLED_PAGES              8
#define OLED_COLUMNS            128

/* OLED命令定义 */
#define OLED_CMD_DISPLAY_OFF    0xAE
#define OLED_CMD_DISPLAY_ON     0xAF
#define OLED_CMD_SET_CONTRAST   0x81
#define OLED_CMD_NORMAL_DISPLAY 0xA6
#define OLED_CMD_INVERSE_DISPLAY 0xA7
#define OLED_CMD_SET_PAGE       0xB0
#define OLED_CMD_SET_COLUMN_LOW 0x00
#define OLED_CMD_SET_COLUMN_HIGH 0x10

/* 字体大小定义 */
typedef enum {
    FONT_SIZE_6x8 = 0,
    FONT_SIZE_8x16,
    FONT_SIZE_12x24,
    FONT_SIZE_16x32
} oled_font_size_t;

/* 显示模式 */
typedef enum {
    OLED_MODE_NORMAL = 0,
    OLED_MODE_INVERSE
} oled_display_mode_t;

/* 对齐方式 */
typedef enum {
    OLED_ALIGN_LEFT = 0,
    OLED_ALIGN_CENTER,
    OLED_ALIGN_RIGHT
} oled_align_t;

/* 显示缓冲区 */
extern uint8_t oled_buffer[OLED_PAGES][OLED_COLUMNS];

/* 函数声明 */

/* 初始化和控制 */
error_code_t OLED_Init(void);
error_code_t OLED_Reset(void);
error_code_t OLED_DisplayOn(void);
error_code_t OLED_DisplayOff(void);
error_code_t OLED_SetContrast(uint8_t contrast);
error_code_t OLED_SetDisplayMode(oled_display_mode_t mode);

/* 缓冲区操作 */
void OLED_Clear(void);
void OLED_ClearArea(uint8_t x, uint8_t y, uint8_t width, uint8_t height);
void OLED_Fill(uint8_t pattern);
void OLED_Update(void);
void OLED_UpdateArea(uint8_t x, uint8_t y, uint8_t width, uint8_t height);

/* 像素操作 */
void OLED_SetPixel(uint8_t x, uint8_t y, bool on);
bool OLED_GetPixel(uint8_t x, uint8_t y);
void OLED_DrawHLine(uint8_t x, uint8_t y, uint8_t width, bool on);
void OLED_DrawVLine(uint8_t x, uint8_t y, uint8_t height, bool on);
void OLED_DrawLine(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2, bool on);

/* 图形绘制 */
void OLED_DrawRect(uint8_t x, uint8_t y, uint8_t width, uint8_t height, bool on);
void OLED_FillRect(uint8_t x, uint8_t y, uint8_t width, uint8_t height, bool on);
void OLED_DrawCircle(uint8_t x, uint8_t y, uint8_t radius, bool on);
void OLED_FillCircle(uint8_t x, uint8_t y, uint8_t radius, bool on);
void OLED_DrawBitmap(uint8_t x, uint8_t y, const uint8_t* bitmap, uint8_t width, uint8_t height);

/* 文字显示 */
void OLED_SetFont(oled_font_size_t font);
void OLED_SetCursor(uint8_t x, uint8_t y);
void OLED_WriteChar(char ch);
void OLED_WriteString(const char* str);
void OLED_Printf(const char* format, ...);
uint8_t OLED_GetStringWidth(const char* str);
uint8_t OLED_GetCharWidth(char ch);
uint8_t OLED_GetFontHeight(void);

/* 对齐文字显示 */
void OLED_WriteStringAligned(uint8_t y, const char* str, oled_align_t align);
void OLED_PrintfAligned(uint8_t y, oled_align_t align, const char* format, ...);

/* 多行文字 */
void OLED_WriteStringMultiLine(uint8_t x, uint8_t y, const char* str, uint8_t line_height);
uint8_t OLED_GetStringLines(const char* str, uint8_t max_width);

/* 进度条和指示器 */
void OLED_DrawProgressBar(uint8_t x, uint8_t y, uint8_t width, uint8_t height, 
                         uint8_t value, uint8_t max_value);
void OLED_DrawBattery(uint8_t x, uint8_t y, uint8_t level);
void OLED_DrawSignalBars(uint8_t x, uint8_t y, uint8_t level, uint8_t max_level);

/* 菜单相关 */
void OLED_DrawMenuItem(uint8_t y, const char* text, bool selected);
void OLED_DrawMenuFrame(uint8_t item_count, uint8_t selected_item);
void OLED_DrawScrollBar(uint8_t x, uint8_t y, uint8_t height, 
                       uint8_t total_items, uint8_t visible_items, uint8_t first_item);

/* 状态显示 */
void OLED_ShowSplashScreen(const char* title, const char* subtitle);
void OLED_ShowLoadingScreen(const char* message, uint8_t progress);
void OLED_ShowErrorScreen(const char* error_message);
void OLED_ShowInfoScreen(const char* title, const char* info);

/* 工具函数 */
bool OLED_IsCoordinateValid(uint8_t x, uint8_t y);
uint8_t OLED_GetMaxCharsPerLine(void);
uint8_t OLED_GetMaxLines(void);

/* 调试功能 */
#if DEBUG_ENABLED
void OLED_PrintBuffer(void);
void OLED_TestPattern(void);
void OLED_TestFonts(void);
#endif

/* 字体数据声明 */
extern const uint8_t font_6x8[][6];
extern const uint8_t font_8x16[][16];
extern const uint8_t font_12x24[][36];
extern const uint8_t font_16x32[][64];

/* 位图数据声明 */
extern const uint8_t bitmap_logo[];
extern const uint8_t bitmap_battery[];
extern const uint8_t bitmap_signal[];
extern const uint8_t bitmap_elrs[];

#ifdef __cplusplus
}
#endif

#endif /* __OLED_DISPLAY_H */
