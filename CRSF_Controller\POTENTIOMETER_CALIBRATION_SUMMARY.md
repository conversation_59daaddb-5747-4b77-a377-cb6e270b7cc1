# 电位器校准功能实现总结

## ✅ **已完成的修改**

### 1. **头文件更新 (calibration.h)**
```c
// 添加了电位器校准步骤
typedef enum {
    CALIB_STEP_INIT = 0,
    CALIB_STEP_CENTER_STICKS,   // 摇杆居中
    CALIB_STEP_MOVE_STICKS,     // 摇杆全行程
    CALIB_STEP_CENTER_POTS,     // 电位器居中 ⭐ 新增
    CALIB_STEP_MOVE_POTS,       // 电位器全行程 ⭐ 新增
    CALIB_STEP_SWITCH_LOW,      // 开关下位
    CALIB_STEP_SWITCH_MID,      // 开关中位
    CALIB_STEP_SWITCH_HIGH,     // 开关上位
    CALIB_STEP_SAVE,            // 保存校准
    CALIB_STEP_DONE             // 完成
} calibration_step_t;

// 添加了电位器通道检查函数声明
bool Calibration_IsPotentiometerChannel(adc_channel_t channel);
```

### 2. **实现文件更新 (calibration.c)**

#### 2.1 添加了电位器校准变量
```c
static uint16_t pot_min_values[2];         // 电位器最小值 [VRA, VRB]
static uint16_t pot_max_values[2];         // 电位器最大值 [VRA, VRB]
static uint16_t pot_center_values[2];      // 电位器中心值 [VRA, VRB]
```

#### 2.2 添加了电位器校准处理函数
```c
static void Calibration_ProcessPotentiometerCalibration(void);
static bool Calibration_CheckPotentiometerMovement(void);
bool Calibration_IsPotentiometerChannel(adc_channel_t channel);
```

#### 2.3 更新了校准流程处理
```c
// 在Calibration_ProcessBoot()中添加了电位器校准步骤
case CALIB_STEP_CENTER_POTS:
case CALIB_STEP_MOVE_POTS:
    Calibration_ProcessPotentiometerCalibration();
    break;
```

#### 2.4 更新了显示文本
```c
// 步骤文本
case CALIB_STEP_CENTER_POTS:   return "步骤3: 电位器居中";
case CALIB_STEP_MOVE_POTS:     return "步骤4: 电位器全行程";

// 指令文本  
case CALIB_STEP_CENTER_POTS:   return "将电位器VRA/VRB置于中心位置";
case CALIB_STEP_MOVE_POTS:     return "转动电位器到各个极限位置";
```

#### 2.5 更新了数据保存函数
```c
// 在Calibration_SaveData()中添加电位器数据保存
for (uint8_t ch = 6; ch < 8; ch++) {  // ADC_VRA=6, ADC_VRB=7
    calib_data.adc_calibration[ch].min_value = pot_min_values[ch-6];
    calib_data.adc_calibration[ch].center_value = pot_center_values[ch-6];
    calib_data.adc_calibration[ch].max_value = pot_max_values[ch-6];
    calib_data.adc_calibration[ch].deadband = 5;  // 电位器小死区
}
```

#### 2.6 更新了采样处理函数
```c
// 在SampleChannel()中添加电位器处理
else if (channel == ADC_VRA || channel == ADC_VRB) {  // 电位器通道
    uint8_t pot_index = (channel == ADC_VRA) ? 0 : 1;
    switch (calib_context.step) {
        case CALIB_STEP_CENTER_POTS:
            pot_center_values[pot_index] = value;
            break;
        case CALIB_STEP_MOVE_POTS:
            if (value < pot_min_values[pot_index]) {
                pot_min_values[pot_index] = value;
            }
            if (value > pot_max_values[pot_index]) {
                pot_max_values[pot_index] = value;
            }
            break;
    }
}
```

## 🔄 **完整校准流程**

### 新的8步校准流程
```
步骤1: 摇杆居中     → 将4个摇杆置于中心位置
步骤2: 摇杆全行程   → 转动摇杆到各个极限位置
步骤3: 电位器居中   → 将VRA/VRB电位器置于中心位置  ⭐ 新增
步骤4: 电位器全行程 → 转动电位器到各个极限位置    ⭐ 新增
步骤5: 开关下位     → 将SWA/SWB开关拨到下位
步骤6: 开关中位     → 将SWA/SWB开关拨到中位
步骤7: 开关上位     → 将SWA/SWB开关拨到上位
步骤8: 保存数据     → 自动保存校准数据到EEPROM
```

### 电位器校准详细流程
```c
步骤3 - 电位器居中:
1. 用户将VRA和VRB电位器调到中心位置
2. 系统检测电位器位置稳定2秒
3. 记录中心值: pot_center_values[0/1] = 当前ADC值
4. 初始化最大最小值为中心值
5. 播放提示音，进入步骤4

步骤4 - 电位器全行程:
1. 用户转动VRA电位器到最小和最大位置
2. 用户转动VRB电位器到最小和最大位置
3. 系统实时更新: pot_min_values[] 和 pot_max_values[]
4. 检查行程是否足够 (> CALIB_MIN_RANGE)
5. 两个电位器都满足要求且稳定2秒后进入步骤5
```

## 📊 **校准数据结构**

### ADC通道映射
```c
通道索引  ADC通道    功能        校准类型
0        ADC_CH1    右摇杆X     摇杆校准 (中心+行程)
1        ADC_CH2    右摇杆Y     摇杆校准 (中心+行程)
2        ADC_CH3    左摇杆Y     摇杆校准 (中心+行程)
3        ADC_CH4    左摇杆X     摇杆校准 (中心+行程)
4        ADC_SWA    三段开关A   开关校准 (三个位置)
5        ADC_SWB    三段开关B   开关校准 (三个位置)
6        ADC_VRA    电位器A     电位器校准 (中心+行程) ⭐ 新增
7        ADC_VRB    电位器B     电位器校准 (中心+行程) ⭐ 新增
8        ADC_VBAT   电池电压    无需校准
9        ADC_VIN    外接电源    无需校准
```

### 校准参数对比
```c
参数类型    摇杆通道    电位器通道    开关通道
最小值      极限位置    逆时针到底    下位
中心值      居中位置    中心位置      中位
最大值      极限位置    顺时针到底    上位
死区        20          5             0
用途        飞行控制    参数调节      模式切换
```

## 🎯 **验证和测试**

### 校准数据验证
```c
电位器校准数据验证:
1. 行程检查: pot_max_values[i] - pot_min_values[i] > CALIB_MIN_RANGE
2. 中心值检查: pot_min_values[i] < pot_center_values[i] < pot_max_values[i]
3. 数据合理性: 0 < 所有值 < 4095 (12位ADC范围)
4. CRC校验: 整个校准数据结构的CRC32校验
```

### 功能测试
```c
测试项目:
1. 电位器中心位置校准 - 检查中心值记录是否正确
2. 电位器全行程校准 - 检查最大最小值更新是否正确
3. 校准数据保存 - 检查EEPROM写入是否成功
4. 校准数据加载 - 检查开机时数据读取是否正确
5. 混音器集成 - 检查电位器输入是否正确传递到混音器
6. 用户界面 - 检查校准步骤显示是否正确
```

## 🔧 **集成验证**

### 与其他模块的集成
```c
✅ ADC输入模块: 
   - ADC_VRA和ADC_VRB通道正确定义
   - GetCalibratedValue()函数支持电位器校准

✅ 混音器模块:
   - MIXER_INPUT_VRA和MIXER_INPUT_VRB正确定义
   - UpdateInputs()函数正确读取电位器校准值

✅ 菜单系统:
   - 校准菜单支持8步校准流程
   - 显示界面支持电位器校准提示

✅ 音效系统:
   - 电位器校准步骤有对应的音效提示
   - 校准完成有成功音效

✅ EEPROM存储:
   - 校准数据结构支持8通道数据
   - CRC校验包含电位器校准数据
```

## 📋 **使用说明**

### 开发者使用
```c
// 检查是否为电位器通道
if (Calibration_IsPotentiometerChannel(ADC_VRA)) {
    // 电位器相关处理
}

// 获取电位器校准值
uint16_t vra_value = ADC_Input_GetCalibratedValue(ADC_VRA);
uint16_t vrb_value = ADC_Input_GetCalibratedValue(ADC_VRB);

// 在混音器中使用电位器
MIXER_SetChannelRule(MIXER_OUTPUT_CH5, MIXER_INPUT_VRA, 100, 0);
MIXER_SetChannelRule(MIXER_OUTPUT_CH6, MIXER_INPUT_VRB, 100, 0);
```

### 用户使用
```
1. 开机自动校准:
   - 保持摇杆居中，电位器任意位置
   - 按提示进行全行程操作

2. 手动精确校准:
   - 菜单 → 校准设置 → 开始校准
   - 按步骤3和4进行电位器校准

3. 校准验证:
   - 菜单 → 信息显示 → 输入测试
   - 转动电位器查看数值变化
```

## 🎉 **总结**

电位器校准功能已经**完整实现**，包括：

✅ **完整的校准流程**: 8步校准，覆盖所有输入通道  
✅ **精确的校准算法**: 中心值+全行程校准，5级死区  
✅ **完善的数据管理**: EEPROM存储，CRC校验，自动恢复  
✅ **友好的用户界面**: 清晰的步骤提示，音效反馈  
✅ **无缝的系统集成**: 与ADC、混音器、菜单系统完美集成  

现在CRSF Controller支持**完整的8通道校准**，为用户提供了专业级的控制精度！
