# CRSF Controller 遥控器用户说明书

## 📖 **产品概述**

CRSF Controller是一款基于STM32F072的高性能遥控器，专为FPV飞行、竞速无人机和精密控制应用设计。采用deviation架构，具有专业级的实时性能和丰富的功能。

### 🎯 **主要特性**
- **16通道输出**: 支持16个独立控制通道
- **4ms精确周期**: CRSF协议精确时序控制
- **多输入支持**: 4路摇杆 + 6路开关/电位器
- **实时混音**: 高级混音器支持复杂控制逻辑
- **USB连接**: 虚拟串口调试和固件升级
- **OLED显示**: 128x64高清显示屏
- **音效反馈**: PWM蜂鸣器 + 震动电机
- **自动校准**: 开机自动校准和手动精确校准

### 📊 **技术规格**
| 项目 | 规格 | 说明 |
|------|------|------|
| **MCU** | STM32F072CBT6 | 48MHz, 128KB Flash, 16KB RAM |
| **通道数** | 16通道输出 | CRSF协议标准 |
| **输入精度** | 12位ADC | 4096级精度 |
| **更新频率** | 250Hz (4ms) | 高频率低延迟 |
| **显示屏** | 128x64 OLED | I2C接口 |
| **连接方式** | UART + USB | CRSF + 调试接口 |
| **电源** | 3.7V锂电池 | 内置电量监测 |

## 🎮 **硬件接口说明**

### 输入接口
```
摇杆输入 (ADC):
├── CH1 (PA0) - 右摇杆 X轴 (副翼/Roll)
├── CH2 (PA1) - 右摇杆 Y轴 (升降/Pitch)  
├── CH3 (PA2) - 左摇杆 Y轴 (油门/Throttle)
└── CH4 (PA3) - 左摇杆 X轴 (方向/Yaw)

开关输入 (ADC):
├── SWA (PA4) - 三段开关A (飞行模式)
├── SWB (PA5) - 三段开关B (辅助功能)
├── VRA (PA6) - 电位器A (可调参数)
└── VRB (PA7) - 电位器B (可调参数)

电源监测 (ADC):
├── VBAT (PB0) - 电池电压监测
└── VIN (PB1) - 外接电源监测
```

### 输出接口
```
CRSF输出 (UART1):
├── TX (PA9) - CRSF数据发送
└── RX (PA10) - CRSF遥测接收

USB接口 (USB):
├── D+ (PA12) - USB数据正
└── D- (PA11) - USB数据负

显示接口 (I2C2):
├── SCL (PB10) - I2C时钟
└── SDA (PB11) - I2C数据
```

### 用户接口
```
按键输入 (GPIO):
├── KEY1 (PD0) - 菜单键
├── KEY2 (PD1) - 上键
├── KEY3 (PD2) - 下键  
├── KEY4 (PD3) - 左键
└── KEY5 (PD4) - 右键/确认键

音效输出 (PWM):
├── BUZZER (PA8) - 蜂鸣器输出
└── VIBRATOR (PE3) - 震动电机输出

状态指示 (GPIO):
├── LED1 (PE4) - 电源指示
├── LED2 (PE5) - 连接指示
├── LED3 (PE6) - 数据指示
└── LED4 (PE7) - 错误指示
```

## 🔄 **系统工作流程**

### 启动流程
```mermaid
graph TD
    A[上电启动] --> B[硬件初始化]
    B --> C[外设配置]
    C --> D[模块初始化]
    D --> E{需要校准?}
    E -->|是| F[开机校准]
    E -->|否| G[启动CRSF协议]
    F --> H[校准完成]
    H --> G
    G --> I[进入主循环]
    I --> J[正常运行]
```

### 主循环流程
```mermaid
graph LR
    A[主循环] --> B[时钟事件处理]
    B --> C[中优先级任务]
    C --> D[低优先级任务]
    D --> E[CRSF接收处理]
    E --> F[看门狗重置]
    F --> G[进入低功耗]
    G --> A
```

### CRSF协议流程
```mermaid
graph TD
    A[TIM2中断 4ms] --> B[STATE_DATA0]
    B --> C[触发混音器计算]
    C --> D[等待mixer_runtime]
    D --> E[STATE_DATA1]
    E --> F[发送CRSF数据包]
    F --> G[处理接收数据]
    G --> H[返回剩余时间]
    H --> A
```

## 🎛️ **功能模块详解**

### 1. **输入系统**

#### 1.1 摇杆输入
```c
// 摇杆通道定义
typedef enum {
    STICK_RIGHT_X = 0,    // 副翼 (Aileron)
    STICK_RIGHT_Y = 1,    // 升降 (Elevator)  
    STICK_LEFT_Y = 2,     // 油门 (Throttle)
    STICK_LEFT_X = 3      // 方向 (Rudder)
} stick_channel_t;

// 摇杆数据处理流程
ADC采样(1ms) → 数字滤波 → 校准应用 → 死区处理 → 输出到混音器
```

**摇杆功能说明**:
- **右摇杆X轴**: 控制飞机的副翼，实现左右倾斜
- **右摇杆Y轴**: 控制飞机的升降舵，实现俯仰动作
- **左摇杆Y轴**: 控制油门，调节飞机动力
- **左摇杆X轴**: 控制方向舵，实现左右转向

#### 1.2 开关输入
```c
// 开关状态定义
typedef enum {
    SWITCH_POS_LOW = 0,     // 下位 (0-30%)
    SWITCH_POS_MID = 1,     // 中位 (40-60%)
    SWITCH_POS_HIGH = 2     // 上位 (70-100%)
} switch_position_t;

// 开关功能分配
SWA: 飞行模式切换 (手动/自稳/定高)
SWB: 辅助功能 (解锁/上锁/紧急停止)
VRA: 可调参数1 (舵机行程/PID参数)
VRB: 可调参数2 (相机云台/LED亮度)
```

#### 1.3 按键输入
```c
// 按键事件类型
typedef enum {
    BUTTON_EVENT_NONE = 0,
    BUTTON_EVENT_PRESS,      // 按下
    BUTTON_EVENT_RELEASE,    // 释放
    BUTTON_EVENT_LONG_PRESS, // 长按 (>1秒)
    BUTTON_EVENT_DOUBLE_CLICK // 双击
} button_event_t;

// 按键功能分配
KEY1 (菜单键): 进入/退出菜单系统
KEY2 (上键): 菜单向上/数值增加
KEY3 (下键): 菜单向下/数值减少
KEY4 (左键): 菜单返回/取消操作
KEY5 (确认键): 菜单确认/进入子菜单
```

### 2. **混音器系统**

#### 2.1 混音器原理
```c
// 混音器规则结构
typedef struct {
    mixer_input_t input;     // 输入源
    int16_t weight;          // 权重 (-100 到 +100)
    int16_t offset;          // 偏移量
    int16_t curve;           // 曲线类型
} mixer_rule_t;

// 混音计算公式
output = (input * weight / 100) + offset
```

#### 2.2 预设混音模式
```c
// 飞机模式 (4通道)
CH1 = STICK_RIGHT_X * 100%           // 副翼
CH2 = STICK_RIGHT_Y * 100%           // 升降
CH3 = STICK_LEFT_Y * 100%            // 油门
CH4 = STICK_LEFT_X * 100%            // 方向

// 直升机模式 (6通道)
CH1 = STICK_RIGHT_X * 100%           // 副翼
CH2 = STICK_RIGHT_Y * 100%           // 升降  
CH3 = STICK_LEFT_Y * 100%            // 油门
CH4 = STICK_LEFT_X * 100%            // 方向
CH5 = SWA                            // 陀螺仪增益
CH6 = SWB                            // 飞行模式

// 多旋翼模式 (8通道)
CH1 = STICK_RIGHT_X * 100%           // Roll
CH2 = STICK_RIGHT_Y * 100%           // Pitch
CH3 = STICK_LEFT_Y * 100%            // Throttle
CH4 = STICK_LEFT_X * 100%            // Yaw
CH5 = SWA                            // 飞行模式
CH6 = SWB                            // 解锁开关
CH7 = VRA                            // 辅助通道1
CH8 = VRB                            // 辅助通道2
```

#### 2.3 高级混音功能
```c
// 差动混音 (V尾飞机)
CH2_LEFT = ELEVATOR + RUDDER * 50%   // 左V尾
CH2_RIGHT = ELEVATOR - RUDDER * 50%  // 右V尾

// 襟副翼混音
CH1_LEFT = AILERON + FLAP * 30%      // 左襟副翼
CH1_RIGHT = -AILERON + FLAP * 30%    // 右襟副翼

// 螺距曲线 (直升机)
PITCH_CURVE: 0% → -12°, 50% → 0°, 100% → +12°

// 油门曲线
THROTTLE_CURVE: 线性/指数/对数曲线可选
```

### 3. **CRSF协议系统**

#### 3.1 CRSF数据包格式
```c
// CRSF RC数据包 (26字节)
typedef struct {
    uint8_t sync;           // 同步字节 (0xC8)
    uint8_t length;         // 数据长度 (24)
    uint8_t type;           // 数据类型 (0x16)
    uint16_t channels[16];  // 16个通道数据 (11位精度)
    uint8_t crc;            // CRC校验
} crsf_rc_packet_t;

// 通道数据范围: 172-1811 (对应1000-2000μs)
// 中心值: 992 (对应1500μs)
```

#### 3.2 CRSF时序控制
```c
// 4ms精确周期控制
void CRSF_SerialCallback(void)
{
    static crsf_state_t state = STATE_DATA0;
    
    switch (state) {
        case STATE_DATA0:
            // 触发混音器计算
            CLOCK_RunMixer();
            state = STATE_DATA1;
            return mixer_runtime;  // 等待混音器完成
            
        case STATE_DATA1:
            // 发送CRSF数据包
            CRSF_SendRCPacket();
            // 处理接收数据
            CRSF_ProcessRxData();
            state = STATE_DATA0;
            return 4000 - mixer_runtime;  // 剩余时间
    }
}
```

#### 3.3 遥测数据处理
```c
// 支持的遥测数据类型
CRSF_FRAMETYPE_GPS          // GPS信息
CRSF_FRAMETYPE_BATTERY      // 电池信息  
CRSF_FRAMETYPE_ATTITUDE     // 姿态信息
CRSF_FRAMETYPE_FLIGHT_MODE  // 飞行模式
CRSF_FRAMETYPE_LINK_STATS   // 链路统计

// 遥测数据显示
void Display_Telemetry(void)
{
    OLED_Printf("GPS: %d Sats", gps_satellites);
    OLED_Printf("RSSI: %d dBm", link_rssi);
    OLED_Printf("Batt: %.1fV", battery_voltage);
    OLED_Printf("Mode: %s", flight_mode_name);
}
```

### 4. **显示系统**

#### 4.1 主界面显示
```c
// 主界面布局
┌─────────────────────────────┐
│ CRSF Controller    [RSSI]   │ 第1行: 标题和信号强度
├─────────────────────────────┤
│ CH1:1500 CH2:1500 CH3:1000 │ 第2行: 主要通道值
│ CH4:1500 SWA:↑ SWB:↓       │ 第3行: 方向舵和开关状态
├─────────────────────────────┤
│ GPS:12 Sats  HDOP:1.2      │ 第4行: GPS信息
│ Batt:3.8V    Mode:ACRO     │ 第5行: 电池和飞行模式
├─────────────────────────────┤
│ [MENU] [TRIM] [SETUP]      │ 第6行: 功能按键提示
└─────────────────────────────┘
```

#### 4.2 菜单系统
```c
// 主菜单结构
主菜单
├── 模型设置
│   ├── 混音器配置
│   ├── 通道映射
│   ├── 舵机行程
│   └── 失控保护
├── 系统设置  
│   ├── 显示设置
│   ├── 音效设置
│   ├── 按键设置
│   └── 电源管理
├── 校准设置
│   ├── 摇杆校准
│   ├── 开关校准
│   └── 电池校准
├── 信息显示
│   ├── 系统信息
│   ├── 版本信息
│   └── 遥测信息
└── 高级功能
    ├── 固件升级
    ├── 数据备份
    └── 恢复出厂
```

### 5. **校准系统**

#### 5.1 开机自动校准
```c
// 开机校准流程
void Calibration_Boot_Process(void)
{
    // 1. 检查是否需要校准
    if (!Calibration_IsNeeded()) {
        return;  // 跳过校准
    }
    
    // 2. 显示校准提示
    OLED_Clear();
    OLED_WriteString("Auto Calibration");
    OLED_WriteString("Keep sticks centered");
    OLED_WriteString("Don't touch switches");
    
    // 3. 等待稳定
    HAL_Delay(2000);
    
    // 4. 采集中心点数据
    for (int i = 0; i < 100; i++) {
        // 采集ADC数据
        calibration_data.center[0] += ADC_GetValue(0);
        calibration_data.center[1] += ADC_GetValue(1);
        calibration_data.center[2] += ADC_GetValue(2);
        calibration_data.center[3] += ADC_GetValue(3);
        HAL_Delay(10);
    }
    
    // 5. 计算平均值
    for (int i = 0; i < 4; i++) {
        calibration_data.center[i] /= 100;
    }
    
    // 6. 保存校准数据
    EEPROM_WriteCalibration(&calibration_data);
    
    SOUND_PlaySuccess();  // 播放成功音效
}
```

#### 5.2 手动精确校准
```c
// 手动校准步骤
typedef enum {
    CAL_STEP_CENTER = 0,    // 中心点校准
    CAL_STEP_MIN,           // 最小值校准
    CAL_STEP_MAX,           // 最大值校准
    CAL_STEP_COMPLETE       // 校准完成
} calibration_step_t;

void Calibration_Manual_Process(void)
{
    switch (calibration_step) {
        case CAL_STEP_CENTER:
            OLED_WriteString("Step 1: Center Position");
            OLED_WriteString("Keep sticks centered");
            OLED_WriteString("Press OK to continue");
            break;
            
        case CAL_STEP_MIN:
            OLED_WriteString("Step 2: Minimum Position");
            OLED_WriteString("Move sticks to corners");
            OLED_WriteString("Press OK when done");
            break;
            
        case CAL_STEP_MAX:
            OLED_WriteString("Step 3: Maximum Position");
            OLED_WriteString("Move sticks to opposite");
            OLED_WriteString("Press OK when done");
            break;
            
        case CAL_STEP_COMPLETE:
            OLED_WriteString("Calibration Complete!");
            OLED_WriteString("Data saved to EEPROM");
            break;
    }
}
```

### 6. **音效系统**

#### 6.1 系统音效
```c
// 预定义音效
void System_Sound_Events(void)
{
    // 开机音效 (上升音阶)
    SOUND_PlayStartup();     // C4-E4-G4 和弦
    
    // 按键音效
    SOUND_PlayBeep();        // 1000Hz 短哔声
    
    // 成功音效 (带震动)
    SOUND_PlaySuccess();     // C4-E4-G4 序列 + 震动
    
    // 警告音效 (双音调)
    SOUND_PlayWarning();     // 600Hz 双音调 + 震动
    
    // 错误音效 (三重警报)
    SOUND_PlayError();       // 300Hz 三重音 + 震动
    
    // 关机音效 (下降音阶)
    SOUND_PlayShutdown();    // G4-E4-C4 序列
}
```

#### 6.2 状态提示音效
```c
// 飞行状态音效
void Flight_Status_Sounds(void)
{
    // 解锁成功
    if (armed_status_changed && armed) {
        SOUND_PlaySuccess();
        VIBRATOR_DoublePulse();
    }
    
    // 失控保护
    if (failsafe_triggered) {
        SOUND_PlayError();
        VIBRATOR_Pattern(sos_pattern, 15);  // SOS震动
    }
    
    // 低电量警告
    if (battery_low) {
        SOUND_PlayWarning();
        VIBRATOR_Pulse(1000);  // 长震动
    }
    
    // 信号丢失
    if (signal_lost) {
        SOUND_PlayError();
        VIBRATOR_TriplePulse();
    }
}
```

### 7. **电源管理**

#### 7.1 电池监测
```c
// 电池电压监测
void Battery_Monitor(void)
{
    // 读取电池电压 (分压电路)
    uint16_t adc_value = ADC_Input_GetValue(ADC_VBAT);
    float battery_voltage = (adc_value * 3.3f / 4096) * 2.0f;  // 2:1分压
    
    // 电量百分比计算 (3.0V-4.2V)
    battery_percentage = (battery_voltage - 3.0f) / 1.2f * 100;
    if (battery_percentage > 100) battery_percentage = 100;
    if (battery_percentage < 0) battery_percentage = 0;
    
    // 低电量警告
    if (battery_percentage < 20 && !low_battery_warned) {
        SOUND_PlayWarning();
        VIBRATOR_Pulse(500);
        low_battery_warned = true;
    }
    
    // 极低电量保护
    if (battery_percentage < 5) {
        // 进入低功耗模式
        System_EnterLowPowerMode();
    }
}
```

#### 7.2 低功耗模式
```c
// 低功耗模式配置
void System_EnterLowPowerMode(void)
{
    // 关闭非必要外设
    HAL_TIM_Base_Stop(&htim1);      // 停止音效定时器
    HAL_I2C_DeInit(&hi2c2);         // 关闭OLED
    
    // 配置GPIO为模拟输入
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    
    // 关闭未使用的时钟
    __HAL_RCC_TIM3_CLK_DISABLE();
    __HAL_RCC_I2C2_CLK_DISABLE();
    
    // 进入STOP模式
    HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
    
    // 唤醒后恢复时钟
    SystemClock_Config();
}
```

## 🔧 **高级功能**

### 1. **数据记录**
```c
// 飞行数据记录
typedef struct {
    uint32_t timestamp;      // 时间戳
    uint16_t channels[16];   // 通道数据
    float battery_voltage;   // 电池电压
    int8_t rssi;            // 信号强度
    uint8_t flight_mode;    // 飞行模式
} flight_log_t;

// 记录飞行数据到EEPROM
void FlightLog_Record(void)
{
    flight_log_t log_entry;
    log_entry.timestamp = HAL_GetTick();
    
    // 记录通道数据
    for (int i = 0; i < 16; i++) {
        log_entry.channels[i] = MIXER_GetOutput(i);
    }
    
    log_entry.battery_voltage = Battery_GetVoltage();
    log_entry.rssi = CRSF_GetRSSI();
    log_entry.flight_mode = CRSF_GetFlightMode();
    
    EEPROM_WriteFlightLog(&log_entry);
}
```

### 2. **模型管理**
```c
// 多模型支持
typedef struct {
    char name[16];           // 模型名称
    mixer_rule_t mixer[32];  // 混音器规则
    uint16_t endpoints[16];  // 舵机行程
    uint8_t flight_mode;     // 飞行模式
    calibration_data_t cal;  // 校准数据
} model_config_t;

// 模型切换
void Model_Switch(uint8_t model_id)
{
    if (model_id >= MAX_MODELS) return;
    
    // 保存当前模型
    EEPROM_SaveModel(current_model_id, &current_model);
    
    // 加载新模型
    EEPROM_LoadModel(model_id, &current_model);
    current_model_id = model_id;
    
    // 重新配置混音器
    MIXER_LoadConfig(&current_model.mixer);
    
    // 播放切换音效
    SOUND_PlaySuccess();
    
    OLED_Printf("Model: %s", current_model.name);
}
```

### 3. **故障诊断**
```c
// 系统自检
void System_SelfTest(void)
{
    bool test_passed = true;
    
    // ADC测试
    if (!ADC_SelfTest()) {
        OLED_WriteString("ADC Test Failed");
        test_passed = false;
    }
    
    // UART测试
    if (!UART_SelfTest()) {
        OLED_WriteString("UART Test Failed");
        test_passed = false;
    }
    
    // I2C测试
    if (!I2C_SelfTest()) {
        OLED_WriteString("I2C Test Failed");
        test_passed = false;
    }
    
    // 按键测试
    if (!Button_SelfTest()) {
        OLED_WriteString("Button Test Failed");
        test_passed = false;
    }
    
    if (test_passed) {
        OLED_WriteString("All Tests Passed");
        SOUND_PlaySuccess();
    } else {
        SOUND_PlayError();
    }
}
```

## 📋 **操作指南**

### 开机操作
1. **正常开机**: 按住电源键2秒
2. **校准模式**: 开机时同时按住KEY1+KEY2
3. **升级模式**: 开机时同时按住KEY1+KEY2+KEY3

### 基本操作
1. **菜单导航**: KEY1进入菜单，KEY2/KEY3上下选择，KEY5确认
2. **参数调节**: KEY4/KEY5左右调节数值
3. **快速设置**: 长按KEY1进入快速设置模式

### 高级操作
1. **模型切换**: 菜单→模型设置→选择模型
2. **混音器配置**: 菜单→模型设置→混音器配置
3. **校准设置**: 菜单→校准设置→选择校准类型

## ⚠️ **注意事项**

### 安全提示
1. **首次使用前必须进行校准**
2. **定期检查电池电量，避免低电量飞行**
3. **确保CRSF连接稳定后再起飞**
4. **设置合适的失控保护参数**

### 维护保养
1. **定期清洁摇杆和按键**
2. **避免在潮湿环境中使用**
3. **长期不用时请取出电池**
4. **定期更新固件版本**

## 🛠️ **STM32CubeMX配置**

### 1. **项目创建和基本设置**

#### 1.1 新建项目
```
STM32CubeMX配置步骤:
1. 启动STM32CubeMX
2. 选择 "File" → "New Project"
3. MCU Selector: 搜索 "STM32F072CBT6"
4. 选择对应MCU型号
5. 点击 "Start Project"

项目设置:
- Project Name: CRSF_Controller
- Toolchain/IDE: Makefile
- 代码生成: HAL库 + 外设独立文件
```

#### 1.2 时钟配置 (Clock Configuration)
```
时钟源配置:
- HSE: 8MHz Crystal/Ceramic Resonator
- PLL: HSE × 6 = 48MHz
- System Clock: PLLCLK (48MHz)
- HCLK: 48MHz
- APB1/APB2: 48MHz
- USB Clock: HSI48 (48MHz)

关键时钟:
- ADC Clock: 12MHz (PCLK/4)
- Timer Clock: 48MHz
- I2C Clock: 48MHz
- UART Clock: 48MHz
```

### 2. **外设配置详解**

#### 2.1 ADC配置 (10通道输入)
```
ADC基本配置:
- Mode: Independent mode
- Clock: Synchronous divided by 4 (12MHz)
- Resolution: 12 bits (4096级)
- Alignment: Right alignment
- Scan Mode: Enable (多通道扫描)
- Continuous Mode: Enable (连续转换)

通道配置:
Rank 1: PA0 (ADC_IN0) - CH1右摇杆X
Rank 2: PA1 (ADC_IN1) - CH2右摇杆Y
Rank 3: PA2 (ADC_IN2) - CH3左摇杆Y
Rank 4: PA3 (ADC_IN3) - CH4左摇杆X
Rank 5: PA4 (ADC_IN4) - SWA三段开关
Rank 6: PA5 (ADC_IN5) - SWB三段开关
Rank 7: PA6 (ADC_IN6) - VRA电位器
Rank 8: PA7 (ADC_IN7) - VRB电位器
Rank 9: PB0 (ADC_IN8) - VBAT电池电压
Rank 10: PB1 (ADC_IN9) - VIN外接电源

DMA配置:
- DMA1 Channel 1: ADC
- Direction: Peripheral To Memory
- Mode: Circular (循环模式)
- Data Width: Half Word (16位)
- Priority: High
```

#### 2.2 UART配置 (CRSF通信)
```
USART1配置:
- Mode: Asynchronous
- Pins: PA9(TX), PA10(RX)
- Baud Rate: 420000 bps (CRSF标准)
- Data Bits: 8
- Stop Bits: 1
- Parity: None
- Flow Control: None

DMA配置:
- TX: DMA1 Channel 2 (Memory To Peripheral, Normal)
- RX: DMA1 Channel 3 (Peripheral To Memory, Circular)

中断配置:
- USART1 global interrupt: Enable
- DMA1 channel 2/3 interrupts: Enable
```

#### 2.3 I2C配置 (OLED显示)
```
I2C2配置:
- Mode: I2C
- Pins: PB10(SCL), PB11(SDA)
- Speed: Standard Mode (100kHz)
- Address Length: 7-bit
- Clock Stretching: Disable

DMA配置:
- TX: DMA1 Channel 4 (Memory To Peripheral, Normal)
- RX: DMA1 Channel 5 (Peripheral To Memory, Normal)

中断配置:
- I2C2 event interrupt: Enable
- I2C2 error interrupt: Enable
```

#### 2.4 定时器配置
```
TIM1 (音效PWM):
- Channel 1: PA8 (蜂鸣器)
- Mode: PWM Generation
- Prescaler: 47 (1MHz时钟)
- Period: 1000 (1kHz基频)
- Pulse: 0 (初始关闭)

TIM2 (CRSF定时器):
- Mode: Internal Clock
- Prescaler: 47 (1MHz时钟)
- Period: 3999 (4ms周期)
- Priority: 0 (最高优先级)

TIM3 (震动电机PWM):
- Channel 1: PE3 (震动电机)
- Mode: PWM Generation
- Prescaler: 47 (1MHz时钟)
- Period: 1000 (1kHz PWM)

TIM7 (ADC触发):
- Mode: Internal Clock
- Prescaler: 47 (1MHz时钟)
- Period: 999 (1ms触发)
- Trigger: Update Event
```

#### 2.5 USB配置
```
USB_DEVICE配置:
- Class: CDC (Virtual COM Port)
- Pins: PA11(DM), PA12(DP)
- Speed: Full Speed (12Mbps)

设备描述符:
- VID: 0x0483 (STMicroelectronics)
- PID: 0x5740 (Virtual COM Port)
- Product: "CRSF Controller"
- Manufacturer: "CRSF Team"

时钟配置:
- USB Clock Source: HSI48 (48MHz)
- HSI48: Enable
```

### 3. **GPIO和中断配置**

#### 3.1 按键输入
```
按键GPIO配置:
- PD0: KEY1 (菜单键)
- PD1: KEY2 (上键)
- PD2: KEY3 (下键)
- PD3: KEY4 (左键)
- PD4: KEY5 (确认键)

GPIO设置:
- Mode: Input
- Pull: Pull-up
- Speed: Low
```

#### 3.2 LED输出
```
LED GPIO配置:
- PE4: LED1 (电源指示)
- PE5: LED2 (连接指示)
- PE6: LED3 (数据指示)
- PE7: LED4 (错误指示)

GPIO设置:
- Mode: Output Push Pull
- Pull: No pull
- Speed: Low
- Initial State: Reset (LED关闭)
```

#### 3.3 软件中断引脚
```
EXTI配置:
- PB1: EXTI1 (混音器计算)
- PB3: EXTI3 (一次性任务)
- PB4: EXTI4 (UART处理)

EXTI设置:
- Mode: Rising edge trigger
- Pull: No pull
- Priority: 1, 3, 2 (分别对应不同优先级)
```

### 4. **中断优先级配置**

#### 4.1 NVIC优先级设置
```
中断优先级 (0=最高, 15=最低):
- TIM2 (CRSF定时器): Priority 0, Subpriority 0
- SysTick (系统时钟): Priority 0, Subpriority 0
- EXTI1 (混音器): Priority 1, Subpriority 0
- EXTI4-15 (UART处理): Priority 2, Subpriority 0
- EXTI3 (后台任务): Priority 3, Subpriority 0
- USART1: Priority 4, Subpriority 0
- DMA1 Ch1 (ADC): Priority 5, Subpriority 0
- DMA1 Ch2/3 (UART): Priority 4, Subpriority 1/2
- I2C2: Priority 6, Subpriority 0
- USB: Priority 7, Subpriority 0
- TIM7 (ADC触发): Priority 5, Subpriority 1
```

### 5. **DMA配置总览**

#### 5.1 DMA通道分配
```
DMA1通道分配:
- Channel 1: ADC (P2M, Circular, High Priority)
- Channel 2: USART1_TX (M2P, Normal, Medium Priority)
- Channel 3: USART1_RX (P2M, Circular, Medium Priority)
- Channel 4: I2C2_TX (M2P, Normal, Low Priority)
- Channel 5: I2C2_RX (P2M, Normal, Low Priority)
- Channel 6: 保留
- Channel 7: 保留

配置要点:
- ADC使用循环模式，连续采样
- UART RX使用循环模式，防止数据丢失
- I2C使用普通模式，按需传输
```

### 6. **代码生成配置**

#### 6.1 代码生成选项
```
Project Manager → Code Generator:
☑ Copy only necessary library files
☑ Generate peripheral initialization as pairs
☑ Keep User Code when re-generating
☑ Delete previously generated files when not re-generated
☑ Backup previously generated files

Advanced Settings:
- Driver Selector: 全部选择HAL
- Generated Function Calls: 全部启用
- Set all free pins as analog: No
```

#### 6.2 用户代码区域
```c
// main.c中的用户代码区域
/* USER CODE BEGIN Includes */
#include "crsf_protocol_v2.h"
#include "mixer.h"
#include "adc_input.h"
#include "button_input.h"
#include "oled_display.h"
#include "menu_system.h"
#include "sound.h"
#include "calibration.h"
#include "usb_cdc.h"
#include "clock_system.h"
/* USER CODE END Includes */

/* USER CODE BEGIN 2 */
// 应用程序初始化
HAL_Delay(100);  // 等待硬件稳定

// 初始化各模块
ADC_Input_Init();
Button_Input_Init();
OLED_Init();
Menu_Init();
MIXER_Init();
SOUND_Init();
USB_CDC_Init();

// 检查是否需要校准
if (Calibration_IsNeeded()) {
    Calibration_StartBoot();
}

// 启动CRSF协议
CRSF_Protocol_Init();
CLOCK_Init();  // 最后初始化时钟系统
CRSF_Protocol_Start();

// 播放开机音效
SOUND_PlayStartup();
/* USER CODE END 2 */

/* USER CODE BEGIN WHILE */
while (1)
{
    // 主循环
    System_MainLoop();

    /* USER CODE END WHILE */
    /* USER CODE BEGIN 3 */
}
/* USER CODE END 3 */
```

### 7. **配置验证清单**

#### 7.1 硬件配置检查
```
引脚配置检查:
☑ ADC: 10个通道正确配置
☑ UART: TX/RX引脚配置正确
☑ I2C: SCL/SDA引脚配置正确
☑ USB: DM/DP引脚配置正确
☑ GPIO: 按键和LED引脚配置正确
☑ PWM: 音效和震动引脚配置正确
☑ EXTI: 软件中断引脚配置正确

时钟配置检查:
☑ 系统时钟: 48MHz
☑ 外设时钟: 正确分配
☑ USB时钟: HSI48使能
☑ ADC时钟: 12MHz (合适的转换速度)
```

#### 7.2 软件配置检查
```
中断优先级检查:
☑ CRSF定时器: 最高优先级 (0)
☑ 混音器: 高优先级 (1)
☑ UART处理: 中优先级 (2)
☑ 后台任务: 低优先级 (3)

DMA配置检查:
☑ ADC DMA: 循环模式，高优先级
☑ UART DMA: TX普通模式，RX循环模式
☑ I2C DMA: 普通模式，低优先级
☑ 无DMA冲突

外设参数检查:
☑ UART波特率: 420000 (CRSF标准)
☑ I2C速度: 100kHz (标准模式)
☑ ADC分辨率: 12位
☑ PWM频率: 1kHz (音效和震动)
```

## 🔬 **技术实现细节**

### 1. **实时性能保证**

#### 1.1 时序控制架构
```c
// 中断优先级配置 (数字越小优先级越高)
NVIC_SetPriority(TIM2_IRQn, 0);        // CRSF定时器 - 最高优先级
NVIC_SetPriority(EXTI1_IRQn, 1);       // 混音器计算 - 高优先级
NVIC_SetPriority(EXTI4_IRQn, 2);       // UART处理 - 中优先级
NVIC_SetPriority(EXTI3_IRQn, 3);       // 后台任务 - 低优先级
NVIC_SetPriority(SysTick_IRQn, 0);     // 系统时钟 - 最高优先级

// 时序监控
void Timing_Monitor(void)
{
    static uint32_t last_crsf_time = 0;
    uint32_t current_time = micros();
    uint32_t period = current_time - last_crsf_time;

    // 4ms ±50μs 精度检查
    if (abs(period - 4000) > 50) {
        timing_error_count++;
        USB_CDC_Printf("CRSF timing error: %dus\n", period);
    }

    last_crsf_time = current_time;
}
```

#### 1.2 软件中断机制
```c
// 软件中断实现 (使用EXTI)
void CLOCK_RunMixer(void)
{
    // 触发PB1引脚的EXTI1中断
    HAL_NVIC_SetPendingIRQ(EXTI1_IRQn);
}

void EXTI1_IRQHandler(void)
{
    // 高优先级: 混音器计算
    MIXER_CalcChannels();

    // 清除中断标志
    __HAL_GPIO_EXTI_CLEAR_IT(GPIO_PIN_1);
}

void EXTI4_IRQHandler(void)
{
    // 中优先级: UART数据处理
    if (func_callback) {
        func_callback();
        func_callback = NULL;
    }

    __HAL_GPIO_EXTI_CLEAR_IT(GPIO_PIN_4);
}
```

### 2. **数据处理流水线**

#### 2.1 ADC数据处理
```c
// ADC数据处理流水线
Raw ADC → DMA传输 → 数字滤波 → 校准映射 → 死区处理 → 混音器输入

// 4点平均滤波器
typedef struct {
    uint16_t buffer[4];
    uint8_t index;
    uint32_t sum;
} moving_average_filter_t;

uint16_t Filter_MovingAverage(moving_average_filter_t* filter, uint16_t new_value)
{
    // 移除最旧的值
    filter->sum -= filter->buffer[filter->index];

    // 添加新值
    filter->buffer[filter->index] = new_value;
    filter->sum += new_value;

    // 更新索引
    filter->index = (filter->index + 1) % 4;

    // 返回平均值
    return filter->sum / 4;
}
```

#### 2.2 校准算法
```c
// 线性校准映射
uint16_t Calibration_ApplyLinear(uint16_t raw_value, calibration_point_t* cal)
{
    // 三点校准: min, center, max
    if (raw_value <= cal->center) {
        // 下半段: min → center 映射到 0 → 1500
        return map(raw_value, cal->min, cal->center, 0, 1500);
    } else {
        // 上半段: center → max 映射到 1500 → 3000
        return map(raw_value, cal->center, cal->max, 1500, 3000);
    }
}

// 死区处理
uint16_t DeadZone_Apply(uint16_t value, uint16_t center, uint16_t deadzone)
{
    int16_t diff = value - center;

    if (abs(diff) < deadzone) {
        return center;  // 在死区内，返回中心值
    }

    // 在死区外，线性映射
    if (diff > 0) {
        return center + map(diff - deadzone, 0, 1500 - deadzone, 0, 1500);
    } else {
        return center - map(-diff - deadzone, 0, 1500 - deadzone, 0, 1500);
    }
}
```

### 3. **通信协议栈**

#### 3.1 CRSF协议层次
```c
// CRSF协议栈
应用层: 通道数据、遥测数据
传输层: 数据包封装、CRC校验
物理层: UART串口通信

// CRSF数据包构建
uint8_t CRSF_BuildRCPacket(uint8_t* packet)
{
    crsf_rc_packet_t* rc_packet = (crsf_rc_packet_t*)packet;

    // 包头
    rc_packet->sync = CRSF_SYNC_BYTE;           // 0xC8
    rc_packet->length = CRSF_RC_PACKET_SIZE;    // 24
    rc_packet->type = CRSF_FRAMETYPE_RC;        // 0x16

    // 通道数据 (11位精度，范围172-1811)
    for (int i = 0; i < 16; i++) {
        uint16_t channel_value = MIXER_GetOutput(i);
        // 映射 0-3000 到 172-1811
        rc_packet->channels[i] = map(channel_value, 0, 3000, 172, 1811);
    }

    // CRC校验
    rc_packet->crc = CRSF_CalculateCRC(packet + 2, CRSF_RC_PACKET_SIZE - 1);

    return CRSF_RC_PACKET_SIZE + 2;  // 总长度
}
```

#### 3.2 遥测数据解析
```c
// 遥测数据解析状态机
typedef enum {
    PARSE_STATE_SYNC = 0,
    PARSE_STATE_LENGTH,
    PARSE_STATE_TYPE,
    PARSE_STATE_DATA,
    PARSE_STATE_CRC
} parse_state_t;

void CRSF_ParseTelemetry(uint8_t byte)
{
    static parse_state_t state = PARSE_STATE_SYNC;
    static uint8_t buffer[64];
    static uint8_t index = 0;
    static uint8_t expected_length = 0;

    switch (state) {
        case PARSE_STATE_SYNC:
            if (byte == CRSF_SYNC_BYTE) {
                buffer[0] = byte;
                index = 1;
                state = PARSE_STATE_LENGTH;
            }
            break;

        case PARSE_STATE_LENGTH:
            expected_length = byte;
            buffer[index++] = byte;
            state = PARSE_STATE_TYPE;
            break;

        case PARSE_STATE_TYPE:
            buffer[index++] = byte;
            state = PARSE_STATE_DATA;
            break;

        case PARSE_STATE_DATA:
            buffer[index++] = byte;
            if (index >= expected_length + 1) {
                state = PARSE_STATE_CRC;
            }
            break;

        case PARSE_STATE_CRC:
            buffer[index++] = byte;

            // 校验CRC
            uint8_t calc_crc = CRSF_CalculateCRC(buffer + 2, expected_length - 1);
            if (calc_crc == byte) {
                // 处理遥测数据
                CRSF_ProcessTelemetryFrame(buffer);
            }

            state = PARSE_STATE_SYNC;
            index = 0;
            break;
    }
}
```

### 4. **内存管理策略**

#### 4.1 静态内存分配
```c
// 全局缓冲区定义
uint16_t adc_buffer[ADC_CHANNELS * 4];          // ADC DMA缓冲区
uint8_t crsf_tx_buffer[CRSF_TX_BUFFER_SIZE];    // CRSF发送缓冲区
uint8_t crsf_rx_buffer[CRSF_RX_BUFFER_SIZE];    // CRSF接收缓冲区
uint8_t usb_tx_buffer[USB_TX_BUFFER_SIZE];      // USB发送缓冲区
uint8_t usb_rx_buffer[USB_RX_BUFFER_SIZE];      // USB接收缓冲区

// 内存使用统计
void Memory_GetUsage(memory_info_t* info)
{
    extern uint32_t _estack;
    extern uint32_t _sstack;
    extern uint32_t _edata;
    extern uint32_t _sdata;
    extern uint32_t _ebss;
    extern uint32_t _sbss;

    // 栈使用情况
    uint32_t stack_size = (uint32_t)&_estack - (uint32_t)&_sstack;
    uint32_t stack_used = (uint32_t)&_estack - (uint32_t)__get_MSP();

    // 数据段使用情况
    uint32_t data_size = (uint32_t)&_edata - (uint32_t)&_sdata;
    uint32_t bss_size = (uint32_t)&_ebss - (uint32_t)&_sbss;

    info->stack_total = stack_size;
    info->stack_used = stack_used;
    info->data_size = data_size;
    info->bss_size = bss_size;
    info->heap_free = stack_size - stack_used - data_size - bss_size;
}
```

#### 4.2 缓冲区管理
```c
// 环形缓冲区实现
typedef struct {
    uint8_t* buffer;
    uint16_t size;
    volatile uint16_t head;
    volatile uint16_t tail;
} ring_buffer_t;

bool RingBuffer_Put(ring_buffer_t* rb, uint8_t data)
{
    uint16_t next_head = (rb->head + 1) % rb->size;

    if (next_head == rb->tail) {
        return false;  // 缓冲区满
    }

    rb->buffer[rb->head] = data;
    rb->head = next_head;
    return true;
}

bool RingBuffer_Get(ring_buffer_t* rb, uint8_t* data)
{
    if (rb->head == rb->tail) {
        return false;  // 缓冲区空
    }

    *data = rb->buffer[rb->tail];
    rb->tail = (rb->tail + 1) % rb->size;
    return true;
}
```

### 5. **错误处理机制**

#### 5.1 看门狗保护
```c
// 独立看门狗配置
void Watchdog_Init(void)
{
    hiwdg.Instance = IWDG;
    hiwdg.Init.Prescaler = IWDG_PRESCALER_64;   // 64分频
    hiwdg.Init.Window = 4095;                   // 窗口值
    hiwdg.Init.Reload = 4095;                   // 重载值 (约2.6秒)

    if (HAL_IWDG_Init(&hiwdg) != HAL_OK) {
        Error_Handler();
    }
}

// 看门狗喂狗
void Watchdog_Refresh(void)
{
    HAL_IWDG_Refresh(&hiwdg);

    // 记录喂狗时间
    static uint32_t last_feed_time = 0;
    uint32_t current_time = HAL_GetTick();

    if (current_time - last_feed_time > 1000) {  // 超过1秒未喂狗
        watchdog_timeout_count++;
    }

    last_feed_time = current_time;
}
```

#### 5.2 异常处理
```c
// 硬件异常处理
void HardFault_Handler(void)
{
    // 保存异常信息
    exception_info.fault_type = FAULT_TYPE_HARD;
    exception_info.timestamp = HAL_GetTick();
    exception_info.pc = __get_PC();
    exception_info.lr = __get_LR();
    exception_info.sp = __get_MSP();

    // 写入EEPROM
    EEPROM_WriteExceptionInfo(&exception_info);

    // 复位系统
    NVIC_SystemReset();
}

// 软件异常处理
void Error_Handler(void)
{
    // 关闭所有中断
    __disable_irq();

    // 紧急停止CRSF输出
    HAL_UART_Abort(&huart1);

    // 播放错误音效
    SOUND_PlayError();

    // 显示错误信息
    OLED_Clear();
    OLED_WriteString("SYSTEM ERROR");
    OLED_WriteString("Please restart");
    OLED_Update();

    // 等待用户操作
    while (1) {
        HAL_Delay(1000);
        HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);  // LED闪烁
    }
}
```

### 6. **性能优化技术**

#### 6.1 编译优化
```c
// 关键函数优化
__attribute__((optimize("O3")))
void MIXER_CalcChannels(void)
{
    // 混音器计算 - 最高优化级别
}

__attribute__((always_inline))
inline uint16_t ADC_GetFilteredValue(uint8_t channel)
{
    // 内联函数 - 减少函数调用开销
}

__attribute__((section(".ccmram")))
volatile uint16_t mixer_outputs[16];  // 放入CCM RAM (如果有)
```

#### 6.2 缓存优化
```c
// 数据预取和缓存友好的数据结构
typedef struct {
    uint16_t channels[16];      // 连续存储，缓存友好
    uint32_t timestamp;
    uint8_t flags;
    uint8_t reserved[3];        // 对齐到4字节边界
} __attribute__((packed, aligned(4))) mixer_data_t;

// 循环展开优化
void CRSF_PackChannels(uint16_t* channels, uint8_t* packet)
{
    // 手动循环展开，减少循环开销
    packet[0] = channels[0] & 0xFF;
    packet[1] = (channels[0] >> 8) | ((channels[1] & 0x07) << 3);
    packet[2] = (channels[1] >> 5) | ((channels[2] & 0x3F) << 6);
    // ... 继续展开
}
```

### 7. **调试和测试**

#### 7.1 调试接口
```c
// USB CDC调试输出
void Debug_Printf(const char* format, ...)
{
    #ifdef DEBUG_ENABLED
    char buffer[256];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    USB_CDC_Transmit((uint8_t*)buffer, strlen(buffer));
    #endif
}

// 性能监控
void Performance_Monitor(void)
{
    static uint32_t last_monitor_time = 0;
    uint32_t current_time = HAL_GetTick();

    if (current_time - last_monitor_time >= 1000) {  // 每秒监控
        // CPU使用率计算
        uint32_t idle_time = idle_counter;
        idle_counter = 0;
        cpu_usage = 100 - (idle_time * 100 / 48000000);  // 48MHz

        // 内存使用情况
        memory_info_t mem_info;
        Memory_GetUsage(&mem_info);

        Debug_Printf("CPU: %d%%, RAM: %d/%d bytes\n",
                    cpu_usage,
                    mem_info.stack_used + mem_info.data_size + mem_info.bss_size,
                    16384);  // 16KB RAM

        last_monitor_time = current_time;
    }
}
```

#### 7.2 单元测试
```c
// 混音器测试
bool Test_Mixer(void)
{
    // 设置测试输入
    mixer_inputs[MIXER_INPUT_CH1] = 1500;  // 中心位置
    mixer_inputs[MIXER_INPUT_CH2] = 2000;  // 最大位置

    // 执行混音计算
    MIXER_CalcChannels();

    // 验证输出
    uint16_t output1 = MIXER_GetOutput(MIXER_OUTPUT_CH1);
    uint16_t output2 = MIXER_GetOutput(MIXER_OUTPUT_CH2);

    // 检查结果
    if (output1 != 1500 || output2 != 2000) {
        Debug_Printf("Mixer test failed: CH1=%d, CH2=%d\n", output1, output2);
        return false;
    }

    return true;
}

// CRSF协议测试
bool Test_CRSF(void)
{
    uint8_t packet[32];
    uint8_t length = CRSF_BuildRCPacket(packet);

    // 验证包头
    if (packet[0] != 0xC8 || packet[1] != 24 || packet[2] != 0x16) {
        Debug_Printf("CRSF packet header error\n");
        return false;
    }

    // 验证CRC
    uint8_t calc_crc = CRSF_CalculateCRC(packet + 2, 23);
    if (calc_crc != packet[25]) {
        Debug_Printf("CRSF CRC error: calc=%02X, recv=%02X\n", calc_crc, packet[25]);
        return false;
    }

    return true;
}
```

## 🎯 **总结**

CRSF Controller遥控器是一个集成了多项先进技术的专业级控制系统：

### 🚀 **核心优势**
- **实时性能**: 4ms精确CRSF周期，确保控制响应及时
- **高精度输入**: 12位ADC + 数字滤波，提供4096级精度
- **智能混音**: 支持复杂的混音规则和多种飞行器类型
- **用户友好**: 直观的OLED界面和完善的菜单系统
- **安全可靠**: 多重保护机制和故障恢复功能

### 🔧 **技术特色**
- **Deviation架构**: 分层设计，模块化开发
- **软件中断**: 高效的任务调度机制
- **内存优化**: 静态分配，零碎片化
- **错误处理**: 完善的异常处理和恢复机制
- **性能监控**: 实时的系统状态监控

### 📈 **应用场景**
- **竞技FPV**: 高频率低延迟，适合竞速飞行
- **航拍摄影**: 精确控制，适合专业航拍
- **特技飞行**: 复杂混音，支持各种特技动作
- **教学培训**: 完善的功能，适合飞行培训

这份详细的说明书为用户提供了全面的技术参考，帮助充分发挥CRSF Controller的强大功能！
