# STM32CubeMX完整配置指导

## 🎯 配置目标

为CRSF_Controller项目配置STM32F072VBT6，实现完整的遥控器功能：
- **13路ADC采样** (摇杆、开关、电位器、电压监测)
- **CRSF协议通信** (420000波特率UART)
- **USB通信** (固件升级、调试)
- **I2C接口** (OLED显示、EEPROM存储)
- **PWM输出** (蜂鸣器、震动电机)
- **软中断系统** (混音器、音效、后台任务)

## 🚀 第一步：新建项目

### 1. 启动STM32CubeMX
```
File → New Project → Board Selector
搜索: STM32F072VBT6
选择: STM32F072VBTx
点击: Start Project
```

### 2. 基本设置
```
Project Manager → Project:
- Project Name: CRSF_Controller
- Project Location: 选择您的工作目录
- Toolchain/IDE: STM32CubeIDE
- Firmware Package: Latest (建议1.11.0+)
```

## ⚡ 第二步：时钟配置

### 1. RCC配置
```
Pinout & Configuration → System Core → RCC:

High Speed Clock (HSE): Crystal/Ceramic Resonator
Low Speed Clock (LSE): Disable (不使用32.768kHz晶振)
Master Clock Output: Disable

Additional Settings:
☑ HSI48: Enable (USB时钟源)
☑ LSI: Enable (看门狗时钟源)
```

### 2. 时钟树配置
```
Clock Configuration 标签页:

Input frequency: 8 (MHz) - 外部晶振
PLLMUL: x6 (8MHz × 6 = 48MHz)
System Clock Mux: PLLCLK
HCLK: 48 MHz
PCLK1: 48 MHz
ADC Clock: 12 MHz (PCLK1/4)
USB Clock: 48 MHz (HSI48)
```

## 📌 第三步：引脚配置

### 1. ADC输入配置 (13路)
```
在Pinout视图中，右键点击以下引脚:

PA0 → ADC_IN0  (CH1 - 右摇杆X)
PA1 → ADC_IN1  (CH2 - 右摇杆Y)  
PA2 → ADC_IN2  (CH3 - 左摇杆Y)
PA3 → ADC_IN3  (CH4 - 左摇杆X)
PA4 → ADC_IN4  (SWA - 三段开关A)
PA5 → ADC_IN5  (SWB - 三段开关B)
PA6 → ADC_IN6  (VRA - 电位器A)
PA7 → ADC_IN7  (VRB - 电位器B)
PC0 → ADC_IN10 (VBAT - 电池电压)
PC1 → ADC_IN11 (BIN - 外接电源)
PC2 → ADC_IN12 (VRA - 电位器A)
PC3 → ADC_IN13 (VRB - 电位器B)
PC5 → ADC_IN15 (VBAT - 电池电压)
```

### 2. UART配置 (CRSF通信)
```
PB6 → USART1_TX
PB7 → USART1_RX
```

### 3. I2C配置 (OLED + EEPROM)
```
PB10 → I2C2_SCL
PB11 → I2C2_SDA
```

### 4. USB配置
```
PA11 → USB_DM
PA12 → USB_DP
```

### 5. PWM输出配置
```
PA8 → TIM1_CH1 (蜂鸣器PWM)
PE3 → TIM3_CH1 (震动电机PWM)
```

### 6. 软中断配置
```
PB1 → GPIO_EXTI1 (混音器计算)
PB2 → GPIO_EXTI2 (音效处理)
PB3 → GPIO_EXTI3 (后台任务)
PB4 → GPIO_EXTI4 (UART处理)
```

### 7. 按键输入配置
```
PC6 → GPIO_Input (KEY3 - 起落架/下键)
PC7 → GPIO_Input (KEY2 - 自动返航/上键)
PC8 → GPIO_Input (KEY1 - 双控对频/退出键)
PC9 → GPIO_Input (KEY0 - 一键起落/确定键)
PD14 → GPIO_Input (KEY5 - 照片视频/左键)
PD15 → GPIO_Input (KEY4 - 快门/右键)
```

### 8. 电源管理配置
```
PD11 → GPIO_Output (PWR_SW - 电源开关)
PD12 → GPIO_Output (PWR_OFF - 电源控制)
PD13 → GPIO_Input (STDBY - 充电状态)
```

### 9. LED指示配置
```
PB8 → GPIO_Output (LED4 - 蓝色LED)
PC4 → GPIO_Output (LED5 - 红色LED)
```

### 10. 调试接口
```
PA13 → SYS_SWDIO
PA14 → SYS_SWCLK
```

## 🔧 第四步：外设详细配置

### 1. ADC配置
```
Pinout & Configuration → Analog → ADC:

Parameter Settings:
- Clock Prescaler: Synchronous clock mode divided by 4
- Resolution: ADC_RESOLUTION_12B (12位)
- Data Alignment: ADC_DATAALIGN_RIGHT
- Scan Conversion Mode: ENABLE
- Continuous Conversion Mode: ENABLE
- DMA Continuous Requests: ENABLE
- External Trigger Conversion Source: Timer 15 Trigger Out event
- External Trigger Conversion Edge: Rising edge
- Number Of Conversion: 13

Rank配置 (按顺序):
Rank 1: Channel 0 (PA0), Sampling Time: 239.5 Cycles
Rank 2: Channel 1 (PA1), Sampling Time: 239.5 Cycles
Rank 3: Channel 2 (PA2), Sampling Time: 239.5 Cycles
Rank 4: Channel 3 (PA3), Sampling Time: 239.5 Cycles
Rank 5: Channel 4 (PA4), Sampling Time: 239.5 Cycles
Rank 6: Channel 5 (PA5), Sampling Time: 239.5 Cycles
Rank 7: Channel 6 (PA6), Sampling Time: 239.5 Cycles
Rank 8: Channel 7 (PA7), Sampling Time: 239.5 Cycles
Rank 9: Channel 10 (PC0), Sampling Time: 239.5 Cycles
Rank 10: Channel 11 (PC1), Sampling Time: 239.5 Cycles
Rank 11: Channel 12 (PC2), Sampling Time: 239.5 Cycles
Rank 12: Channel 13 (PC3), Sampling Time: 239.5 Cycles
Rank 13: Channel 15 (PC5), Sampling Time: 239.5 Cycles

DMA Settings:
☑ Add DMA Request: ADC
- Mode: Circular
- Priority: High
- Data Width: Half Word (16-bit)
```

### 2. TIM15配置 (ADC触发)
```
Pinout & Configuration → Timers → TIM15:

Mode: Internal Clock

Parameter Settings:
- Prescaler: 47 (48MHz/48 = 1MHz)
- Counter Period: 999 (1000μs = 1ms)
- auto-reload preload: Enable
- Trigger Event Selection: Update Event

NVIC Settings:
☑ TIM15 global interrupt: Enable, Priority: 5
```

### 3. TIM2配置 (CRSF定时器)
```
Pinout & Configuration → Timers → TIM2:

Clock Source: Internal Clock
Channel1: Output Compare CH1 No Output

Parameter Settings:
- Prescaler: 47 (1MHz时钟)
- Counter Period: 0xFFFFFFFF (32位最大值)
- Output Compare1 Pulse: 0

NVIC Settings:
☑ TIM2 global interrupt: Enable, Priority: 0 (最高)
```

### 4. TIM1配置 (蜂鸣器PWM)
```
Pinout & Configuration → Timers → TIM1:

Channel1: PWM Generation CH1

Parameter Settings:
- Prescaler: 47 (1MHz时钟)
- Counter Period: 1000 (1kHz PWM)
- Pulse: 0 (初始关闭)
```

### 5. TIM3配置 (震动电机PWM)
```
Pinout & Configuration → Timers → TIM3:

Channel1: PWM Generation CH1

Parameter Settings:
- Prescaler: 47 (1MHz时钟)
- Counter Period: 1000 (1kHz PWM)
- Pulse: 0 (初始关闭)
```

## 📡 第五步：通信接口配置

### 1. USART1配置 (CRSF)
```
Pinout & Configuration → Connectivity → USART1:

Mode: Asynchronous

Parameter Settings:
- Baud Rate: 420000 (CRSF标准)
- Word Length: 8 Bits
- Parity: None
- Stop Bits: 1
- Data Direction: Receive and Transmit
- Over Sampling: 16 Samples

DMA Settings:
☑ Add DMA Request: USART1_RX
- Mode: Circular, Priority: Medium
☑ Add DMA Request: USART1_TX  
- Mode: Normal, Priority: Medium

NVIC Settings:
☑ USART1 global interrupt: Enable, Priority: 4
```

### 2. I2C2配置 (OLED + EEPROM)
```
Pinout & Configuration → Connectivity → I2C2:

Mode: I2C

Parameter Settings:
- I2C Speed Mode: Standard Mode
- I2C Clock Speed: 100000 Hz
- Clock No Stretch Mode: Disable
- General Call Address Detection: Disable
- Primary Address Length selection: 7-bit

DMA Settings:
☑ Add DMA Request: I2C2_RX
- Mode: Normal, Priority: Low
☑ Add DMA Request: I2C2_TX
- Mode: Normal, Priority: Low

NVIC Settings:
☑ I2C2 event interrupt: Enable, Priority: 6
☑ I2C2 error interrupt: Enable, Priority: 6
```

### 3. USB配置
```
Pinout & Configuration → Connectivity → USB:

Mode: Device (FS)
Class: Communication Device Class (Virtual Port Com)

Parameter Settings:
- Device Descriptor FS: CDC (Virtual Port Com)
- Product String FS: "CRSF Controller"
- Manufacturer String FS: "CRSF Team"

NVIC Settings:
☑ USB global interrupt: Enable, Priority: 7 (最低)
```

## 🔧 第六步：GPIO详细配置

### 1. 软中断引脚配置
```
System Core → GPIO:

PB1 (EXTI_MIXER):
- GPIO mode: External Interrupt Mode with Rising/Falling edge trigger
- GPIO Pull-up/Pull-down: Pull-up
- User Label: EXTI_MIXER

PB2 (EXTI_SOUND):
- GPIO mode: External Interrupt Mode with Falling edge trigger
- GPIO Pull-up/Pull-down: Pull-up  
- User Label: EXTI_SOUND

PB3 (EXTI_BTN1):
- GPIO mode: External Interrupt Mode with Falling edge trigger
- GPIO Pull-up/Pull-down: Pull-up
- User Label: EXTI_BTN1

PB4 (EXTI_BTN2):
- GPIO mode: External Interrupt Mode with Falling edge trigger
- GPIO Pull-up/Pull-down: Pull-up
- User Label: EXTI_BTN2
```

### 2. 按键引脚配置
```
PC6-PC9, PD14-PD15:
- GPIO mode: Input mode
- GPIO Pull-up/Pull-down: Pull-up
- User Label: KEY0-KEY5
```

### 3. 输出引脚配置
```
PB8, PC4 (LED):
- GPIO mode: Output Push Pull
- GPIO Pull-up/Pull-down: No pull-up and no pull-down
- Maximum output speed: Low
- User Label: LED4, LED5

PD11, PD12 (电源控制):
- GPIO mode: Output Push Pull
- GPIO Pull-up/Pull-down: No pull-up and no pull-down
- Maximum output speed: Low
- User Label: PWR_SW, PWR_OFF
```

## 🚨 第七步：中断优先级配置

### NVIC Settings配置
```
System Core → NVIC:

☑ TIM2 global interrupt: Priority 0, Subpriority 0 (CRSF定时器)
☑ EXTI line[0:1] interrupts: Priority 1, Subpriority 0 (混音器)
☑ EXTI line[4:15] interrupts: Priority 2, Subpriority 0 (UART处理)
☑ EXTI line[2:3] interrupts: Priority 3, Subpriority 0 (音效+后台)
☑ USART1 global interrupt: Priority 4, Subpriority 0
☑ TIM15 global interrupt: Priority 5, Subpriority 0 (ADC触发)
☑ I2C2 event interrupt: Priority 6, Subpriority 0
☑ I2C2 error interrupt: Priority 6, Subpriority 0
☑ USB global interrupt: Priority 7, Subpriority 0 (最低)
```

## 🛡️ 第八步：看门狗配置

### IWDG配置
```
Pinout & Configuration → System Core → IWDG:

☑ Activated

Parameter Settings:
- IWDG counter clock prescaler: 32
- IWDG down-counter reload value: 2312
- IWDG window value: 4095

计算: 超时时间 = (2312 × 32) / 37000 ≈ 2.0秒
```

## 🎯 第九步：DMA配置总结

### DMA1通道分配
```
DMA1 Channel1: ADC (Circular, High Priority)
DMA1 Channel2: USART1_TX (Normal, Medium Priority)  
DMA1 Channel3: USART1_RX (Circular, Medium Priority)
DMA1 Channel4: I2C2_TX (Normal, Low Priority)
DMA1 Channel5: I2C2_RX (Normal, Low Priority)
```

## ✅ 第十步：生成代码

### 1. 项目设置
```
Project Manager → Code Generator:

☑ Copy only the necessary library files
☑ Generate peripheral initialization as a pair of '.c/.h' files per peripheral
☑ Keep User Code when re-generating
☑ Delete previously generated files when not re-generated
```

### 2. 生成代码
```
点击: GENERATE CODE
等待生成完成
```

## 🔍 配置验证清单

### 1. 时钟系统 ✅
```
□ HSE: 8MHz晶振
□ PLL: ×6倍频 = 48MHz
□ SYSCLK: 48MHz
□ HCLK: 48MHz
□ PCLK1: 48MHz
□ ADC Clock: 12MHz
□ USB Clock: 48MHz (HSI48)
```

### 2. ADC系统 ✅
```
□ 13个ADC通道配置
□ TIM15触发源 (1kHz)
□ DMA1_Channel1循环传输
□ 12位分辨率，右对齐
□ 239.5周期采样时间
□ 连续转换模式
```

### 3. 通信接口 ✅
```
□ USART1: 420000波特率，DMA收发
□ I2C2: 100kHz，DMA支持
□ USB: CDC虚拟串口
□ 所有中断优先级正确设置
```

### 4. 定时器系统 ✅
```
□ TIM2: CRSF定时器，比较匹配模式
□ TIM15: ADC触发，1ms周期
□ TIM1: 蜂鸣器PWM，1kHz
□ TIM3: 震动电机PWM，1kHz
```

### 5. 软中断系统 ✅
```
□ PB1/EXTI1: 混音器计算 (优先级1)
□ PB2/EXTI2: 音效处理 (优先级3)
□ PB3/EXTI3: 后台任务 (优先级3)
□ PB4/EXTI4: UART处理 (优先级2)
```

### 6. GPIO配置 ✅
```
□ 6个按键输入 (上拉电阻)
□ 2个LED输出
□ 2个电源控制输出
□ 调试接口 (SWD)
```

### 7. 安全系统 ✅
```
□ IWDG看门狗: 2秒超时
□ 所有中断优先级合理分配
□ DMA通道无冲突
```

## 🚀 下一步操作

### 1. 代码生成后检查
```
1. 检查生成的main.c中的初始化顺序
2. 验证所有外设初始化函数存在
3. 确认DMA配置正确
4. 检查中断向量表
```

### 2. 集成应用代码
```
1. 复制CRSF_Controller源代码到项目
2. 修改包含路径
3. 更新Makefile或项目配置
4. 编译测试
```

### 3. 硬件测试
```
1. 上电测试 - 检查电源指示
2. USB连接测试 - 虚拟串口识别
3. ADC测试 - 摇杆和开关读数
4. UART测试 - CRSF通信
5. I2C测试 - OLED显示
```

---
*配置完成后，您将获得一个完整的CRSF_Controller硬件抽象层*
*所有外设都已正确配置，可以直接集成应用层代码*
*建议按照清单逐项验证，确保配置无误*
