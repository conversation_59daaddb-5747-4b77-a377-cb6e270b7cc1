# CRSF Controller 任务安全编程指南

## ⚠️ 任务阻塞问题分析

### 🚨 **当前调度器的严重问题**

您的担心完全正确！当前的协作式调度器存在严重的阻塞风险：

#### 调度器执行流程
```c
TIM6中断 (1ms) → TaskScheduler_Tick() → TaskScheduler_RunTask() → task->function()
                                                                        ↓
                                                              如果任务阻塞 → 整个系统停止！
```

#### 危险代码示例
```c
// ❌ 这些代码会导致系统死锁！
void Bad_Task(void* parameters) {
    delay_ms(100);              // 阻塞100ms - 系统死锁！
    HAL_Delay(50);              // 阻塞50ms - 系统死锁！
    while(condition);           // 无限等待 - 系统死锁！
    HAL_I2C_Transmit(..., 1000); // 阻塞1秒 - 系统死锁！
}
```

### 💥 **发现的危险代码**

#### 1. **EEPROM等待函数** (eeprom.c:317-329)
```c
static error_code_t EEPROM_WaitReady(uint32_t timeout_ms) {
    uint32_t start_time = millis();
    while (millis() - start_time < timeout_ms) {  // ❌ 阻塞等待！
        if (HAL_I2C_IsDeviceReady(&hi2c2, EEPROM_I2C_ADDR << 1, 1, 1) == HAL_OK) {
            return ERR_OK;
        }
        delay_ms(1);  // ❌ 1ms阻塞！
    }
    return ERR_TIMEOUT;
}
```

#### 2. **主程序中的延时** (main.c:51,60,70)
```c
delay_ms(2000);  // ❌ 2秒阻塞 (虽然在任务启动前)
delay_ms(50);    // ❌ 50ms阻塞
delay_ms(2000);  // ❌ 2秒阻塞
```

#### 3. **微秒延时函数** (hal_drivers.c:303-307)
```c
void delay_us(uint32_t us) {
    uint32_t start = micros();
    while ((micros() - start) < us);  // ❌ 忙等待阻塞！
}
```

## 🛡️ **任务安全编程原则**

### ✅ **DO - 正确的做法**

#### 1. **状态机模式**
```c
typedef enum {
    STATE_IDLE,
    STATE_WAITING,
    STATE_PROCESSING,
    STATE_COMPLETE
} task_state_t;

void Safe_Task(void* parameters) {
    static task_state_t state = STATE_IDLE;
    static uint32_t start_time = 0;
    
    switch (state) {
        case STATE_IDLE:
            // 启动操作
            HAL_I2C_Transmit_IT(&hi2c2, addr, data, len);
            state = STATE_WAITING;
            start_time = millis();
            break;
            
        case STATE_WAITING:
            // 检查是否完成
            if (i2c_complete_flag) {
                state = STATE_PROCESSING;
            } else if (millis() - start_time > TIMEOUT) {
                state = STATE_IDLE;  // 超时重试
            }
            break;
            
        case STATE_PROCESSING:
            // 处理结果
            process_data();
            state = STATE_COMPLETE;
            break;
            
        case STATE_COMPLETE:
            // 完成，回到空闲
            state = STATE_IDLE;
            break;
    }
}
```

#### 2. **非阻塞延时**
```c
void Safe_Delay_Task(void* parameters) {
    static uint32_t last_action_time = 0;
    static bool action_pending = false;
    
    uint32_t current_time = millis();
    
    if (!action_pending) {
        // 启动延时
        last_action_time = current_time;
        action_pending = true;
    } else if (current_time - last_action_time >= DELAY_TIME) {
        // 延时到达，执行动作
        do_action();
        action_pending = false;
    }
    // 其他情况：什么都不做，直接返回
}
```

#### 3. **中断驱动I/O**
```c
// ✅ 使用中断模式
HAL_I2C_Transmit_IT(&hi2c2, addr, data, len);  // 非阻塞
HAL_UART_Transmit_DMA(&huart1, data, len);     // 非阻塞
HAL_ADC_Start_DMA(&hadc1, buffer, size);       // 非阻塞

// 在中断回调中处理完成事件
void HAL_I2C_MasterTxCpltCallback(I2C_HandleTypeDef *hi2c) {
    i2c_complete_flag = true;  // 设置完成标志
}
```

### ❌ **DON'T - 禁止的做法**

#### 1. **任何形式的阻塞等待**
```c
// ❌ 绝对禁止！
delay_ms(any_time);
HAL_Delay(any_time);
while(condition);
for(long_loop);
HAL_I2C_Transmit(..., timeout);  // 阻塞模式
HAL_UART_Transmit(..., timeout); // 阻塞模式
```

#### 2. **长时间计算**
```c
// ❌ 禁止长时间计算
void Bad_Task(void* parameters) {
    for (int i = 0; i < 1000000; i++) {  // 长循环
        complex_calculation();
    }
}

// ✅ 分片处理
void Good_Task(void* parameters) {
    static int progress = 0;
    const int CHUNK_SIZE = 100;
    
    int end = MIN(progress + CHUNK_SIZE, 1000000);
    for (int i = progress; i < end; i++) {
        complex_calculation();
    }
    progress = end;
    
    if (progress >= 1000000) {
        progress = 0;  // 重置
    }
}
```

## 🔧 **修复现有问题**

### 1. **修复EEPROM等待函数**
```c
// 修改为非阻塞版本
typedef struct {
    bool waiting;
    uint32_t start_time;
    uint32_t timeout_ms;
} eeprom_wait_context_t;

static eeprom_wait_context_t wait_context = {false, 0, 0};

error_code_t EEPROM_WaitReady_NonBlocking(uint32_t timeout_ms) {
    if (!wait_context.waiting) {
        // 开始等待
        wait_context.waiting = true;
        wait_context.start_time = millis();
        wait_context.timeout_ms = timeout_ms;
        return ERR_BUSY;  // 表示正在等待
    }
    
    // 检查是否就绪
    if (HAL_I2C_IsDeviceReady(&hi2c2, EEPROM_I2C_ADDR << 1, 1, 1) == HAL_OK) {
        wait_context.waiting = false;
        return ERR_OK;  // 就绪
    }
    
    // 检查超时
    if (millis() - wait_context.start_time >= wait_context.timeout_ms) {
        wait_context.waiting = false;
        return ERR_TIMEOUT;  // 超时
    }
    
    return ERR_BUSY;  // 继续等待
}
```

### 2. **修复任务中的延时**
```c
void Buzzer_Task(void* parameters) {
    (void)parameters;
    
    // ✅ 非阻塞检查
    if (buzzer_playing && buzzer_stop_time > 0) {
        if (millis() >= buzzer_stop_time) {
            Buzzer_Off();
        }
    }
    // 直接返回，不阻塞
}
```

### 3. **添加任务执行时间监控**
```c
// 在task_scheduler.c中添加
#define MAX_TASK_EXEC_TIME_US  1000  // 最大执行时间1ms

static void TaskScheduler_RunTask(uint8_t task_id) {
    // ... 现有代码 ...
    
    uint32_t start_time = micros();
    task->function(task->parameters);
    uint32_t exec_time = micros() - start_time;
    
    // 检查执行时间
    if (exec_time > MAX_TASK_EXEC_TIME_US) {
        // 记录超时任务
        task->timeout_count++;
        // 可以发送警告或禁用任务
        USB_CDC_Printf("WARN: Task %s exceeded %dus (actual: %dus)\n", 
                      task->name, MAX_TASK_EXEC_TIME_US, exec_time);
    }
    
    // ... 现有代码 ...
}
```

## 📊 **任务性能监控**

### 实时监控任务执行时间
```c
void Debug_PrintTaskStats(void) {
    USB_CDC_Printf("=== Task Performance ===\n");
    for (uint8_t i = 0; i < MAX_TASKS; i++) {
        task_control_block_t* task = &tasks[i];
        if (task->function != NULL) {
            uint32_t avg_time = task->total_exec_time / MAX(task->run_count, 1);
            USB_CDC_Printf("%s: avg=%dus max=%dus runs=%d\n",
                          task->name, avg_time, task->max_exec_time, task->run_count);
        }
    }
}
```

## 🎯 **最佳实践总结**

### ✅ **任务设计原则**
1. **快进快出**: 任务执行时间 < 1ms
2. **状态机**: 复杂操作分解为状态机
3. **非阻塞**: 使用中断和DMA
4. **分片处理**: 长计算分多次执行
5. **超时检查**: 所有等待都要有超时

### 🔍 **调试技巧**
1. **执行时间监控**: 记录每个任务的执行时间
2. **状态输出**: 定期输出任务状态
3. **看门狗**: 使用硬件看门狗检测死锁
4. **LED指示**: 用LED显示系统运行状态

### ⚡ **性能目标**
- **任务执行时间**: < 1ms
- **调度器开销**: < 100μs
- **系统响应**: < 2ms
- **CPU利用率**: < 80%

遵循这些原则，您的CRSF遥控器将具有**可靠的实时性能**和**稳定的系统响应**！
