# simpleTx_esp32

This project its based on kkbin505 work on:

https://github.com/kkbin505/Arduino-Transmitter-for-ELRS

and mix up code from Expresslrs and Deviationtx 

https://github.com/ExpressLRS/ExpressLRS

https://github.com/DeviationTX/deviation

By default gps support is disabled.
For Gps support replace "//define GPSCO" with "define GPSCO" in "oled.h" file.
Gps displayes latitude,longitude with number of satellite.



its based on esp32 mcu

planning to make a simple customizable handset for whatever you want/need.

more updates comming :D
