/**
 * @file buzzer_vibrator.h
 * @brief 蜂鸣器和震动电机驱动接口
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __BUZZER_VIBRATOR_H
#define __BUZZER_VIBRATOR_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"

/* 蜂鸣器音调定义 (Hz) */
#define BUZZER_FREQ_LOW         500
#define BUZZER_FREQ_MID         1000
#define BUZZER_FREQ_HIGH        2000
#define BUZZER_FREQ_BEEP        1500
#define BUZZER_FREQ_WARNING     800
#define BUZZER_FREQ_ERROR       400

/* 震动强度定义 (0-100%) */
#define VIBRATOR_OFF            0
#define VIBRATOR_LOW            30
#define VIBRATOR_MID            60
#define VIBRATOR_HIGH           100

/* 预定义音效类型 */
typedef enum {
    SOUND_NONE = 0,
    SOUND_BEEP_SHORT,       // 短哔声
    SOUND_BEEP_LONG,        // 长哔声
    SOUND_BEEP_DOUBLE,      // 双哔声
    SOUND_BEEP_TRIPLE,      // 三哔声
    SOUND_WARNING,          // 警告音
    SOUND_ERROR,            // 错误音
    SOUND_SUCCESS,          // 成功音
    SOUND_STARTUP,          // 开机音
    SOUND_SHUTDOWN,         // 关机音
    SOUND_MENU_ENTER,       // 菜单进入音
    SOUND_MENU_EXIT,        // 菜单退出音
    SOUND_BUTTON_PRESS,     // 按键音
    SOUND_ALARM             // 报警音
} sound_type_t;

/* 震动模式定义 */
typedef enum {
    VIBRATE_NONE = 0,
    VIBRATE_SHORT,          // 短震动
    VIBRATE_LONG,           // 长震动
    VIBRATE_DOUBLE,         // 双震动
    VIBRATE_TRIPLE,         // 三震动
    VIBRATE_PULSE,          // 脉冲震动
    VIBRATE_CONTINUOUS,     // 连续震动
    VIBRATE_WARNING,        // 警告震动
    VIBRATE_ERROR           // 错误震动
} vibrate_type_t;

/* 音效配置结构 */
typedef struct {
    uint16_t frequency;     // 频率 (Hz)
    uint16_t duration;      // 持续时间 (ms)
    uint8_t volume;         // 音量 (0-100%)
} sound_config_t;

/* 震动配置结构 */
typedef struct {
    uint8_t intensity;      // 强度 (0-100%)
    uint16_t duration;      // 持续时间 (ms)
    uint16_t interval;      // 间隔时间 (ms)
    uint8_t repeat;         // 重复次数
} vibrate_config_t;

/* 全局变量声明 */
extern bool buzzer_enabled;
extern bool vibrator_enabled;

/* 函数声明 */

/* 初始化和配置 */
error_code_t Buzzer_Init(void);
error_code_t Vibrator_Init(void);
void Buzzer_Enable(bool enable);
void Vibrator_Enable(bool enable);

/* 蜂鸣器控制 */
void Buzzer_SetFrequency(uint16_t frequency);
void Buzzer_SetVolume(uint8_t volume);
void Buzzer_On(void);
void Buzzer_Off(void);
void Buzzer_Beep(uint16_t frequency, uint16_t duration);
void Buzzer_PlaySound(sound_type_t sound);
void Buzzer_PlayCustom(const sound_config_t* config, uint8_t count);

/* 震动电机控制 */
void Vibrator_SetIntensity(uint8_t intensity);
void Vibrator_On(void);
void Vibrator_Off(void);
void Vibrator_Pulse(uint8_t intensity, uint16_t duration);
void Vibrator_Vibrate(vibrate_type_t type);
void Vibrator_VibrateCustom(const vibrate_config_t* config);

/* 组合反馈 */
void Feedback_ButtonPress(void);
void Feedback_MenuEnter(void);
void Feedback_MenuExit(void);
void Feedback_Warning(void);
void Feedback_Error(void);
void Feedback_Success(void);
void Feedback_Startup(void);
void Feedback_Shutdown(void);
void Feedback_LowBattery(void);
void Feedback_ConnectionLost(void);
void Feedback_ConnectionRestored(void);

/* 状态查询 */
bool Buzzer_IsPlaying(void);
bool Vibrator_IsActive(void);
bool Buzzer_IsEnabled(void);
bool Vibrator_IsEnabled(void);

/* 任务函数 */
void Buzzer_Task(void* parameters);
void Vibrator_Task(void* parameters);

/* 工具函数 */
uint16_t Buzzer_NoteToFreq(uint8_t note);
void Buzzer_PlayMelody(const uint16_t* melody, const uint16_t* durations, uint8_t length);

/* 音符定义 (MIDI音符号) */
#define NOTE_C4     60
#define NOTE_CS4    61
#define NOTE_D4     62
#define NOTE_DS4    63
#define NOTE_E4     64
#define NOTE_F4     65
#define NOTE_FS4    66
#define NOTE_G4     67
#define NOTE_GS4    68
#define NOTE_A4     69
#define NOTE_AS4    70
#define NOTE_B4     71
#define NOTE_C5     72

/* 预定义音效时长 */
#define DURATION_SHORT      100     // 短音 100ms
#define DURATION_MEDIUM     300     // 中音 300ms
#define DURATION_LONG       500     // 长音 500ms

/* 预定义震动时长 */
#define VIBRATE_SHORT_MS    50      // 短震动 50ms
#define VIBRATE_MEDIUM_MS   150     // 中震动 150ms
#define VIBRATE_LONG_MS     300     // 长震动 300ms

#ifdef __cplusplus
}
#endif

#endif /* __BUZZER_VIBRATOR_H */
