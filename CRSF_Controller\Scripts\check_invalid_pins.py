#!/usr/bin/env python3
"""
检查IOC配置中不存在的引脚
根据引脚图验证哪些引脚在STM32F072VBT6中实际不存在
"""

import re
import sys
from pathlib import Path

class InvalidPinChecker:
    def __init__(self, ioc_file_path):
        self.ioc_file_path = Path(ioc_file_path)
        
        # 根据官方引脚图严格定义的实际存在的引脚
        self.VALID_PINS = {
            # 左侧引脚
            'PC2', 'PC3', 'PF2', 'PF3',
            'PA0', 'PA1', 'PA2',

            # 底部引脚
            'PA3', 'PA4', 'PA5', 'PA6', 'PA7',
            'PC4', 'PC5',
            'PB0', 'PB1', 'PB2',
            'PE7', 'PE8', 'PE9', 'PE10', 'PE11', 'PE12', 'PE13', 'PE14', 'PE15',
            'PB10', 'PB11',

            # 右侧引脚
            'PD12', 'PD11', 'PD10', 'PD9', 'PD8',
            'PB15', 'PB14', 'PB13', 'PB12',

            # 顶部引脚
            'PE1', 'PE0', 'PB9', 'PB8', 'PB7', 'PB6', 'PB5', 'PB4', 'PB3',
            'PD7', 'PD6', 'PD5', 'PD4', 'PD3', 'PD2', 'PD1', 'PD0',
            'PC12', 'PC11', 'PC10', 'PA15', 'PA14',

            # 内部引脚
            'PE2', 'PE3', 'PE4', 'PE5', 'PE6',
            'PC13', 'PC14', 'PC15', 'PF9', 'PF10',
            'PC0', 'PC1',
            'PA13', 'PA12', 'PA11', 'PA10', 'PA9', 'PA8',
            'PC9', 'PC8', 'PC7', 'PC6',
            'PD15', 'PD14', 'PD13',

            # 振荡器引脚
            'PF0-OSC_IN', 'PF1-OSC_OUT',
            'PC14-OSC32_IN', 'PC15-OSC32_OUT'
        }

        # 当前配置中使用但可能不在引脚图中的引脚（需要检查）
        self.QUESTIONABLE_PINS = {
            'PA0', 'PA1', 'PA8', 'PA9', 'PA10', 'PA11', 'PA12', 'PA13', 'PA14',
            'PB3', 'PB4', 'PB5', 'PB6', 'PB7', 'PB8', 'PB9',
            'PC0', 'PC1', 'PC2', 'PC3', 'PC6', 'PC7', 'PC8', 'PC9',
            'PD11', 'PD12', 'PD13', 'PD14', 'PD15',
            'PE0', 'PE1', 'PE3'
        }
        
    def load_ioc_config(self):
        """加载IOC配置文件"""
        configured_pins = set()
        
        try:
            with open(self.ioc_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找所有引脚配置
            pin_pattern = r'^(P[A-F]\d+(?:-[A-Z_]+)?)\.'
            matches = re.findall(pin_pattern, content, re.MULTILINE)
            
            for pin in matches:
                configured_pins.add(pin)
                
            return configured_pins
            
        except Exception as e:
            print(f"❌ 无法读取IOC文件: {e}")
            return set()
    
    def check_invalid_pins(self):
        """检查无效引脚"""
        print("STM32F072VBT6 引脚图对比检查工具")
        print("=" * 50)

        configured_pins = self.load_ioc_config()
        if not configured_pins:
            return

        print(f"✅ 发现 {len(configured_pins)} 个配置的引脚")

        # 分类检查引脚
        confirmed_valid = configured_pins & self.VALID_PINS
        questionable = configured_pins & self.QUESTIONABLE_PINS
        invalid = configured_pins - self.VALID_PINS - self.QUESTIONABLE_PINS

        print(f"\n=== 引脚分类检查 ===")
        print(f"✅ 引脚图中确认存在: {len(confirmed_valid)} 个")
        print(f"❓ 需要确认的引脚: {len(questionable)} 个")
        print(f"❌ 明确不存在的引脚: {len(invalid)} 个")

        if confirmed_valid:
            print(f"\n✅ 引脚图中确认存在的引脚:")
            for pin in sorted(confirmed_valid):
                print(f"   ✅ {pin}")

        if questionable:
            print(f"\n❓ 需要确认的引脚（可能存在但不在引脚图中显示）:")
            for pin in sorted(questionable):
                print(f"   ❓ {pin}")

        if invalid:
            print(f"\n❌ 明确不存在的引脚:")
            for pin in sorted(invalid):
                print(f"   ❌ {pin}")

            print(f"\n💡 建议:")
            print(f"   1. 从IOC配置中移除明确不存在的引脚")
            print(f"   2. 确认需要确认的引脚是否真实存在")
            print(f"   3. 重新生成代码")
        else:
            print(f"\n🎉 没有发现明确不存在的引脚！")

        return len(invalid) == 0

def main():
    if len(sys.argv) != 2:
        print("用法: python check_invalid_pins.py <ioc_file_path>")
        sys.exit(1)
        
    ioc_file = sys.argv[1]
    checker = InvalidPinChecker(ioc_file)
    
    success = checker.check_invalid_pins()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
