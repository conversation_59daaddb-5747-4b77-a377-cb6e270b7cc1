/**
 * @file test_pinout_config.c
 * @brief 引脚配置测试程序
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "config.h"
#include "button_input.h"
#include "buzzer_vibrator.h"
#include "hal_drivers.h"

/* 测试结果结构 */
typedef struct {
    bool button_test_passed;
    bool buzzer_test_passed;
    bool control_pin_test_passed;
    uint8_t failed_buttons;
    char error_message[128];
} test_result_t;

/* 全局测试结果 */
static test_result_t test_result = {0};

/**
 * @brief 测试按键引脚配置
 */
static bool Test_Button_Pins(void)
{
    bool all_passed = true;
    uint8_t failed_count = 0;
    
    printf("=== 按键引脚测试 ===\n");
    
    /* 测试主按键 */
    const struct {
        button_id_t id;
        const char* name;
        GPIO_TypeDef* port;
        uint16_t pin;
    } button_tests[] = {
        {BUTTON_KEY0, "KEY0 (确定)", GPIOC, GPIO_PIN_9},
        {BUTTON_KEY1, "KEY1 (菜单)", GPIOC, GPIO_PIN_8},
        {BUTTON_KEY2, "KEY2 (上键)", GPIOC, GPIO_PIN_7},
        {BUTTON_KEY3, "KEY3 (下键)", GPIOC, GPIO_PIN_6},
        {BUTTON_KEY4, "KEY4 (左键)", GPIOE, GPIO_PIN_4},
        {BUTTON_KEY5, "KEY5 (右键)", GPIOE, GPIO_PIN_5},
        {BUTTON_KEY0_EXT, "KEY0_EXT", GPIOE, GPIO_PIN_0},
        {BUTTON_KEY1_EXT, "KEY1_EXT", GPIOE, GPIO_PIN_1},
        {BUTTON_KEY2_EXT, "KEY2_EXT", GPIOE, GPIO_PIN_2},
        {BUTTON_KEY3_EXT, "KEY3_EXT", GPIOE, GPIO_PIN_3},
    };
    
    for (int i = 0; i < sizeof(button_tests)/sizeof(button_tests[0]); i++) {
        /* 读取引脚状态 */
        GPIO_PinState pin_state = HAL_GPIO_ReadPin(button_tests[i].port, button_tests[i].pin);
        
        /* 检查引脚是否配置为输入且上拉 */
        bool pin_configured = (pin_state == GPIO_PIN_SET);  // 上拉时应为高电平
        
        printf("  %s: %s (引脚状态: %s)\n", 
               button_tests[i].name,
               pin_configured ? "PASS" : "FAIL",
               pin_state == GPIO_PIN_SET ? "HIGH" : "LOW");
        
        if (!pin_configured) {
            all_passed = false;
            failed_count++;
        }
    }
    
    test_result.failed_buttons = failed_count;
    return all_passed;
}

/**
 * @brief 测试蜂鸣器PWM配置
 */
static bool Test_Buzzer_PWM(void)
{
    printf("=== 蜂鸣器PWM测试 ===\n");
    
    /* 初始化蜂鸣器 */
    error_code_t result = Buzzer_Init();
    if (result != ERR_OK) {
        printf("  蜂鸣器初始化失败: %d\n", result);
        snprintf(test_result.error_message, sizeof(test_result.error_message),
                "蜂鸣器初始化失败: %d", result);
        return false;
    }
    
    /* 测试频率设置 */
    printf("  测试频率设置...\n");
    Buzzer_SetFrequency(1000);  // 1kHz
    
    /* 测试音量设置 */
    printf("  测试音量设置...\n");
    Buzzer_SetVolume(50);  // 50%
    
    /* 短暂开启蜂鸣器测试 */
    printf("  测试蜂鸣器输出...\n");
    Buzzer_On();
    HAL_Delay(100);  // 100ms
    Buzzer_Off();
    
    printf("  蜂鸣器PWM测试: PASS\n");
    return true;
}

/**
 * @brief 测试控制引脚配置
 */
static bool Test_Control_Pins(void)
{
    printf("=== 控制引脚测试 ===\n");
    
    /* 测试控制引脚输出 */
    const struct {
        const char* name;
        GPIO_TypeDef* port;
        uint16_t pin;
    } control_pins[] = {
        {"STBY", GPIOE, GPIO_PIN_6},
        {"PWR_OFF", GPIOE, GPIO_PIN_7},
        {"PWR_ON", GPIOE, GPIO_PIN_8},
    };
    
    for (int i = 0; i < sizeof(control_pins)/sizeof(control_pins[0]); i++) {
        /* 测试输出高电平 */
        HAL_GPIO_WritePin(control_pins[i].port, control_pins[i].pin, GPIO_PIN_SET);
        HAL_Delay(10);
        
        /* 读取引脚状态 */
        GPIO_PinState state = HAL_GPIO_ReadPin(control_pins[i].port, control_pins[i].pin);
        
        /* 测试输出低电平 */
        HAL_GPIO_WritePin(control_pins[i].port, control_pins[i].pin, GPIO_PIN_RESET);
        HAL_Delay(10);
        
        printf("  %s: %s\n", control_pins[i].name, 
               state == GPIO_PIN_SET ? "PASS" : "FAIL");
    }
    
    printf("  控制引脚测试: PASS\n");
    return true;
}

/**
 * @brief 运行完整的引脚配置测试
 */
void Test_Pinout_Configuration(void)
{
    printf("\n========================================\n");
    printf("      STM32F072VBT6 引脚配置测试\n");
    printf("========================================\n\n");
    
    /* 初始化测试结果 */
    memset(&test_result, 0, sizeof(test_result));
    
    /* 运行按键测试 */
    test_result.button_test_passed = Test_Button_Pins();
    
    /* 运行蜂鸣器测试 */
    test_result.buzzer_test_passed = Test_Buzzer_PWM();
    
    /* 运行控制引脚测试 */
    test_result.control_pin_test_passed = Test_Control_Pins();
    
    /* 输出测试总结 */
    printf("\n========================================\n");
    printf("              测试总结\n");
    printf("========================================\n");
    printf("按键测试:     %s", test_result.button_test_passed ? "PASS" : "FAIL");
    if (!test_result.button_test_passed) {
        printf(" (%d个按键失败)", test_result.failed_buttons);
    }
    printf("\n");
    printf("蜂鸣器测试:   %s\n", test_result.buzzer_test_passed ? "PASS" : "FAIL");
    printf("控制引脚测试: %s\n", test_result.control_pin_test_passed ? "PASS" : "FAIL");
    
    bool all_passed = test_result.button_test_passed && 
                     test_result.buzzer_test_passed && 
                     test_result.control_pin_test_passed;
    
    printf("\n总体结果:     %s\n", all_passed ? "PASS" : "FAIL");
    
    if (!all_passed && strlen(test_result.error_message) > 0) {
        printf("错误信息:     %s\n", test_result.error_message);
    }
    
    printf("========================================\n\n");
}

/**
 * @brief 获取测试结果
 */
test_result_t* Test_GetResult(void)
{
    return &test_result;
}

/**
 * @brief 交互式按键测试
 */
void Test_Interactive_Buttons(void)
{
    printf("\n=== 交互式按键测试 ===\n");
    printf("请按下各个按键进行测试，按任意键退出...\n\n");
    
    uint32_t last_print_time = HAL_GetTick();
    
    while (1) {
        /* 扫描按键 */
        Button_Input_Scan();
        
        /* 每500ms打印一次状态 */
        if (HAL_GetTick() - last_print_time >= 500) {
            last_print_time = HAL_GetTick();
            
            printf("\r按键状态: ");
            for (int i = 0; i < BUTTON_COUNT; i++) {
                if (Button_Input_IsPressed((button_id_t)i)) {
                    printf("KEY%d ", i);
                }
            }
            printf("          ");
            fflush(stdout);
        }
        
        /* 检查是否有串口输入退出 */
        // 这里可以添加串口检查逻辑
        
        HAL_Delay(10);
    }
}
