/**
 * @file clock_system.h
 * @brief 时钟系统和软件中断架构 (基于deviation设计)
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __CLOCK_SYSTEM_H
#define __CLOCK_SYSTEM_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"

/* 优先级定义 */
typedef enum {
    PRIORITY_HIGH = 0,      // 高优先级 (EXTI0) - 紧急处理
    PRIORITY_MEDIUM,        // 中优先级 (EXTI1) - 混音器计算
    PRIORITY_LOW,           // 低优先级 (EXTI2) - 后台任务
    PRIORITY_SOUND,         // 音效优先级 (EXTI3) - 音效处理
    PRIORITY_COUNT
} priority_level_t;

/* 混音器同步状态 */
typedef enum {
    MIX_NOT_DONE = 0,
    MIX_DONE,
    MIX_TIMER
} mixer_sync_t;

/* 时间回调函数类型 */
typedef uint16_t (*timer_callback_t)(void);
typedef void (*func_callback_t)(void);

/* 全局变量声明 */
extern volatile uint32_t msecs;
extern volatile uint32_t wdg_time;
extern volatile uint8_t priority_ready;
extern volatile mixer_sync_t mixer_sync;

/* 函数声明 */

/* 时钟系统初始化 */
error_code_t CLOCK_Init(void);

/* 定时器管理 */
void CLOCK_StartTimer(uint32_t us, timer_callback_t cb);
void CLOCK_StopTimer(void);
uint32_t CLOCK_GetTime(void);

/* 软件中断触发 */
void CLOCK_RunMixer(void);          // PB1: 混音器计算 - 高优先级
void CLOCK_RunOnce(func_callback_t cb);     // PB3: 一次性函数 - 低优先级
void CLOCK_RunUART(func_callback_t cb);     // PB4: UART处理 - 中优先级

/* 震动电机控制 */
void CLOCK_StartVibrator(uint32_t duration_ms);
void CLOCK_StopVibrator(void);
bool CLOCK_IsVibratorActive(void);

/* 优先级任务管理 */
void CLOCK_SetCallback(priority_level_t priority, uint32_t interval_ms);
void CLOCK_ClearCallback(priority_level_t priority);
bool CLOCK_IsReady(priority_level_t priority);
void CLOCK_ClearReady(priority_level_t priority);

/* 主循环处理 */
void CLOCK_ProcessEvents(void);

/* 看门狗管理 */
void CLOCK_ResetWatchdog(void);
bool CLOCK_IsWatchdogExpired(void);

/* 中断服务程序 (在stm32f0xx_it.c中实现) */
void EXTI0_1_IRQHandler(void);   // EXTI1: 混音器计算 (PB1)
void EXTI2_3_IRQHandler(void);   // EXTI3: 一次性函数 (PB3)
void EXTI4_15_IRQHandler(void);  // EXTI4: UART处理 (PB4)
void TIM2_IRQHandler(void);      // 定时器中断

/* 时间间隔定义 */
#define HIGH_PRIORITY_MSEC      1       // 1ms
#define MEDIUM_PRIORITY_MSEC    2       // 2ms  
#define LOW_PRIORITY_MSEC       10      // 10ms
#define SOUND_PRIORITY_MSEC     5       // 5ms

/* 看门狗超时 */
#define WATCHDOG_TIMEOUT_MS     2000    // 2秒

#ifdef __cplusplus
}
#endif

#endif /* __CLOCK_SYSTEM_H */
