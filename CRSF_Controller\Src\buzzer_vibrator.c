/**
 * @file buzzer_vibrator.c
 * @brief 蜂鸣器和震动电机驱动实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "buzzer_vibrator.h"
#include "hal_drivers.h"

/* 全局变量定义 */
bool buzzer_enabled = true;
bool vibrator_enabled = true;

/* 私有变量 */
static bool buzzer_playing = false;
static bool vibrator_active = false;
static uint32_t buzzer_stop_time = 0;
static uint32_t vibrator_stop_time = 0;

/* 音符频率表 (MIDI音符到频率的转换) */
static const uint16_t note_frequencies[] = {
    262, 277, 294, 311, 330, 349, 370, 392, 415, 440, 466, 494, 523  // C4-C5
};

/* 预定义音效配置 */
static const sound_config_t sound_configs[] = {
    {0, 0, 0},                                      // SOUND_NONE
    {BUZZER_FREQ_BEEP, DURATION_SHORT, 50},         // SOUND_BEEP_SHORT
    {BUZZER_FREQ_BEEP, DURATION_LONG, 50},          // SOUND_BEEP_LONG
    {BUZZER_FREQ_BEEP, DURATION_SHORT, 50},         // SOUND_BEEP_DOUBLE (第一声)
    {BUZZER_FREQ_BEEP, DURATION_SHORT, 50},         // SOUND_BEEP_TRIPLE (第一声)
    {BUZZER_FREQ_WARNING, DURATION_LONG, 70},       // SOUND_WARNING
    {BUZZER_FREQ_ERROR, DURATION_LONG, 80},         // SOUND_ERROR
    {BUZZER_FREQ_HIGH, DURATION_MEDIUM, 60},        // SOUND_SUCCESS
    {BUZZER_FREQ_MID, DURATION_MEDIUM, 50},         // SOUND_STARTUP
    {BUZZER_FREQ_LOW, DURATION_LONG, 50},           // SOUND_SHUTDOWN
    {BUZZER_FREQ_HIGH, DURATION_SHORT, 40},         // SOUND_MENU_ENTER
    {BUZZER_FREQ_LOW, DURATION_SHORT, 40},          // SOUND_MENU_EXIT
    {BUZZER_FREQ_MID, 50, 30},                      // SOUND_BUTTON_PRESS
    {BUZZER_FREQ_ERROR, 200, 100}                   // SOUND_ALARM
};

/* 预定义震动配置 */
static const vibrate_config_t vibrate_configs[] = {
    {0, 0, 0, 0},                                   // VIBRATE_NONE
    {VIBRATOR_MID, VIBRATE_SHORT_MS, 0, 1},        // VIBRATE_SHORT
    {VIBRATOR_MID, VIBRATE_LONG_MS, 0, 1},         // VIBRATE_LONG
    {VIBRATOR_MID, VIBRATE_SHORT_MS, 100, 2},      // VIBRATE_DOUBLE
    {VIBRATOR_MID, VIBRATE_SHORT_MS, 100, 3},      // VIBRATE_TRIPLE
    {VIBRATOR_LOW, 100, 100, 5},                   // VIBRATE_PULSE
    {VIBRATOR_MID, 1000, 0, 1},                    // VIBRATE_CONTINUOUS
    {VIBRATOR_HIGH, 200, 200, 3},                  // VIBRATE_WARNING
    {VIBRATOR_HIGH, 500, 0, 1}                     // VIBRATE_ERROR
};

/**
 * @brief 蜂鸣器初始化
 */
error_code_t Buzzer_Init(void)
{
    /* TIM1已在HAL_TIM_Init_All()中初始化 */
    
    /* 启动PWM输出 */
    if (HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }
    
    /* 初始状态关闭 */
    Buzzer_Off();
    
    return ERR_OK;
}

/**
 * @brief 震动电机初始化
 */
error_code_t Vibrator_Init(void)
{
    /* TIM3已在HAL_TIM_Init_All()中初始化 */
    
    /* 启动PWM输出 */
    if (HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_1) != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }
    
    /* 初始状态关闭 */
    Vibrator_Off();
    
    return ERR_OK;
}

/**
 * @brief 设置蜂鸣器频率
 */
void Buzzer_SetFrequency(uint16_t frequency)
{
    if (frequency == 0) {
        Buzzer_Off();
        return;
    }
    
    /* 计算ARR和CCR值 */
    uint32_t timer_freq = 1000000;  // 1MHz (48MHz / 48)
    uint32_t period = timer_freq / frequency;
    
    if (period > 65535) period = 65535;
    if (period < 1) period = 1;
    
    /* 更新定时器配置 */
    __HAL_TIM_SET_AUTORELOAD(&htim1, period - 1);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, period / 2);  // 50%占空比
}

/**
 * @brief 设置蜂鸣器音量
 */
void Buzzer_SetVolume(uint8_t volume)
{
    if (volume > 100) volume = 100;
    
    uint32_t period = __HAL_TIM_GET_AUTORELOAD(&htim1) + 1;
    uint32_t pulse = (period * volume) / 100;
    
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, pulse);
}

/**
 * @brief 蜂鸣器开启
 */
void Buzzer_On(void)
{
    if (!buzzer_enabled) return;
    
    HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
    buzzer_playing = true;
}

/**
 * @brief 蜂鸣器关闭
 */
void Buzzer_Off(void)
{
    HAL_TIM_PWM_Stop(&htim1, TIM_CHANNEL_1);
    buzzer_playing = false;
    buzzer_stop_time = 0;
}

/**
 * @brief 蜂鸣器哔声
 */
void Buzzer_Beep(uint16_t frequency, uint16_t duration)
{
    if (!buzzer_enabled) return;
    
    Buzzer_SetFrequency(frequency);
    Buzzer_On();
    
    buzzer_stop_time = millis() + duration;
}

/**
 * @brief 播放预定义音效
 */
void Buzzer_PlaySound(sound_type_t sound)
{
    if (!buzzer_enabled || sound >= sizeof(sound_configs)/sizeof(sound_configs[0])) {
        return;
    }
    
    const sound_config_t* config = &sound_configs[sound];
    
    if (config->frequency > 0) {
        Buzzer_SetFrequency(config->frequency);
        Buzzer_SetVolume(config->volume);
        Buzzer_On();
        buzzer_stop_time = millis() + config->duration;
    }
}

/**
 * @brief 设置震动强度
 */
void Vibrator_SetIntensity(uint8_t intensity)
{
    if (intensity > 100) intensity = 100;
    
    uint32_t period = __HAL_TIM_GET_AUTORELOAD(&htim3) + 1;
    uint32_t pulse = (period * intensity) / 100;
    
    __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, pulse);
}

/**
 * @brief 震动电机开启
 */
void Vibrator_On(void)
{
    if (!vibrator_enabled) return;
    
    HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_1);
    vibrator_active = true;
}

/**
 * @brief 震动电机关闭
 */
void Vibrator_Off(void)
{
    HAL_TIM_PWM_Stop(&htim3, TIM_CHANNEL_1);
    vibrator_active = false;
    vibrator_stop_time = 0;
}

/**
 * @brief 震动脉冲
 */
void Vibrator_Pulse(uint8_t intensity, uint16_t duration)
{
    if (!vibrator_enabled) return;
    
    Vibrator_SetIntensity(intensity);
    Vibrator_On();
    
    vibrator_stop_time = millis() + duration;
}

/**
 * @brief 播放预定义震动
 */
void Vibrator_Vibrate(vibrate_type_t type)
{
    if (!vibrator_enabled || type >= sizeof(vibrate_configs)/sizeof(vibrate_configs[0])) {
        return;
    }
    
    const vibrate_config_t* config = &vibrate_configs[type];
    
    if (config->intensity > 0) {
        Vibrator_SetIntensity(config->intensity);
        Vibrator_On();
        vibrator_stop_time = millis() + config->duration;
    }
}

/**
 * @brief 蜂鸣器任务
 */
void Buzzer_Task(void* parameters)
{
    (void)parameters;
    
    /* 检查是否需要停止蜂鸣器 */
    if (buzzer_playing && buzzer_stop_time > 0) {
        if (millis() >= buzzer_stop_time) {
            Buzzer_Off();
        }
    }
}

/**
 * @brief 震动电机任务
 */
void Vibrator_Task(void* parameters)
{
    (void)parameters;
    
    /* 检查是否需要停止震动 */
    if (vibrator_active && vibrator_stop_time > 0) {
        if (millis() >= vibrator_stop_time) {
            Vibrator_Off();
        }
    }
}

/**
 * @brief 按键反馈
 */
void Feedback_ButtonPress(void)
{
    Buzzer_PlaySound(SOUND_BUTTON_PRESS);
    Vibrator_Vibrate(VIBRATE_SHORT);
}

/**
 * @brief 菜单进入反馈
 */
void Feedback_MenuEnter(void)
{
    Buzzer_PlaySound(SOUND_MENU_ENTER);
    Vibrator_Vibrate(VIBRATE_SHORT);
}

/**
 * @brief 菜单退出反馈
 */
void Feedback_MenuExit(void)
{
    Buzzer_PlaySound(SOUND_MENU_EXIT);
    Vibrator_Vibrate(VIBRATE_SHORT);
}

/**
 * @brief 警告反馈
 */
void Feedback_Warning(void)
{
    Buzzer_PlaySound(SOUND_WARNING);
    Vibrator_Vibrate(VIBRATE_WARNING);
}

/**
 * @brief 错误反馈
 */
void Feedback_Error(void)
{
    Buzzer_PlaySound(SOUND_ERROR);
    Vibrator_Vibrate(VIBRATE_ERROR);
}

/**
 * @brief 成功反馈
 */
void Feedback_Success(void)
{
    Buzzer_PlaySound(SOUND_SUCCESS);
    Vibrator_Vibrate(VIBRATE_DOUBLE);
}

/**
 * @brief 开机反馈
 */
void Feedback_Startup(void)
{
    Buzzer_PlaySound(SOUND_STARTUP);
    Vibrator_Vibrate(VIBRATE_LONG);
}

/**
 * @brief 关机反馈
 */
void Feedback_Shutdown(void)
{
    Buzzer_PlaySound(SOUND_SHUTDOWN);
    Vibrator_Vibrate(VIBRATE_LONG);
}

/**
 * @brief 状态查询函数
 */
bool Buzzer_IsPlaying(void) { return buzzer_playing; }
bool Vibrator_IsActive(void) { return vibrator_active; }
bool Buzzer_IsEnabled(void) { return buzzer_enabled; }
bool Vibrator_IsEnabled(void) { return vibrator_enabled; }

void Buzzer_Enable(bool enable) { buzzer_enabled = enable; }
void Vibrator_Enable(bool enable) { vibrator_enabled = enable; }
