/**
 * @file usb_cdc.c
 * @brief USB虚拟串口(CDC)驱动实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "usb_cdc.h"
#include "hal_drivers.h"
#include <stdarg.h>

/* 全局变量定义 */
USBD_HandleTypeDef hUsbDeviceFS;
usb_cdc_stats_t usb_cdc_stats;

/* 私有变量 */
static uint8_t usb_rx_buffer[USB_CDC_RX_BUFFER_SIZE];
static uint8_t usb_tx_buffer[USB_CDC_TX_BUFFER_SIZE];
static volatile uint16_t rx_head = 0;
static volatile uint16_t rx_tail = 0;
static volatile uint16_t tx_head = 0;
static volatile uint16_t tx_tail = 0;
static volatile bool usb_connected = false;
static volatile bool usb_configured = false;
static volatile bool tx_busy = false;

/* 私有函数声明 */
static uint16_t USB_CDC_GetRxBufferCount(void);
static uint16_t USB_CDC_GetTxBufferCount(void);
static void USB_CDC_PutRxData(uint8_t* data, uint32_t length);
static uint16_t USB_CDC_GetTxData(uint8_t* data, uint16_t max_length);

/**
 * @brief USB CDC初始化
 */
error_code_t USB_CDC_Init(void)
{
    /* 初始化统计信息 */
    memset(&usb_cdc_stats, 0, sizeof(usb_cdc_stats));
    
    /* 初始化缓冲区 */
    rx_head = rx_tail = 0;
    tx_head = tx_tail = 0;
    usb_connected = false;
    usb_configured = false;
    tx_busy = false;
    
    /* 初始化USB设备 */
    if (USBD_Init(&hUsbDeviceFS, &FS_Desc, DEVICE_FS) != USBD_OK) {
        return ERR_HARDWARE_FAULT;
    }
    
    /* 注册CDC类 */
    if (USBD_RegisterClass(&hUsbDeviceFS, &USBD_CDC) != USBD_OK) {
        return ERR_HARDWARE_FAULT;
    }
    
    /* 注册CDC接口 */
    if (USBD_CDC_RegisterInterface(&hUsbDeviceFS, &USBD_Interface_fops_FS) != USBD_OK) {
        return ERR_HARDWARE_FAULT;
    }
    
    return ERR_OK;
}

/**
 * @brief 启动USB CDC
 */
error_code_t USB_CDC_Start(void)
{
    if (USBD_Start(&hUsbDeviceFS) != USBD_OK) {
        return ERR_HARDWARE_FAULT;
    }
    
    return ERR_OK;
}

/**
 * @brief 停止USB CDC
 */
error_code_t USB_CDC_Stop(void)
{
    if (USBD_Stop(&hUsbDeviceFS) != USBD_OK) {
        return ERR_HARDWARE_FAULT;
    }
    
    usb_connected = false;
    usb_configured = false;
    
    return ERR_OK;
}

/**
 * @brief 发送数据
 */
error_code_t USB_CDC_Transmit(const uint8_t* data, uint16_t length)
{
    if (!usb_configured || data == NULL || length == 0) {
        return ERR_INVALID_PARAM;
    }
    
    /* 检查发送缓冲区空间 */
    uint16_t free_space = USB_CDC_TX_BUFFER_SIZE - USB_CDC_GetTxBufferCount();
    if (length > free_space) {
        usb_cdc_stats.tx_errors++;
        return ERR_BUFFER_FULL;
    }
    
    /* 将数据放入发送缓冲区 */
    for (uint16_t i = 0; i < length; i++) {
        usb_tx_buffer[tx_head] = data[i];
        tx_head = (tx_head + 1) % USB_CDC_TX_BUFFER_SIZE;
    }
    
    /* 启动发送 */
    USB_CDC_ProcessTxData();
    
    usb_cdc_stats.bytes_sent += length;
    return ERR_OK;
}

/**
 * @brief 发送字符串
 */
error_code_t USB_CDC_TransmitString(const char* str)
{
    if (str == NULL) {
        return ERR_INVALID_PARAM;
    }
    
    return USB_CDC_Transmit((const uint8_t*)str, strlen(str));
}

/**
 * @brief 格式化发送
 */
error_code_t USB_CDC_Printf(const char* format, ...)
{
    char buffer[256];
    va_list args;
    
    va_start(args, format);
    int length = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    if (length > 0) {
        return USB_CDC_Transmit((const uint8_t*)buffer, length);
    }
    
    return ERR_INVALID_PARAM;
}

/**
 * @brief 接收数据
 */
uint16_t USB_CDC_Receive(uint8_t* data, uint16_t max_length)
{
    if (data == NULL || max_length == 0) {
        return 0;
    }
    
    uint16_t count = 0;
    uint16_t available = USB_CDC_GetRxBufferCount();
    uint16_t to_read = MIN(max_length, available);
    
    for (uint16_t i = 0; i < to_read; i++) {
        data[count++] = usb_rx_buffer[rx_tail];
        rx_tail = (rx_tail + 1) % USB_CDC_RX_BUFFER_SIZE;
    }
    
    return count;
}

/**
 * @brief 获取可用数据数量
 */
uint16_t USB_CDC_Available(void)
{
    return USB_CDC_GetRxBufferCount();
}

/**
 * @brief 检查是否连接
 */
bool USB_CDC_IsConnected(void)
{
    return usb_connected;
}

/**
 * @brief 检查是否配置完成
 */
bool USB_CDC_IsConfigured(void)
{
    return usb_configured;
}

/**
 * @brief 检查发送是否忙
 */
bool USB_CDC_IsTxBusy(void)
{
    return tx_busy;
}

/**
 * @brief 处理发送数据
 */
void USB_CDC_ProcessTxData(void)
{
    if (!usb_configured || tx_busy) {
        return;
    }
    
    uint16_t count = USB_CDC_GetTxBufferCount();
    if (count == 0) {
        return;
    }
    
    /* 准备发送数据 */
    static uint8_t tx_packet[USB_CDC_MAX_PACKET_SIZE];
    uint16_t packet_size = MIN(count, USB_CDC_MAX_PACKET_SIZE);
    
    for (uint16_t i = 0; i < packet_size; i++) {
        tx_packet[i] = usb_tx_buffer[tx_tail];
        tx_tail = (tx_tail + 1) % USB_CDC_TX_BUFFER_SIZE;
    }
    
    /* 发送数据包 */
    if (USBD_CDC_TransmitPacket(&hUsbDeviceFS) == USBD_OK) {
        if (USBD_CDC_SetTxBuffer(&hUsbDeviceFS, tx_packet, packet_size) == USBD_OK) {
            tx_busy = true;
        }
    }
}

/**
 * @brief 处理接收数据
 */
void USB_CDC_ProcessRxData(void)
{
    /* 这个函数会在CDC接收回调中被调用 */
}

/**
 * @brief USB CDC任务
 */
void USB_CDC_Task(void* parameters)
{
    (void)parameters;
    
    /* 处理发送数据 */
    if (!tx_busy) {
        USB_CDC_ProcessTxData();
    }
}

/**
 * @brief 获取接收缓冲区数据数量
 */
static uint16_t USB_CDC_GetRxBufferCount(void)
{
    return (rx_head + USB_CDC_RX_BUFFER_SIZE - rx_tail) % USB_CDC_RX_BUFFER_SIZE;
}

/**
 * @brief 获取发送缓冲区数据数量
 */
static uint16_t USB_CDC_GetTxBufferCount(void)
{
    return (tx_head + USB_CDC_TX_BUFFER_SIZE - tx_tail) % USB_CDC_TX_BUFFER_SIZE;
}

/**
 * @brief 将数据放入接收缓冲区
 */
static void USB_CDC_PutRxData(uint8_t* data, uint32_t length)
{
    for (uint32_t i = 0; i < length; i++) {
        uint16_t next_head = (rx_head + 1) % USB_CDC_RX_BUFFER_SIZE;
        if (next_head != rx_tail) {  // 缓冲区未满
            usb_rx_buffer[rx_head] = data[i];
            rx_head = next_head;
        } else {
            usb_cdc_stats.rx_errors++;  // 缓冲区溢出
            break;
        }
    }
    
    usb_cdc_stats.bytes_received += length;
}

/**
 * @brief 获取统计信息
 */
const usb_cdc_stats_t* USB_CDC_GetStats(void)
{
    return &usb_cdc_stats;
}

/**
 * @brief 重置统计信息
 */
void USB_CDC_ResetStats(void)
{
    memset(&usb_cdc_stats, 0, sizeof(usb_cdc_stats));
}

/* 回调函数 (弱定义) */
__weak void USB_CDC_OnConnect(void)
{
    usb_connected = true;
    usb_cdc_stats.connect_count++;
}

__weak void USB_CDC_OnDisconnect(void)
{
    usb_connected = false;
    usb_configured = false;
    usb_cdc_stats.disconnect_count++;
}

__weak void USB_CDC_OnDataReceived(uint8_t* data, uint32_t length)
{
    USB_CDC_PutRxData(data, length);
}

__weak void USB_CDC_OnDataSent(void)
{
    tx_busy = false;
}
