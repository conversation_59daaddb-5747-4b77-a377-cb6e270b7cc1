# CRSF定时架构详解

## 🎯 架构概述

CRSF_Controller使用基于TIM2的精确定时架构，实现微秒级的CRSF协议时序控制。

## ⚙️ TIM2定时器配置

### IOC配置
```ini
TIM2.IPParameters=Prescaler,Period
TIM2.Period=3999                    # 4ms周期 (不直接使用)
TIM2.Prescaler=47                   # 48MHz/48 = 1MHz (1μs精度)
```

### 实际用途
- **不是周期性中断**: TIM2不使用溢出中断
- **比较匹配模式**: 使用TIM_IT_CC1比较匹配中断
- **动态时间设置**: 每次中断后动态设置下次触发时间
- **微秒级精度**: 1MHz时钟提供1μs精度

## 🔄 CRSF定时工作流程

### 1. 协议启动
```c
error_code_t CRSF_Protocol_Start(void)
{
    /* 启动定时器 */
    CLOCK_StartTimer(updateInterval, CRSF_SerialCallback);
    
    /* 设置状态 */
    crsf_state = CRSF_STATE_DATA0;
    protocol_running = true;
    
    return ERR_OK;
}
```

### 2. 定时器设置
```c
void CLOCK_StartTimer(uint32_t us, timer_callback_t cb)
{
    timer_callback = cb;
    
    /* 获取当前计数值 */
    uint32_t current_count = __HAL_TIM_GET_COUNTER(&htim2);
    
    /* 设置比较值 */
    __HAL_TIM_SET_COMPARE(&htim2, TIM_CHANNEL_1, current_count + us);
    
    /* 清除中断标志并使能中断 */
    __HAL_TIM_CLEAR_FLAG(&htim2, TIM_FLAG_CC1);
    __HAL_TIM_ENABLE_IT(&htim2, TIM_IT_CC1);
}
```

### 3. 中断处理
```c
void TIM2_IRQHandler(void)
{
    if (__HAL_TIM_GET_FLAG(&htim2, TIM_FLAG_CC1) != RESET) {
        if (timer_callback) {
            uint32_t next_us = timer_callback();  // 调用CRSF_SerialCallback
            
            __HAL_TIM_CLEAR_FLAG(&htim2, TIM_FLAG_CC1);
            
            if (next_us) {
                /* 设置下次中断时间 */
                uint32_t current_ccr = __HAL_TIM_GET_COMPARE(&htim2, TIM_CHANNEL_1);
                __HAL_TIM_SET_COMPARE(&htim2, TIM_CHANNEL_1, current_ccr + next_us);
                return;
            }
        }
        CLOCK_StopTimer();
    }
}
```

## 🎮 CRSF状态机

### 状态机回调函数
```c
uint16_t CRSF_SerialCallback(void)
{
    switch (crsf_state) {
        case CRSF_STATE_DATA0:
            /* 触发混音器计算 */
            CLOCK_RunMixer();                    // 触发EXTI1中断
            crsf_state = CRSF_STATE_DATA1;
            return mixer_runtime;                // 等待混音器完成时间
            
        case CRSF_STATE_DATA1:
            /* 构建并发送数据包 */
            length = CRSF_BuildRCDataPacket(tx_packet);
            HAL_UART_Transmit_DMA(&huart1, tx_packet, length);
            
            /* 返回下一状态 */
            crsf_state = CRSF_STATE_DATA0;
            return CRSF_GetUpdateInterval() - mixer_runtime;  // 剩余时间
            
        default:
            crsf_state = CRSF_STATE_DATA0;
            return CRSF_FRAME_PERIOD;            // 4000μs
    }
}
```

### 时序分析
```
总周期: 4000μs (4ms)
├── STATE_DATA0: 触发混音器 → 等待 mixer_runtime μs
└── STATE_DATA1: 发送数据包 → 等待 (4000 - mixer_runtime) μs
```

## 🧮 时序计算

### 典型时序
```
mixer_runtime = 200μs (混音器计算时间)
updateInterval = 4000μs (CRSF周期)

STATE_DATA0 → STATE_DATA1: 200μs
STATE_DATA1 → STATE_DATA0: 3800μs
总周期: 4000μs ✅
```

### 自适应时序
- **混音器时间可变**: 根据计算复杂度调整
- **自动补偿**: 总周期始终保持4ms
- **微秒级精度**: 确保CRSF时序要求

## 🔗 与软件中断的协作

### 混音器计算触发
```c
void CLOCK_RunMixer(void)
{
    mixer_sync = MIX_NOT_DONE;
    HAL_NVIC_SetPendingIRQ(EXTI1_IRQn);  // 触发PB1/EXTI1
}
```

### EXTI1中断处理
```c
void EXTI0_1_IRQHandler(void)
{
    if (__HAL_GPIO_EXTI_GET_FLAG(GPIO_PIN_1)) {
        __HAL_GPIO_EXTI_CLEAR_FLAG(GPIO_PIN_1);

        /* ADC滤波和混音器计算 */
        ADC_Filter();
        MIXER_CalcChannels();

        if (mixer_sync == MIX_NOT_DONE) {
            mixer_sync = MIX_DONE;
        }
    }
}
```

## 📊 中断优先级配置

### 优先级层次
```
优先级0 (最高): TIM2_IRQn     - CRSF定时器 (最关键)
优先级0 (最高): SysTick_IRQn  - 系统时钟
优先级1 (高):   EXTI0_1_IRQn  - 混音器计算
优先级2 (中):   EXTI4_15_IRQn - UART处理
优先级3 (低):   EXTI2_3_IRQn  - 后台任务
```

### 设计原理
1. **TIM2最高优先级**: 确保CRSF时序精度
2. **混音器高优先级**: 保证实时计算
3. **分层处理**: 避免中断嵌套冲突

## 🔍 调试和监控

### 时序监控
```c
void CRSF_TimingMonitor(void)
{
    static uint32_t last_time = 0;
    uint32_t current_time = __HAL_TIM_GET_COUNTER(&htim2);
    uint32_t period = current_time - last_time;
    
    if (abs(period - 4000) > 10) {  // 超过10μs误差
        USB_CDC_Printf("CRSF timing error: %dus\n", period);
    }
    
    last_time = current_time;
}
```

### 状态查询
```c
crsf_state_t CRSF_GetState(void);
uint16_t CRSF_GetMixerRuntime(void);
bool CRSF_IsTransmitting(void);
```

## ✅ 架构优势

1. **微秒级精度**: TIM2比较匹配提供1μs精度
2. **动态调整**: 根据混音器时间自动调整
3. **非阻塞设计**: 软件中断异步处理
4. **高效实现**: 最小CPU开销
5. **可扩展性**: 易于添加新的时序任务

## 🚨 注意事项

1. **TIM2不能停止**: 一旦启动，必须持续运行
2. **回调函数必须快速**: timer_callback应尽快返回
3. **时间计算准确**: 返回值直接影响下次中断时间
4. **中断优先级**: TIM2必须是最高优先级

---
*架构设计: Deviation-based*
*精度: 1μs*
*周期: 4ms*
*状态: ✅ 已验证*
