/**
 * @file button_input.c
 * @brief 按键输入处理实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "button_input.h"
#include "hal_drivers.h"

/* 全局变量定义 */
button_config_t button_config;
button_status_t button_status[BUTTON_COUNT];

/* 私有变量 */
static button_callback_t button_callback = NULL;
static bool button_initialized = false;
static bool menu_mode = false;
static uint32_t last_combo_check_time = 0;

/* 私有函数声明 */
static bool Button_ReadRawState(button_id_t button);
static void Button_ProcessDebounce(button_id_t button);
static void Button_ProcessEvents(button_id_t button);
static void Button_TriggerEvent(button_id_t button, button_event_t event);

/**
 * @brief 按键输入初始化
 */
error_code_t Button_Input_Init(void)
{
    if (button_initialized) {
        return ERR_OK;
    }

    /* 初始化按键状态 */
    memset(button_status, 0, sizeof(button_status));

    /* 设置默认配置 */
    button_config.debounce_time_ms = BUTTON_DEFAULT_DEBOUNCE_TIME;
    button_config.long_press_time_ms = BUTTON_DEFAULT_LONG_PRESS_TIME;
    button_config.repeat_delay_ms = BUTTON_DEFAULT_REPEAT_DELAY;
    button_config.repeat_rate_ms = BUTTON_DEFAULT_REPEAT_RATE;
    button_config.double_click_time_ms = BUTTON_DEFAULT_DOUBLE_CLICK_TIME;
    button_config.repeat_enabled = true;
    button_config.long_press_enabled = true;
    button_config.double_click_enabled = true;

    /* 初始化所有按键状态 */
    for (uint8_t i = 0; i < BUTTON_COUNT; i++) {
        button_status[i].state = BUTTON_STATE_RELEASED;
        button_status[i].last_event = BUTTON_EVENT_NONE;
        button_status[i].raw_state = false;
        button_status[i].debounced_state = false;
        button_status[i].event_pending = false;
    }

    button_callback = NULL;
    button_initialized = true;

    return ERR_OK;
}

/**
 * @brief 配置按键参数
 */
error_code_t Button_Input_Configure(const button_config_t* config)
{
    if (config == NULL) {
        return ERR_INVALID_PARAM;
    }

    memcpy(&button_config, config, sizeof(button_config_t));
    return ERR_OK;
}

/**
 * @brief 设置按键回调函数
 */
void Button_Input_SetCallback(button_callback_t callback)
{
    button_callback = callback;
}

/**
 * @brief 扫描所有按键
 */
void Button_Input_Scan(void)
{
    if (!button_initialized) {
        return;
    }

    for (uint8_t i = 0; i < BUTTON_COUNT; i++) {
        /* 读取原始状态 */
        button_status[i].raw_state = Button_ReadRawState((button_id_t)i);
        
        /* 处理去抖 */
        Button_ProcessDebounce((button_id_t)i);
        
        /* 处理事件 */
        Button_ProcessEvents((button_id_t)i);
    }
}

/**
 * @brief 检查按键是否按下
 */
bool Button_Input_IsPressed(button_id_t button)
{
    if (button >= BUTTON_COUNT) {
        return false;
    }

    return (button_status[button].state == BUTTON_STATE_PRESSED ||
            button_status[button].state == BUTTON_STATE_LONG_PRESSED ||
            button_status[button].state == BUTTON_STATE_REPEAT);
}

/**
 * @brief 检查按键是否释放
 */
bool Button_Input_IsReleased(button_id_t button)
{
    if (button >= BUTTON_COUNT) {
        return true;
    }

    return (button_status[button].state == BUTTON_STATE_RELEASED);
}

/**
 * @brief 获取按键状态
 */
button_state_t Button_Input_GetState(button_id_t button)
{
    if (button >= BUTTON_COUNT) {
        return BUTTON_STATE_RELEASED;
    }

    return button_status[button].state;
}

/**
 * @brief 获取按键事件
 */
button_event_t Button_Input_GetEvent(button_id_t button)
{
    if (button >= BUTTON_COUNT) {
        return BUTTON_EVENT_NONE;
    }

    button_event_t event = button_status[button].last_event;
    button_status[button].last_event = BUTTON_EVENT_NONE;
    button_status[button].event_pending = false;
    
    return event;
}

/**
 * @brief 清除按键事件
 */
void Button_Input_ClearEvent(button_id_t button)
{
    if (button >= BUTTON_COUNT) {
        return;
    }

    button_status[button].last_event = BUTTON_EVENT_NONE;
    button_status[button].event_pending = false;
}

/**
 * @brief 获取所有按键状态
 */
void Button_Input_GetAllStates(button_input_t* button_input)
{
    if (button_input == NULL) {
        return;
    }

    button_input->key0 = Button_Input_IsPressed(BUTTON_KEY0);
    button_input->key1 = Button_Input_IsPressed(BUTTON_KEY1);
    button_input->key2 = Button_Input_IsPressed(BUTTON_KEY2);
    button_input->key3 = Button_Input_IsPressed(BUTTON_KEY3);
    button_input->key4 = Button_Input_IsPressed(BUTTON_KEY4);
    button_input->key5 = Button_Input_IsPressed(BUTTON_KEY5);
    button_input->pwr_sw = Button_Input_IsPressed(BUTTON_PWR_SW);
    button_input->menu_mode = menu_mode;
}

/**
 * @brief 检查是否有按键按下
 */
bool Button_Input_AnyPressed(void)
{
    for (uint8_t i = 0; i < BUTTON_COUNT; i++) {
        if (Button_Input_IsPressed((button_id_t)i)) {
            return true;
        }
    }
    return false;
}

/**
 * @brief 按键任务函数
 */
void Button_Input_Task(void* parameters)
{
    (void)parameters;

    /* 扫描按键 */
    Button_Input_Scan();

    /* 检查菜单组合键 */
    uint32_t current_time = millis();
    if (current_time - last_combo_check_time >= 50) {  // 50ms检查一次
        last_combo_check_time = current_time;

        if (Button_Input_IsMenuCombo()) {
            if (menu_mode) {
                Button_Input_ExitMenuMode();
            } else {
                Button_Input_EnterMenuMode();
            }
        }
    }
}

/**
 * @brief 检查菜单组合键 (KEY4 + KEY5)
 */
bool Button_Input_IsMenuCombo(void)
{
    return (Button_Input_IsPressed(BUTTON_KEY4) && Button_Input_IsPressed(BUTTON_KEY5));
}

/**
 * @brief 检查退出菜单组合键 (KEY4 + KEY5)
 */
bool Button_Input_IsExitMenuCombo(void)
{
    return (Button_Input_IsPressed(BUTTON_KEY4) && Button_Input_IsPressed(BUTTON_KEY5));
}

/**
 * @brief 进入菜单模式
 */
void Button_Input_EnterMenuMode(void)
{
    if (!menu_mode) {
        menu_mode = true;
        /* 播放菜单进入音效 */
        // Feedback_MenuEnter();
    }
}

/**
 * @brief 退出菜单模式
 */
void Button_Input_ExitMenuMode(void)
{
    if (menu_mode) {
        menu_mode = false;
        /* 播放菜单退出音效 */
        // Feedback_MenuExit();
    }
}

/**
 * @brief 检查是否在菜单模式
 */
bool Button_Input_IsMenuMode(void)
{
    return menu_mode;
}

/**
 * @brief 读取按键原始状态
 */
static bool Button_ReadRawState(button_id_t button)
{
    GPIO_TypeDef* port = BUTTON_GPIO_PORT(button);
    uint16_t pin = BUTTON_GPIO_PIN(button);
    
    if (port == NULL || pin == 0) {
        return false;
    }

    /* 按键按下时GPIO为低电平 (上拉输入) */
    return (HAL_GPIO_ReadPin(port, pin) == GPIO_PIN_RESET);
}

/**
 * @brief 处理按键去抖
 */
static void Button_ProcessDebounce(button_id_t button)
{
    static uint32_t last_change_time[BUTTON_COUNT] = {0};
    uint32_t current_time = millis();
    
    /* 检查状态是否改变 */
    if (button_status[button].raw_state != button_status[button].debounced_state) {
        /* 状态改变，开始去抖计时 */
        if (current_time - last_change_time[button] >= button_config.debounce_time_ms) {
            button_status[button].debounced_state = button_status[button].raw_state;
            last_change_time[button] = current_time;
        }
    } else {
        /* 状态稳定，更新时间 */
        last_change_time[button] = current_time;
    }
}

/**
 * @brief 处理按键事件
 */
static void Button_ProcessEvents(button_id_t button)
{
    uint32_t current_time = millis();
    button_status_t* status = &button_status[button];
    
    switch (status->state) {
        case BUTTON_STATE_RELEASED:
            if (status->debounced_state) {
                /* 按键按下 */
                status->state = BUTTON_STATE_PRESSED;
                status->press_time = current_time;
                Button_TriggerEvent(button, BUTTON_EVENT_PRESS);
            }
            break;
            
        case BUTTON_STATE_PRESSED:
            if (!status->debounced_state) {
                /* 按键释放 */
                status->state = BUTTON_STATE_RELEASED;
                status->release_time = current_time;
                status->click_count++;
                Button_TriggerEvent(button, BUTTON_EVENT_RELEASE);
                Button_TriggerEvent(button, BUTTON_EVENT_CLICK);
            } else if (button_config.long_press_enabled && 
                      (current_time - status->press_time) >= button_config.long_press_time_ms) {
                /* 长按 */
                status->state = BUTTON_STATE_LONG_PRESSED;
                Button_TriggerEvent(button, BUTTON_EVENT_LONG_PRESS);
                
                if (button_config.repeat_enabled) {
                    status->last_repeat_time = current_time;
                }
            }
            break;
            
        case BUTTON_STATE_LONG_PRESSED:
            if (!status->debounced_state) {
                /* 长按释放 */
                status->state = BUTTON_STATE_RELEASED;
                status->release_time = current_time;
                Button_TriggerEvent(button, BUTTON_EVENT_RELEASE);
            } else if (button_config.repeat_enabled && 
                      (current_time - status->last_repeat_time) >= button_config.repeat_delay_ms) {
                /* 开始重复 */
                status->state = BUTTON_STATE_REPEAT;
                status->last_repeat_time = current_time;
                Button_TriggerEvent(button, BUTTON_EVENT_REPEAT);
            }
            break;
            
        case BUTTON_STATE_REPEAT:
            if (!status->debounced_state) {
                /* 重复状态释放 */
                status->state = BUTTON_STATE_RELEASED;
                status->release_time = current_time;
                Button_TriggerEvent(button, BUTTON_EVENT_RELEASE);
            } else if ((current_time - status->last_repeat_time) >= button_config.repeat_rate_ms) {
                /* 继续重复 */
                status->last_repeat_time = current_time;
                Button_TriggerEvent(button, BUTTON_EVENT_REPEAT);
            }
            break;
    }
}

/**
 * @brief 触发按键事件
 */
static void Button_TriggerEvent(button_id_t button, button_event_t event)
{
    button_status[button].last_event = event;
    button_status[button].event_pending = true;
    
    /* 调用回调函数 */
    if (button_callback != NULL) {
        button_callback(button, event);
    }
}
