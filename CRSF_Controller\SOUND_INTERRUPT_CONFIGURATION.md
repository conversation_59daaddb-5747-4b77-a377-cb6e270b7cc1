# 音效中断配置 - PRIORITY_SOUND

## 🎯 配置目标

为CRSF_Controller添加音效处理专用软中断，实现音效回调和震动控制的异步处理。

## 🔍 代码分析发现

### 优先级定义 (clock_system.h)
```c
typedef enum {
    PRIORITY_HIGH = 0,      // 高优先级 (EXTI0) - 紧急处理
    PRIORITY_MEDIUM,        // 中优先级 (EXTI1) - 混音器计算
    PRIORITY_LOW,           // 低优先级 (EXTI2) - 后台任务
    PRIORITY_SOUND,         // 音效优先级 (EXTI3) - 音效处理 ⚠️
    PRIORITY_COUNT
} priority_level_t;
```

### 音效处理使用 (sound.c)
```c
void SOUND_StartWithoutVibrating(uint32_t msec, sound_callback_t next_note_cb)
{
    /* 设置回调和启动定时器 */
    CLOCK_SetCallback(PRIORITY_SOUND, msec);  // 使用PRIORITY_SOUND
    sound_callback = next_note_cb;
}

void VIBRATOR_DoublePulse(void)
{
    CLOCK_SetCallback(PRIORITY_SOUND, 150);   // 使用PRIORITY_SOUND
}
```

### 主循环处理 (clock_system.c)
```c
void CLOCK_ProcessEvents(void)
{
    /* 处理音效任务 */
    if (CLOCK_IsReady(PRIORITY_SOUND)) {
        CLOCK_ClearReady(PRIORITY_SOUND);
        
        /* 音效处理 */
        // Sound_Task();  // 需要实现
    }
}
```

## ⚙️ STM32CubeMX配置

### 新增PB2 (音效处理中断)

#### IOC配置：
```ini
PB2.GPIOParameters=GPIO_Label,GPIO_PuPd,GPIO_ModeDefaultEXTI
PB2.GPIO_Label=EXTI_SOUND
PB2.GPIO_PuPd=GPIO_PULLUP
PB2.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PB2.Locked=true
PB2.Signal=GPXTI2
```

#### 信号映射：
```ini
SH.GPXTI2.0=GPIO_EXTI2
SH.GPXTI2.ConfNb=1
```

#### NVIC配置：
```ini
NVIC.EXTI2_3_IRQn=true:3:0    # 优先级3 (低优先级)
```

## 📊 完整软中断映射

### 更新后的引脚分配：
```
PB1/EXTI1 → 混音器计算 (PRIORITY_MEDIUM) - 优先级1
PB2/EXTI2 → 音效处理 (PRIORITY_SOUND) - 优先级3  ✅ 新增
PB3/EXTI3 → 后台任务 (PRIORITY_LOW) - 优先级3
PB4/EXTI4 → UART处理 (自定义) - 优先级2
```

### 中断处理函数映射：
```
EXTI0_1_IRQHandler → 处理PB1 (混音器)
EXTI2_3_IRQHandler → 处理PB2 (音效) + PB3 (后台任务)
EXTI4_15_IRQHandler → 处理PB4 (UART)
```

## 💻 代码实现

### 1. 音效中断触发函数
```c
// 需要在clock_system.c中添加
void CLOCK_RunSound(func_callback_t cb)
{
    if (cb) {
        func_callback = cb;
        HAL_NVIC_SetPendingIRQ(EXTI2_3_IRQn);  // 触发PB2
    }
}
```

### 2. 更新EXTI2_3中断处理
```c
void EXTI2_3_IRQHandler(void)
{
    /* EXTI2: 音效处理 (PB2) */
    if (__HAL_GPIO_EXTI_GET_FLAG(GPIO_PIN_2)) {
        __HAL_GPIO_EXTI_CLEAR_FLAG(GPIO_PIN_2);
        
        /* 音效回调处理 */
        if (CLOCK_IsReady(PRIORITY_SOUND)) {
            CLOCK_ClearReady(PRIORITY_SOUND);
            
            /* 执行音效回调 */
            uint32_t next_interval = SOUND_Callback();
            if (next_interval > 0) {
                CLOCK_SetCallback(PRIORITY_SOUND, next_interval);
            }
        }
    }
    
    /* EXTI3: 后台任务 (PB3) */
    if (__HAL_GPIO_EXTI_GET_FLAG(GPIO_PIN_3)) {
        __HAL_GPIO_EXTI_CLEAR_FLAG(GPIO_PIN_3);
        
        if (func_callback) {
            func_callback();
            func_callback = NULL;
        }
    }
}
```

### 3. 音效系统集成
```c
// 在sound.c中使用
void SOUND_ProcessCallback(void)
{
    if (sound_callback) {
        uint32_t next_ms = sound_callback();
        if (next_ms > 0) {
            CLOCK_SetCallback(PRIORITY_SOUND, next_ms);
        } else {
            SOUND_Stop();
        }
    }
}
```

## 🎵 音效处理流程

### 1. 音效启动
```
SOUND_Start() → CLOCK_SetCallback(PRIORITY_SOUND, msec) → 设置定时器
```

### 2. 定时器到期
```
SysTick_Handler() → priority_ready |= (1 << PRIORITY_SOUND)
```

### 3. 主循环检测
```
CLOCK_ProcessEvents() → CLOCK_IsReady(PRIORITY_SOUND) → 触发软中断
```

### 4. 软中断处理
```
HAL_NVIC_SetPendingIRQ(EXTI2_3_IRQn) → EXTI2_3_IRQHandler() → SOUND_Callback()
```

### 5. 音效回调
```
SOUND_Callback() → sound_callback() → 返回下次间隔 → 重复循环
```

## 🔧 使用示例

### 播放音效序列
```c
void PlayStartupSound(void)
{
    SOUND_Start(SOUND_DURATION_MID, SOUND_StartupSequence, 1);
    // 自动通过PRIORITY_SOUND处理音效回调
}
```

### 震动控制
```c
void DoublePulseVibration(void)
{
    VIBRATOR_DoublePulse();
    // 内部使用CLOCK_SetCallback(PRIORITY_SOUND, 150)
}
```

## ✅ 配置验证

### IOC配置检查：
- ✅ **PB2配置**: GPXTI2, 上拉, 下降沿触发
- ✅ **信号映射**: SH.GPXTI2.0=GPIO_EXTI2
- ✅ **NVIC中断**: EXTI2_3_IRQn优先级3

### 代码集成检查：
- ✅ **优先级定义**: PRIORITY_SOUND已定义
- ✅ **回调设置**: CLOCK_SetCallback使用PRIORITY_SOUND
- ✅ **主循环处理**: CLOCK_ProcessEvents检查PRIORITY_SOUND
- ✅ **中断处理**: 需要更新EXTI2_3_IRQHandler

## 🚨 注意事项

1. **共享中断线**: EXTI2_3_IRQHandler处理两个引脚 (PB2音效 + PB3后台)
2. **优先级相同**: PB2和PB3都使用优先级3，需要在中断内部区分
3. **回调管理**: 音效回调和后台任务回调需要分别管理
4. **时序要求**: 音效处理对时序敏感，需要及时响应

---
*配置完成时间: 2025-07-30*
*新增引脚: PB2 (EXTI_SOUND)*
*用途: PRIORITY_SOUND音效处理*
*状态: ✅ 已配置*
