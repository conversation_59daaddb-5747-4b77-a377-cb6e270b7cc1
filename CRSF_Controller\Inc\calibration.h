/**
 * @file calibration.h
 * @brief 校准功能接口
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __CALIBRATION_H
#define __CALIBRATION_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"
#include "adc_input.h"

/* 校准状态定义 */
typedef enum {
    CALIB_STATE_IDLE = 0,
    CALIB_STATE_CENTER,         // 校准中心位置
    CALIB_STATE_MIN_MAX,        // 校准最小最大值
    CALIB_STATE_SWITCHES,       // 校准开关
    CALIB_STATE_COMPLETE,       // 校准完成
    CALIB_STATE_ERROR           // 校准错误
} calibration_state_t;

/* 校准步骤定义 */
typedef enum {
    CALIB_STEP_INIT = 0,
    CALIB_STEP_CENTER_STICKS,   // 摇杆居中
    CALIB_STEP_MOVE_STICKS,     // 摇杆全行程
    CALIB_STEP_CENTER_POTS,     // 电位器居中
    CALIB_STEP_MOVE_POTS,       // 电位器全行程
    CALIB_STEP_SWITCH_LOW,      // 开关下位
    CALIB_STEP_SWITCH_MID,      // 开关中位
    CALIB_STEP_SWITCH_HIGH,     // 开关上位
    CALIB_STEP_SAVE,            // 保存校准
    CALIB_STEP_DONE             // 完成
} calibration_step_t;

/* 校准上下文 */
typedef struct {
    calibration_state_t state;
    calibration_step_t step;
    adc_channel_t current_channel;
    uint32_t step_start_time;
    uint32_t sample_count;
    uint32_t sample_sum;
    bool auto_mode;             // 自动校准模式
    bool need_save;             // 需要保存
} calibration_context_t;

/* 全局变量声明 */
extern calibration_context_t calib_context;

/* 函数声明 */

/* 开机校准 */
bool Calibration_IsNeeded(void);
error_code_t Calibration_StartBoot(void);
void Calibration_ProcessBoot(void);
bool Calibration_IsBootComplete(void);

/* 菜单校准 */
error_code_t Calibration_StartMenu(void);
void Calibration_ProcessMenu(void);
bool Calibration_IsMenuComplete(void);
void Calibration_Cancel(void);

/* 校准步骤处理 */
void Calibration_NextStep(void);
void Calibration_PrevStep(void);
const char* Calibration_GetStepText(void);
const char* Calibration_GetInstructionText(void);

/* 校准数据处理 */
void Calibration_SampleChannel(adc_channel_t channel);
void Calibration_ApplyCalibration(adc_channel_t channel);
void Calibration_SaveData(void);
void Calibration_LoadData(void);
void Calibration_ResetData(void);

/* 状态查询 */
calibration_state_t Calibration_GetState(void);
calibration_step_t Calibration_GetStep(void);
uint8_t Calibration_GetProgress(void);
bool Calibration_IsActive(void);

/* 显示相关 */
void Calibration_ShowBootScreen(void);
void Calibration_ShowMenuScreen(void);
void Calibration_UpdateDisplay(void);

/* 工具函数 */
bool Calibration_IsStickChannel(adc_channel_t channel);
bool Calibration_IsSwitchChannel(adc_channel_t channel);
bool Calibration_IsPotentiometerChannel(adc_channel_t channel);
bool Calibration_IsStickCentered(void);
bool Calibration_IsStickMoved(void);

/* 常量定义 */
#define CALIB_SAMPLE_TIME_MS        1000    // 采样时间
#define CALIB_SAMPLE_COUNT_MIN      50      // 最小采样数
#define CALIB_CENTER_TOLERANCE      50      // 中心位置容差
#define CALIB_MOVEMENT_THRESHOLD    200     // 移动阈值
#define CALIB_SWITCH_TOLERANCE      100     // 开关位置容差

/* 校准提示文本 */
#define CALIB_TEXT_INIT             "校准初始化"
#define CALIB_TEXT_CENTER_STICKS    "摇杆居中，按确定"
#define CALIB_TEXT_MOVE_STICKS      "摇杆全行程运动"
#define CALIB_TEXT_SWITCH_LOW       "开关拨到下位"
#define CALIB_TEXT_SWITCH_MID       "开关拨到中位"
#define CALIB_TEXT_SWITCH_HIGH      "开关拨到上位"
#define CALIB_TEXT_SAVE             "保存校准数据"
#define CALIB_TEXT_DONE             "校准完成"

#ifdef __cplusplus
}
#endif

#endif /* __CALIBRATION_H */
