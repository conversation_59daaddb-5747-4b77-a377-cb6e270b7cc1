/**
 * @file eeprom.h
 * @brief EEPROM存储驱动接口 (FT24C128A)
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __EEPROM_H
#define __EEPROM_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"

/* EEPROM配置参数 */
#define EEPROM_I2C_ADDR         (FT24C128A_WRITE_ADDR >> 1)  // 7位地址
#define EEPROM_WRITE_CYCLE_TIME 5                             // 写周期时间 (ms)
#define EEPROM_MAX_RETRY        3                             // 最大重试次数

/* 存储区域定义 */
typedef enum {
    EEPROM_AREA_SYSTEM = 0,     // 系统配置区 (0-1023)
    EEPROM_AREA_CALIBRATION,    // 校准数据区 (1024-2047)
    EEPROM_AREA_MODELS,         // 模型数据区 (2048-8191)
    EEPROM_AREA_LOGS,           // 日志数据区 (8192-12287)
    EEPROM_AREA_USER,           // 用户数据区 (12288-16383)
    EEPROM_AREA_COUNT
} eeprom_area_t;

/* 存储区域信息 */
typedef struct {
    uint16_t start_addr;        // 起始地址
    uint16_t size;              // 区域大小
    const char* name;           // 区域名称
} eeprom_area_info_t;

/* 系统配置结构 */
typedef struct {
    uint32_t magic;             // 魔数标识
    uint16_t version;           // 配置版本
    uint16_t checksum;          // 校验和
    
    /* 显示设置 */
    uint8_t backlight;          // 背光亮度 (0-100)
    uint8_t contrast;           // 对比度 (0-100)
    uint16_t auto_off_time;     // 自动关机时间 (分钟)
    
    /* 音效设置 */
    bool buzzer_enabled;        // 蜂鸣器使能
    bool vibrator_enabled;      // 震动使能
    uint8_t buzzer_volume;      // 蜂鸣器音量 (0-100)
    uint8_t vibrator_intensity; // 震动强度 (0-100)
    
    /* CRSF设置 */
    uint32_t crsf_baudrate;     // CRSF波特率
    uint8_t crsf_update_rate;   // CRSF更新频率
    uint8_t model_id;           // 模型ID
    
    /* 其他设置 */
    uint8_t language;           // 语言设置
    uint8_t reserved[32];       // 保留字节
} system_config_t;

/* 校准数据结构 */
typedef struct {
    uint32_t magic;             // 魔数标识
    uint16_t version;           // 校准版本
    uint16_t checksum;          // 校验和
    
    /* ADC校准数据 */
    struct {
        uint16_t min_value;     // 最小值
        uint16_t center_value;  // 中心值
        uint16_t max_value;     // 最大值
        bool reversed;          // 是否反向
        uint16_t deadband;      // 死区
    } adc_calibration[ADC_CHANNELS];
    
    uint8_t reserved[64];       // 保留字节
} calibration_data_t;

/* 函数声明 */

/* 初始化和基础操作 */
error_code_t EEPROM_Init(void);
error_code_t EEPROM_Test(void);
bool EEPROM_IsReady(void);

/* 底层读写操作 */
error_code_t EEPROM_ReadByte(uint16_t addr, uint8_t* data);
error_code_t EEPROM_WriteByte(uint16_t addr, uint8_t data);
error_code_t EEPROM_ReadBytes(uint16_t addr, uint8_t* data, uint16_t length);
error_code_t EEPROM_WriteBytes(uint16_t addr, const uint8_t* data, uint16_t length);

/* 页操作 */
error_code_t EEPROM_ReadPage(uint16_t page, uint8_t* data);
error_code_t EEPROM_WritePage(uint16_t page, const uint8_t* data);
error_code_t EEPROM_ErasePage(uint16_t page);

/* 区域操作 */
error_code_t EEPROM_ReadArea(eeprom_area_t area, uint16_t offset, uint8_t* data, uint16_t length);
error_code_t EEPROM_WriteArea(eeprom_area_t area, uint16_t offset, const uint8_t* data, uint16_t length);
error_code_t EEPROM_EraseArea(eeprom_area_t area);

/* 高级操作 */
error_code_t EEPROM_Format(void);
error_code_t EEPROM_Backup(uint8_t* backup_buffer, uint16_t buffer_size);
error_code_t EEPROM_Restore(const uint8_t* backup_buffer, uint16_t buffer_size);

/* 系统配置管理 */
error_code_t EEPROM_LoadSystemConfig(system_config_t* config);
error_code_t EEPROM_SaveSystemConfig(const system_config_t* config);
error_code_t EEPROM_ResetSystemConfig(void);

/* 校准数据管理 */
error_code_t EEPROM_LoadCalibrationData(calibration_data_t* data);
error_code_t EEPROM_SaveCalibrationData(const calibration_data_t* data);
error_code_t EEPROM_ResetCalibrationData(void);

/* 工具函数 */
uint16_t EEPROM_CalculateChecksum(const uint8_t* data, uint16_t length);
bool EEPROM_VerifyChecksum(const uint8_t* data, uint16_t length, uint16_t expected_checksum);
const eeprom_area_info_t* EEPROM_GetAreaInfo(eeprom_area_t area);
uint16_t EEPROM_GetAreaStartAddr(eeprom_area_t area);
uint16_t EEPROM_GetAreaSize(eeprom_area_t area);

/* 状态查询 */
uint16_t EEPROM_GetTotalSize(void);
uint16_t EEPROM_GetPageSize(void);
uint16_t EEPROM_GetPageCount(void);
uint16_t EEPROM_GetFreeSpace(eeprom_area_t area);

/* 调试功能 */
#if DEBUG_ENABLED
void EEPROM_PrintInfo(void);
void EEPROM_PrintAreaInfo(eeprom_area_t area);
void EEPROM_DumpArea(eeprom_area_t area, uint16_t offset, uint16_t length);
error_code_t EEPROM_TestPattern(void);
#endif

/* 常量定义 */
#define EEPROM_MAGIC_SYSTEM     0x53595354UL    // "SYST"
#define EEPROM_MAGIC_CALIB      0x43414C49UL    // "CALI"
#define EEPROM_VERSION_SYSTEM   0x0100          // v1.0
#define EEPROM_VERSION_CALIB    0x0100          // v1.0

/* 默认配置值 */
#define DEFAULT_BACKLIGHT       80
#define DEFAULT_CONTRAST        50
#define DEFAULT_AUTO_OFF_TIME   10
#define DEFAULT_BUZZER_VOLUME   50
#define DEFAULT_VIBRATOR_INTENSITY 60
#define DEFAULT_CRSF_BAUDRATE   400000
#define DEFAULT_CRSF_UPDATE_RATE 100
#define DEFAULT_MODEL_ID        1

#ifdef __cplusplus
}
#endif

#endif /* __EEPROM_H */
