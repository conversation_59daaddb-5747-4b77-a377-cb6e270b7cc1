/**
 * @file oled_display.c
 * @brief OLED显示驱动实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "oled_display.h"
#include "hal_drivers.h"
#include <stdarg.h>

/* 显示缓冲区 */
uint8_t oled_buffer[OLED_PAGES][OLED_COLUMNS];

/* 私有变量 */
static oled_font_size_t current_font = FONT_SIZE_6x8;
static uint8_t cursor_x = 0;
static uint8_t cursor_y = 0;
static bool oled_initialized = false;

/* 字体宽度和高度表 */
static const uint8_t font_width[] = {6, 8, 12, 16};
static const uint8_t font_height[] = {8, 16, 24, 32};

/* 私有函数声明 */
static error_code_t OLED_WriteCommand(uint8_t cmd);
static error_code_t OLED_WriteData(uint8_t data);
static error_code_t OLED_WriteBuffer(uint8_t* buffer, uint16_t length);
static void OLED_SetPosition(uint8_t page, uint8_t column);
static uint8_t OLED_GetFontByte(char ch, uint8_t row);

/**
 * @brief OLED初始化
 */
error_code_t OLED_Init(void)
{
    if (oled_initialized) {
        return ERR_OK;
    }

    /* 等待OLED稳定 */
    delay_ms(100);

    /* 初始化序列 */
    OLED_WriteCommand(OLED_CMD_DISPLAY_OFF);
    OLED_WriteCommand(0x20); // Set Memory Addressing Mode
    OLED_WriteCommand(0x10); // 00,Horizontal Addressing Mode;01,Vertical Addressing Mode;10,Page Addressing Mode (RESET);11,Invalid
    OLED_WriteCommand(0xB0); // Set Page Start Address for Page Addressing Mode,0-7
    OLED_WriteCommand(0xC8); // Set COM Output Scan Direction
    OLED_WriteCommand(0x00); // Set low column address
    OLED_WriteCommand(0x10); // Set high column address
    OLED_WriteCommand(0x40); // Set start line address
    OLED_WriteCommand(0x81); // Set contrast control register
    OLED_WriteCommand(0xFF); // Set contrast value
    OLED_WriteCommand(0xA1); // Set segment re-map 0 to 127
    OLED_WriteCommand(0xA6); // Set normal display
    OLED_WriteCommand(0xA8); // Set multiplex ratio(1 to 64)
    OLED_WriteCommand(0x3F); // 1/64 duty
    OLED_WriteCommand(0xA4); // 0xa4,Output follows RAM content;0xa5,Output ignores RAM content
    OLED_WriteCommand(0xD3); // Set display offset
    OLED_WriteCommand(0x00); // No offset
    OLED_WriteCommand(0xD5); // Set display clock divide ratio/oscillator frequency
    OLED_WriteCommand(0xF0); // Set divide ratio
    OLED_WriteCommand(0xD9); // Set pre-charge period
    OLED_WriteCommand(0x22); // Set pre-charge period
    OLED_WriteCommand(0xDA); // Set com pins hardware configuration
    OLED_WriteCommand(0x12);
    OLED_WriteCommand(0xDB); // Set vcomh
    OLED_WriteCommand(0x20); // 0x20,0.77xVcc
    OLED_WriteCommand(0x8D); // Set DC-DC enable
    OLED_WriteCommand(0x14); // Enable charge pump
    OLED_WriteCommand(OLED_CMD_DISPLAY_ON);

    /* 清空显示缓冲区 */
    OLED_Clear();
    OLED_Update();

    /* 设置默认字体和光标位置 */
    OLED_SetFont(FONT_SIZE_6x8);
    OLED_SetCursor(0, 0);

    oled_initialized = true;
    return ERR_OK;
}

/**
 * @brief 显示开启
 */
error_code_t OLED_DisplayOn(void)
{
    return OLED_WriteCommand(OLED_CMD_DISPLAY_ON);
}

/**
 * @brief 显示关闭
 */
error_code_t OLED_DisplayOff(void)
{
    return OLED_WriteCommand(OLED_CMD_DISPLAY_OFF);
}

/**
 * @brief 设置对比度
 */
error_code_t OLED_SetContrast(uint8_t contrast)
{
    error_code_t result = OLED_WriteCommand(OLED_CMD_SET_CONTRAST);
    if (result != ERR_OK) {
        return result;
    }
    return OLED_WriteCommand(contrast);
}

/**
 * @brief 清空显示缓冲区
 */
void OLED_Clear(void)
{
    memset(oled_buffer, 0, sizeof(oled_buffer));
}

/**
 * @brief 更新显示
 */
void OLED_Update(void)
{
    for (uint8_t page = 0; page < OLED_PAGES; page++) {
        OLED_SetPosition(page, 0);
        OLED_WriteBuffer(oled_buffer[page], OLED_COLUMNS);
    }
}

/**
 * @brief 设置像素
 */
void OLED_SetPixel(uint8_t x, uint8_t y, bool on)
{
    if (x >= OLED_WIDTH || y >= OLED_HEIGHT) {
        return;
    }

    uint8_t page = y / 8;
    uint8_t bit = y % 8;

    if (on) {
        oled_buffer[page][x] |= (1 << bit);
    } else {
        oled_buffer[page][x] &= ~(1 << bit);
    }
}

/**
 * @brief 获取像素状态
 */
bool OLED_GetPixel(uint8_t x, uint8_t y)
{
    if (x >= OLED_WIDTH || y >= OLED_HEIGHT) {
        return false;
    }

    uint8_t page = y / 8;
    uint8_t bit = y % 8;

    return (oled_buffer[page][x] & (1 << bit)) != 0;
}

/**
 * @brief 画水平线
 */
void OLED_DrawHLine(uint8_t x, uint8_t y, uint8_t width, bool on)
{
    for (uint8_t i = 0; i < width; i++) {
        OLED_SetPixel(x + i, y, on);
    }
}

/**
 * @brief 画垂直线
 */
void OLED_DrawVLine(uint8_t x, uint8_t y, uint8_t height, bool on)
{
    for (uint8_t i = 0; i < height; i++) {
        OLED_SetPixel(x, y + i, on);
    }
}

/**
 * @brief 画矩形
 */
void OLED_DrawRect(uint8_t x, uint8_t y, uint8_t width, uint8_t height, bool on)
{
    OLED_DrawHLine(x, y, width, on);
    OLED_DrawHLine(x, y + height - 1, width, on);
    OLED_DrawVLine(x, y, height, on);
    OLED_DrawVLine(x + width - 1, y, height, on);
}

/**
 * @brief 填充矩形
 */
void OLED_FillRect(uint8_t x, uint8_t y, uint8_t width, uint8_t height, bool on)
{
    for (uint8_t i = 0; i < height; i++) {
        OLED_DrawHLine(x, y + i, width, on);
    }
}

/**
 * @brief 设置字体
 */
void OLED_SetFont(oled_font_size_t font)
{
    if (font < sizeof(font_width) / sizeof(font_width[0])) {
        current_font = font;
    }
}

/**
 * @brief 设置光标位置
 */
void OLED_SetCursor(uint8_t x, uint8_t y)
{
    cursor_x = x;
    cursor_y = y;
}

/**
 * @brief 写字符
 */
void OLED_WriteChar(char ch)
{
    uint8_t font_w = font_width[current_font];
    uint8_t font_h = font_height[current_font];

    /* 检查边界 */
    if (cursor_x + font_w > OLED_WIDTH) {
        cursor_x = 0;
        cursor_y += font_h;
    }
    if (cursor_y + font_h > OLED_HEIGHT) {
        return;
    }

    /* 绘制字符 */
    for (uint8_t row = 0; row < font_h; row++) {
        uint8_t font_byte = OLED_GetFontByte(ch, row);
        for (uint8_t col = 0; col < font_w; col++) {
            bool pixel_on = (font_byte & (1 << (font_w - 1 - col))) != 0;
            OLED_SetPixel(cursor_x + col, cursor_y + row, pixel_on);
        }
    }

    cursor_x += font_w;
}

/**
 * @brief 写字符串
 */
void OLED_WriteString(const char* str)
{
    if (str == NULL) {
        return;
    }

    while (*str) {
        OLED_WriteChar(*str++);
    }
}

/**
 * @brief 格式化输出
 */
void OLED_Printf(const char* format, ...)
{
    char buffer[128];
    va_list args;
    
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    OLED_WriteString(buffer);
}

/**
 * @brief 居中显示字符串
 */
void OLED_WriteStringAligned(uint8_t y, const char* str, oled_align_t align)
{
    if (str == NULL) {
        return;
    }

    uint8_t str_width = OLED_GetStringWidth(str);
    uint8_t x;

    switch (align) {
        case OLED_ALIGN_LEFT:
            x = 0;
            break;
        case OLED_ALIGN_CENTER:
            x = (OLED_WIDTH - str_width) / 2;
            break;
        case OLED_ALIGN_RIGHT:
            x = OLED_WIDTH - str_width;
            break;
        default:
            x = 0;
            break;
    }

    OLED_SetCursor(x, y);
    OLED_WriteString(str);
}

/**
 * @brief 获取字符串宽度
 */
uint8_t OLED_GetStringWidth(const char* str)
{
    if (str == NULL) {
        return 0;
    }

    return strlen(str) * font_width[current_font];
}

/**
 * @brief 获取字体高度
 */
uint8_t OLED_GetFontHeight(void)
{
    return font_height[current_font];
}

/**
 * @brief 显示启动画面
 */
void OLED_ShowSplashScreen(const char* title, const char* subtitle)
{
    OLED_Clear();
    
    /* 显示标题 */
    OLED_SetFont(FONT_SIZE_8x16);
    OLED_WriteStringAligned(16, title, OLED_ALIGN_CENTER);
    
    /* 显示副标题 */
    OLED_SetFont(FONT_SIZE_6x8);
    OLED_WriteStringAligned(40, subtitle, OLED_ALIGN_CENTER);
    
    OLED_Update();
}

/**
 * @brief 写命令
 */
static error_code_t OLED_WriteCommand(uint8_t cmd)
{
    uint8_t data[2] = {0x00, cmd}; // 0x00 for command
    return HAL_I2C_Transmit_Safe(&hi2c1, OLED_I2C_ADDRESS << 1, data, 2, 100);
}

/**
 * @brief 写数据
 */
static error_code_t OLED_WriteData(uint8_t data)
{
    uint8_t buffer[2] = {0x40, data}; // 0x40 for data
    return HAL_I2C_Transmit_Safe(&hi2c1, OLED_I2C_ADDRESS << 1, buffer, 2, 100);
}

/**
 * @brief 写缓冲区
 */
static error_code_t OLED_WriteBuffer(uint8_t* buffer, uint16_t length)
{
    uint8_t temp_buffer[129]; // 1 byte for control + 128 bytes data
    temp_buffer[0] = 0x40; // Data mode
    memcpy(&temp_buffer[1], buffer, length);
    
    return HAL_I2C_Transmit_Safe(&hi2c1, OLED_I2C_ADDRESS << 1, temp_buffer, length + 1, 100);
}

/**
 * @brief 设置显示位置
 */
static void OLED_SetPosition(uint8_t page, uint8_t column)
{
    OLED_WriteCommand(OLED_CMD_SET_PAGE | page);
    OLED_WriteCommand(OLED_CMD_SET_COLUMN_LOW | (column & 0x0F));
    OLED_WriteCommand(OLED_CMD_SET_COLUMN_HIGH | ((column >> 4) & 0x0F));
}

/**
 * @brief 获取字体字节
 */
static uint8_t OLED_GetFontByte(char ch, uint8_t row)
{
    /* 简化的字体实现，实际应用中需要包含完整的字体数据 */
    if (ch < 32 || ch > 126) {
        ch = ' '; // 不可打印字符显示为空格
    }
    
    /* 这里应该根据current_font和字符ch返回对应的字体数据 */
    /* 为了简化，这里返回一个简单的模式 */
    return 0x00; // 实际实现需要字体数据表
}
