# CRSF Controller 快速参考卡

## 🎮 **硬件接口速查**

### 输入接口
| 接口 | 引脚 | 功能 | 范围 |
|------|------|------|------|
| **CH1** | PA0 | 右摇杆X (副翼) | 0-4095 |
| **CH2** | PA1 | 右摇杆Y (升降) | 0-4095 |
| **CH3** | PA2 | 左摇杆Y (油门) | 0-4095 |
| **CH4** | PA3 | 左摇杆X (方向) | 0-4095 |
| **SWA** | PA4 | 三段开关A | 3档位 |
| **SWB** | PA5 | 三段开关B | 3档位 |
| **VRA** | PA6 | 电位器A | 0-4095 |
| **VRB** | PA7 | 电位器B | 0-4095 |

### 按键定义
| 按键 | 引脚 | 功能 | 操作 |
|------|------|------|------|
| **KEY1** | PD0 | 菜单键 | 进入/退出菜单 |
| **KEY2** | PD1 | 上键 | 向上/增加 |
| **KEY3** | PD2 | 下键 | 向下/减少 |
| **KEY4** | PD3 | 左键 | 返回/取消 |
| **KEY5** | PD4 | 确认键 | 确认/进入 |

### 输出接口
| 接口 | 引脚 | 功能 | 协议 |
|------|------|------|------|
| **CRSF TX** | PA9 | 数据发送 | UART 420K |
| **CRSF RX** | PA10 | 遥测接收 | UART 420K |
| **USB D+** | PA12 | USB数据+ | USB 2.0 |
| **USB D-** | PA11 | USB数据- | USB 2.0 |
| **I2C SCL** | PB10 | OLED时钟 | I2C 100K |
| **I2C SDA** | PB11 | OLED数据 | I2C 100K |

## ⚡ **系统参数速查**

### 性能指标
| 参数 | 数值 | 说明 |
|------|------|------|
| **CRSF周期** | 4ms ±10μs | 250Hz更新率 |
| **ADC精度** | 12位 (4096级) | 高精度输入 |
| **CPU频率** | 48MHz | STM32F072 |
| **Flash容量** | 128KB | 程序存储 |
| **RAM容量** | 16KB | 运行内存 |
| **通道数** | 16通道 | CRSF标准 |

### 中断优先级
| 中断 | 优先级 | 功能 |
|------|--------|------|
| **TIM2** | 0 (最高) | CRSF定时器 |
| **SysTick** | 0 (最高) | 系统时钟 |
| **EXTI1** | 1 (高) | 混音器计算 |
| **EXTI4** | 2 (中) | UART处理 |
| **EXTI3** | 3 (低) | 后台任务 |

## 🔧 **操作速查**

### 开机模式
| 操作 | 结果 |
|------|------|
| **正常开机** | 按电源键2秒 |
| **校准模式** | 开机时按KEY1+KEY2 |
| **升级模式** | 开机时按KEY1+KEY2+KEY3 |
| **自检模式** | 开机时按KEY4+KEY5 |

### 菜单导航
| 按键 | 功能 |
|------|------|
| **KEY1** | 进入菜单 / 返回主界面 |
| **KEY2** | 向上选择 / 数值增加 |
| **KEY3** | 向下选择 / 数值减少 |
| **KEY4** | 返回上级 / 取消操作 |
| **KEY5** | 确认选择 / 进入子菜单 |
| **KEY1长按** | 快速设置模式 |

### 快捷操作
| 组合键 | 功能 |
|--------|------|
| **KEY2+KEY3** | 快速校准 |
| **KEY4+KEY5** | 系统信息 |
| **KEY1+KEY5** | 模型切换 |
| **KEY2+KEY4** | 音效开关 |

## 📊 **CRSF协议速查**

### 数据包格式
```
[SYNC][LEN][TYPE][DATA...][CRC]
 0xC8   24   0x16  22字节   1字节
```

### 通道映射
| 通道 | 功能 | 范围 | 中心值 |
|------|------|------|--------|
| **CH1** | 副翼 (Aileron) | 172-1811 | 992 |
| **CH2** | 升降 (Elevator) | 172-1811 | 992 |
| **CH3** | 油门 (Throttle) | 172-1811 | 172 |
| **CH4** | 方向 (Rudder) | 172-1811 | 992 |
| **CH5-16** | 辅助通道 | 172-1811 | 992 |

### 遥测类型
| 类型 | 代码 | 内容 |
|------|------|------|
| **GPS** | 0x02 | 经纬度、高度、速度 |
| **电池** | 0x08 | 电压、电流、容量 |
| **姿态** | 0x1E | 俯仰、横滚、偏航 |
| **链路** | 0x14 | RSSI、LQ、功率 |

## 🎛️ **混音器速查**

### 基本混音模式
```c
// 飞机模式 (4通道)
CH1 = 副翼 * 100%
CH2 = 升降 * 100%
CH3 = 油门 * 100%
CH4 = 方向 * 100%

// 直升机模式 (6通道)
CH1 = 副翼 * 100%
CH2 = 升降 * 100%
CH3 = 油门 * 100%
CH4 = 方向 * 100%
CH5 = 陀螺仪增益
CH6 = 飞行模式

// 多旋翼模式 (8通道)
CH1 = Roll * 100%
CH2 = Pitch * 100%
CH3 = Throttle * 100%
CH4 = Yaw * 100%
CH5 = 飞行模式
CH6 = 解锁开关
CH7 = 辅助1
CH8 = 辅助2
```

### 高级混音
```c
// V尾混音
CH2_LEFT = 升降 + 方向 * 50%
CH2_RIGHT = 升降 - 方向 * 50%

// 襟副翼混音
CH1_LEFT = 副翼 + 襟翼 * 30%
CH1_RIGHT = -副翼 + 襟翼 * 30%

// 差动混音
CH_DIFF = 输入1 - 输入2
```

## 🔊 **音效代码速查**

### 系统音效
```c
SOUND_PlayStartup();     // 开机音效 (C-E-G)
SOUND_PlayBeep();        // 按键音效 (1000Hz)
SOUND_PlaySuccess();     // 成功音效 + 震动
SOUND_PlayWarning();     // 警告音效 + 震动
SOUND_PlayError();       // 错误音效 + 震动
SOUND_PlayShutdown();    // 关机音效 (G-E-C)
```

### 震动模式
```c
VIBRATOR_Pulse(100);     // 单次震动100ms
VIBRATOR_DoublePulse();  // 双震动模式
VIBRATOR_TriplePulse();  // 三震动模式
```

## 🔍 **故障排除速查**

### 常见问题
| 问题 | 可能原因 | 解决方法 |
|------|----------|----------|
| **无CRSF输出** | UART配置错误 | 检查波特率420K |
| **摇杆漂移** | 需要校准 | 进行摇杆校准 |
| **按键无响应** | GPIO配置错误 | 检查上拉电阻 |
| **显示异常** | I2C通信问题 | 检查I2C地址0x3C |
| **USB无法识别** | 时钟配置错误 | 检查HSI48时钟 |

### 错误代码
| 代码 | 含义 | 处理 |
|------|------|------|
| **E001** | ADC初始化失败 | 检查ADC配置 |
| **E002** | UART初始化失败 | 检查UART配置 |
| **E003** | I2C初始化失败 | 检查I2C配置 |
| **E004** | 校准数据无效 | 重新校准 |
| **E005** | EEPROM读写失败 | 检查I2C连接 |

### 调试命令
```c
// USB CDC调试命令
"status"     // 显示系统状态
"channels"   // 显示通道值
"adc"        // 显示ADC原始值
"calibrate"  // 启动校准
"reset"      // 软件复位
"upgrade"    // 进入升级模式
```

## 🛠️ **STM32CubeMX配置速查**

### 时钟配置
| 时钟源 | 频率 | 配置 |
|--------|------|------|
| **HSE** | 8MHz | Crystal/Ceramic Resonator |
| **PLL** | 48MHz | HSE × 6 |
| **SYSCLK** | 48MHz | PLLCLK |
| **HCLK** | 48MHz | SYSCLK |
| **APB1** | 48MHz | HCLK |
| **APB2** | 48MHz | HCLK |
| **USB** | 48MHz | HSI48 |
| **ADC** | 12MHz | PCLK/4 |

### 外设引脚配置
| 外设 | 引脚 | 功能 | 配置 |
|------|------|------|------|
| **ADC** | PA0-PA7,PB0-PB1 | 10通道输入 | 12位,连续转换 |
| **USART1** | PA9(TX),PA10(RX) | CRSF通信 | 420000bps |
| **I2C2** | PB10(SCL),PB11(SDA) | OLED显示 | 100kHz |
| **USB** | PA11(DM),PA12(DP) | 虚拟串口 | Full Speed |
| **TIM1** | PA8 | 蜂鸣器PWM | 1kHz |
| **TIM3** | PE3 | 震动PWM | 1kHz |

### DMA通道分配
| 通道 | 外设 | 方向 | 模式 | 优先级 |
|------|------|------|------|--------|
| **DMA1_CH1** | ADC | P2M | Circular | High |
| **DMA1_CH2** | USART1_TX | M2P | Normal | Medium |
| **DMA1_CH3** | USART1_RX | P2M | Circular | Medium |
| **DMA1_CH4** | I2C2_TX | M2P | Normal | Low |
| **DMA1_CH5** | I2C2_RX | P2M | Normal | Low |

### 中断优先级
| 中断 | 优先级 | 子优先级 | 功能 |
|------|--------|----------|------|
| **TIM2** | 0 | 0 | CRSF定时器 |
| **SysTick** | 0 | 0 | 系统时钟 |
| **EXTI1** | 1 | 0 | 混音器计算 |
| **EXTI4-15** | 2 | 0 | UART处理 |
| **EXTI3** | 3 | 0 | 后台任务 |
| **USART1** | 4 | 0 | UART中断 |
| **DMA1_CH1** | 5 | 0 | ADC DMA |

### GPIO配置
| 引脚组 | 引脚 | 模式 | 上拉 | 功能 |
|--------|------|------|------|------|
| **按键** | PD0-PD4 | Input | Pull-up | 5个按键 |
| **LED** | PE4-PE7 | Output PP | No pull | 4个LED |
| **EXTI** | PB1,PB3,PB4 | EXTI | No pull | 软件中断 |

## 📋 **配置参数速查**

### 系统配置
```c
#define SYSTEM_CLOCK_FREQ    48000000   // 系统时钟
#define ADC_SAMPLE_RATE      1000       // ADC采样率
#define CRSF_FRAME_PERIOD    4000       // CRSF周期(μs)
#define MIXER_RUNTIME        500        // 混音器运行时间(μs)
```

### 硬件配置
```c
#define OLED_I2C_ADDRESS     0x3C       // OLED地址
#define EEPROM_I2C_ADDRESS   0x50       // EEPROM地址
#define UART_BAUDRATE        420000     // CRSF波特率
#define I2C_CLOCK_SPEED      100000     // I2C时钟
```

### 校准参数
```c
#define STICK_CENTER_DEADZONE  20       // 摇杆死区
#define STICK_MIN_TRAVEL      100       // 最小行程
#define STICK_MAX_TRAVEL      3900      // 最大行程
#define SWITCH_THRESHOLD_LOW   1000     // 开关下限
#define SWITCH_THRESHOLD_HIGH  3000     // 开关上限
```

## 🎯 **性能监控速查**

### 关键指标
```c
// 时序监控
CRSF周期: 4000μs ±50μs
混音器时间: <500μs
ADC转换时间: <100μs

// 资源使用
CPU使用率: <30%
RAM使用: <12KB
Flash使用: <64KB

// 通信状态
UART错误率: <0.1%
I2C错误率: <0.01%
USB连接状态: 正常
```

这份快速参考卡提供了CRSF Controller的所有关键信息，方便开发和使用时快速查阅！
