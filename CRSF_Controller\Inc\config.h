/**
 * @file config.h
 * @brief 系统配置文件
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __CONFIG_H
#define __CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32f0xx_hal.h"
#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include <stdio.h>

/* 系统配置 */
#define SYSTEM_CLOCK_FREQ       48000000    // 系统时钟频率 48MHz
#define DEBUG_ENABLED           1           // 调试输出开关

/* UART配置 */
#define CRSF_UART_BAUDRATE      400000      // CRSF串口波特率
#define DEBUG_UART_BAUDRATE     115200      // 调试串口波特率

/* ADC配置 */
#define ADC_CHANNELS            13          // ADC通道数 (PA0-PA7, PB0, PC0-PC3)
#define ADC_SAMPLE_RATE         1000        // ADC采样率 (Hz) - 1ms采样周期
#define ADC_RESOLUTION          4096        // ADC分辨率 (12位)
#define ADC_DEFAULT_DEADBAND    20          // 默认死区

/* CRSF协议配置 */
#define CRSF_MAX_CHANNELS       16          // CRSF最大通道数
#define CRSF_PACKET_SIZE        26          // CRSF数据包大小
#define CRSF_UPDATE_RATE        100         // CRSF更新频率 (Hz)
#define CRSF_TIMEOUT_MS         1000        // CRSF超时时间

/* 按键配置 */
#define BUTTON_DEBOUNCE_MS      50          // 按键去抖时间
#define BUTTON_LONG_PRESS_MS    1000        // 长按时间

/* OLED配置 */
#define OLED_I2C_ADDRESS        0x3C        // OLED I2C地址
#define OLED_WIDTH              128         // OLED宽度
#define OLED_HEIGHT             64          // OLED高度

/* 任务调度配置 */
#define TASK_SCHEDULER_FREQ     1000        // 任务调度频率 (Hz)
#define MAX_TASKS               10          // 最大任务数

/* 内存配置 */
#define UART_RX_BUFFER_SIZE     256         // UART接收缓冲区大小
#define UART_TX_BUFFER_SIZE     256         // UART发送缓冲区大小
#define ADC_BUFFER_SIZE         (ADC_CHANNELS * 4)  // ADC缓冲区大小

/* GPIO引脚定义 */
// UART引脚
#define CRSF_UART_TX_PIN        GPIO_PIN_6
#define CRSF_UART_TX_PORT       GPIOB
#define CRSF_UART_RX_PIN        GPIO_PIN_7
#define CRSF_UART_RX_PORT       GPIOB

// I2C引脚 (I2C2)
#define OLED_I2C_SCL_PIN        GPIO_PIN_10
#define OLED_I2C_SCL_PORT       GPIOB
#define OLED_I2C_SDA_PIN        GPIO_PIN_11
#define OLED_I2C_SDA_PORT       GPIOB

// 按键引脚定义 (实际硬件配置)
#define KEY0_PIN                GPIO_PIN_9      // 一键起落按键，菜单模式复用为确定键
#define KEY0_PORT               GPIOC
#define KEY1_PIN                GPIO_PIN_8      // 双控对频按键，菜单模式复用为退出键
#define KEY1_PORT               GPIOC
#define KEY2_PIN                GPIO_PIN_7      // 自动返航按键，菜单模式复用为上键
#define KEY2_PORT               GPIOC
#define KEY3_PIN                GPIO_PIN_6      // 起落架收放，菜单模式复用为下键
#define KEY3_PORT               GPIOC
#define KEY4_PIN                GPIO_PIN_15     // 快门键，菜单模式复用为右键
#define KEY4_PORT               GPIOD
#define KEY5_PIN                GPIO_PIN_14     // 照片/视频切换，菜单模式复用为左键
#define KEY5_PORT               GPIOD

// 电源控制引脚定义
#define PWR_SW_PIN              GPIO_PIN_11     // 电源开关按键
#define PWR_SW_PORT             GPIOD
#define PWR_OFF_PIN             GPIO_PIN_12     // 电源开关控制
#define PWR_OFF_PORT            GPIOD
#define STDBY_PIN               GPIO_PIN_13     // 充电STDBY引脚
#define STDBY_PORT              GPIOD

// PWM输出引脚
#define BUZZER_PWM_PIN          GPIO_PIN_8      // 蜂鸣器PWM (TIM1_CH1)
#define BUZZER_PWM_PORT         GPIOA
#define VIBRATOR_PWM_PIN        GPIO_PIN_3      // 震动电机PWM (TIM3_CH1)
#define VIBRATOR_PWM_PORT       GPIOE

// LED引脚定义
#define LED4_PIN                GPIO_PIN_8      // 蓝色LED
#define LED4_PORT               GPIOB
#define LED5_PIN                GPIO_PIN_4      // 红色LED
#define LED5_PORT               GPIOC

// 按键引脚定义
#define PWR_SW_Pin              GPIO_PIN_11     // 电源开关按键
#define PWR_SW_GPIO_Port        GPIOD
#define PWR_OFF_Pin             GPIO_PIN_12     // 电源开关控制
#define PWR_OFF_GPIO_Port       GPIOD
#define STDBY_Pin               GPIO_PIN_13     // 充电STDBY引脚
#define STDBY_GPIO_Port         GPIOD
#define KEY5_Pin                GPIO_PIN_14     // 照片/视频切换，菜单模式复用为左键
#define KEY5_GPIO_Port          GPIOD
#define KEY4_Pin                GPIO_PIN_15     // 快门键，菜单模式复用为右键
#define KEY4_GPIO_Port          GPIOD
#define KEY3_Pin                GPIO_PIN_6      // 起落架收放，菜单模式复用为下键
#define KEY3_GPIO_Port          GPIOC
#define KEY2_Pin                GPIO_PIN_7      // 自动返航按键，菜单模式复用为上键
#define KEY2_GPIO_Port          GPIOC
#define KEY1_Pin                GPIO_PIN_8      // 双控对频按键，菜单模式复用为退出键
#define KEY1_GPIO_Port          GPIOC
#define KEY0_Pin                GPIO_PIN_9      // 一键起落按键，菜单模式复用为确定键
#define KEY0_GPIO_Port          GPIOC

// LED引脚定义
#define LED4_Pin                GPIO_PIN_8      // 蓝色LED
#define LED4_GPIO_Port          GPIOB
#define LED5_Pin                GPIO_PIN_4      // 红色LED
#define LED5_GPIO_Port          GPIOC

// PWM引脚定义 (实际硬件配置)
#define BUZZER_PIN              GPIO_PIN_8      // 蜂鸣器 PA8 (TIM1_CH1)
#define BUZZER_PORT             GPIOA
#define VIBRATOR_PIN            GPIO_PIN_3      // 震动电机 PE3 (TIM3_CH1)
#define VIBRATOR_PORT           GPIOE

// USB引脚定义
#define USB_DM_PIN              GPIO_PIN_11     // USB D-
#define USB_DM_PORT             GPIOA
#define USB_DP_PIN              GPIO_PIN_12     // USB D+
#define USB_DP_PORT             GPIOA

// EEPROM配置
#define FT24C128A_WRITE_ADDR    0xA0            // EEPROM I2C写地址
#define FT24C128A_READ_ADDR     0xA1            // EEPROM I2C读地址
#define EEPROM_PAGE_SIZE        64              // 每页字节数
#define EEPROM_TOTAL_PAGES      256             // 总页数
#define EEPROM_TOTAL_SIZE       (EEPROM_PAGE_SIZE * EEPROM_TOTAL_PAGES)  // 总容量16KB

/* RC通道配置 */
#define RC_CHANNEL_MIN          172         // RC通道最小值
#define RC_CHANNEL_MID          992         // RC通道中点值
#define RC_CHANNEL_MAX          1811        // RC通道最大值

/* 数据类型定义 */
typedef struct {
    uint16_t ch1;           // PA0 - 通道1
    uint16_t ch2;           // PA1 - 通道2
    uint16_t ch3;           // PA2 - 通道3
    uint16_t ch4;           // PA3 - 通道4
    uint16_t swa;           // PA4 - 开关A
    uint16_t swb;           // PA5 - 开关B
    uint16_t vra;           // PA6 - 电位器A
    uint16_t vrb;           // PA7 - 电位器B
    uint16_t vbat;          // PB0 - 电池电压
    uint16_t vbat2;         // PC0 - 电池电压2
    uint16_t bin;           // PC1 - 外接电源检测
    uint16_t vra2;          // PC2 - 电位器A2
    uint16_t vrb2;          // PC3 - 电位器B2
} rc_input_t;

typedef struct {
    bool enter;
    bool back;
    bool up;
    bool down;
    bool left;
    bool right;
} button_input_t;

typedef struct {
    uint8_t address;
    uint8_t number_of_params;
    uint8_t params_version;
    uint32_t serial_number;
    uint32_t hardware_id;
    uint32_t firmware_id;
    char name[32];
} crsf_device_t;

typedef struct {
    uint8_t update;
    uint8_t bad_pkts;
    uint16_t good_pkts;
    uint8_t flags;
    char flag_info[32];
} elrs_info_t;

/* 工具宏定义 */
#define MIN(a, b)               ((a) < (b) ? (a) : (b))
#define MAX(a, b)               ((a) > (b) ? (a) : (b))
#define CONSTRAIN(x, a, b)      ((x) < (a) ? (a) : ((x) > (b) ? (b) : (x)))
#define MAP(x, in_min, in_max, out_min, out_max) \
    (((x) - (in_min)) * ((out_max) - (out_min)) / ((in_max) - (in_min)) + (out_min))

/* 调试宏定义 */
#if DEBUG_ENABLED
#define DEBUG_PRINT(fmt, ...)   printf(fmt, ##__VA_ARGS__)
#else
#define DEBUG_PRINT(fmt, ...)   
#endif

/* 错误代码定义 */
typedef enum {
    ERR_OK = 0,
    ERR_INVALID_PARAM,
    ERR_TIMEOUT,
    ERR_BUSY,
    ERR_NOT_READY,
    ERR_HARDWARE_FAULT
} error_code_t;

#ifdef __cplusplus
}
#endif

#endif /* __CONFIG_H */
