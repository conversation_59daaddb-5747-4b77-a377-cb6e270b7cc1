# 软中断配置指南

## 🎯 软中断用途分析

### 需要软中断的引脚：
- **PB1** - 混音器计算 (高优先级实时任务) - PRIORITY_MEDIUM
- **PB2** - 音效处理 (音效回调任务) - PRIORITY_SOUND
- **PB3** - 后台任务 (一次性函数) - PRIORITY_LOW
- **PB4** - UART处理 (通信任务) - 自定义优先级

### 不需要软中断的引脚：
- **PA8** - 蜂鸣器PWM (硬件PWM，由TIM1驱动)
- **PE3** - 震动电机PWM (硬件PWM，由TIM3驱动)

## ⚙️ STM32CubeMX配置步骤

### 1. PB1 (混音器中断) 配置

#### Pinout配置：
```
在Pinout视图中:
右键点击PB1引脚 → GPIO_EXTI1
```

#### GPIO参数设置：
```
System Core → GPIO → PB1:

GPIO mode: External Interrupt Mode with Rising/Falling edge trigger detection
GPIO Pull-up/Pull-down: Pull-up
Maximum output speed: Low (不重要，因为是输入)
User Label: EXTI_MIXER

Additional Parameters:
External Interrupt Mode: Rising/Falling edge trigger detection
```

### 2. PB2 (音效处理中断) 配置

#### Pinout配置：
```
在Pinout视图中:
右键点击PB2引脚 → GPIO_EXTI2
```

#### GPIO参数设置：
```
System Core → GPIO → PB2:

GPIO mode: External Interrupt Mode with Falling edge trigger detection
GPIO Pull-up/Pull-down: Pull-up
User Label: EXTI_SOUND

Additional Parameters:
External Interrupt Mode: Falling edge trigger detection
```

### 3. PB3 (后台任务中断) 配置

#### Pinout配置：
```
在Pinout视图中:
右键点击PB3引脚 → GPIO_EXTI3
```

#### GPIO参数设置：
```
System Core → GPIO → PB3:

GPIO mode: External Interrupt Mode with Falling edge trigger detection
GPIO Pull-up/Pull-down: Pull-up
User Label: EXTI_BTN1

Additional Parameters:
External Interrupt Mode: Falling edge trigger detection
```

### 3. PB4 (UART处理中断) 配置

#### Pinout配置：
```
在Pinout视图中:
右键点击PB4引脚 → GPIO_EXTI4
```

#### GPIO参数设置：
```
System Core → GPIO → PB4:

GPIO mode: External Interrupt Mode with Falling edge trigger detection
GPIO Pull-up/Pull-down: Pull-up
User Label: EXTI_BTN2

Additional Parameters:
External Interrupt Mode: Falling edge trigger detection
```

### 4. NVIC中断优先级配置

```
System Core → NVIC:

☑ EXTI line[0:1] interrupts
  Priority: 1
  Subpriority: 0
  (处理PB1/EXTI1 - 混音器计算)

☑ EXTI line[2:3] interrupts  
  Priority: 3
  Subpriority: 0
  (处理PB3/EXTI3 - 后台任务)

☑ EXTI line[4:15] interrupts
  Priority: 2
  Subpriority: 0
  (处理PB4/EXTI4 - UART处理)
```

## 📋 当前IOC配置验证

### ✅ PB1配置 (混音器中断)：
```ini
PB1.GPIOParameters=GPIO_Label,GPIO_PuPd,GPIO_ModeDefaultEXTI
PB1.GPIO_Label=EXTI_MIXER
PB1.GPIO_PuPd=GPIO_PULLUP
PB1.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PB1.Locked=true
PB1.Signal=GPXTI1
```

### ✅ PB3配置 (后台任务中断)：
```ini
PB3.GPIOParameters=GPIO_Label,GPIO_PuPd,GPIO_ModeDefaultEXTI
PB3.GPIO_Label=EXTI_BTN1
PB3.GPIO_PuPd=GPIO_PULLUP
PB3.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PB3.Locked=true
PB3.Signal=GPXTI3
```

### ✅ PB4配置 (UART处理中断)：
```ini
PB4.GPIOParameters=GPIO_Label,GPIO_PuPd,GPIO_ModeDefaultEXTI
PB4.GPIO_Label=EXTI_BTN2
PB4.GPIO_PuPd=GPIO_PULLUP
PB4.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PB4.Locked=true
PB4.Signal=GPXTI4
```

### ✅ NVIC中断优先级：
```ini
NVIC.EXTI0_1_IRQn=true:1:0         # PB1混音器 - 高优先级
NVIC.EXTI2_3_IRQn=true:3:0         # PB3后台任务 - 低优先级
NVIC.EXTI4_15_IRQn=true:2:0        # PB4 UART - 中优先级
```

## 🔧 PWM引脚配置 (不需要软中断)

### ✅ PA8 (蜂鸣器PWM)：
```ini
PA8.GPIOParameters=GPIO_Label
PA8.GPIO_Label=TIM1_CH1
PA8.Locked=true
PA8.Signal=S_TIM1_CH1              # TIM1通道1 PWM输出
```

### ✅ PE3 (震动电机PWM)：
```ini
PE3.GPIOParameters=GPIO_Label
PE3.GPIO_Label=VIBRATOR_PWM
PE3.Locked=true
PE3.Signal=S_TIM3_CH1              # TIM3通道1 PWM输出
```

## 💻 代码中的软中断使用

### 1. 触发软中断
```c
// 触发混音器计算 (PB1/EXTI1)
void CLOCK_RunMixer(void)
{
    mixer_sync = MIX_NOT_DONE;
    HAL_NVIC_SetPendingIRQ(EXTI0_1_IRQn);
}

// 触发后台任务 (PB3/EXTI3)
void CLOCK_RunOnce(func_callback_t cb)
{
    func_callback = cb;
    HAL_NVIC_SetPendingIRQ(EXTI2_3_IRQn);
}

// 触发UART处理 (PB4/EXTI4)
void CLOCK_RunUART(func_callback_t cb)
{
    func_callback = cb;
    HAL_NVIC_SetPendingIRQ(EXTI4_15_IRQn);
}
```

### 2. 中断处理函数
```c
// PB1混音器中断处理
void EXTI0_1_IRQHandler(void)
{
    if (__HAL_GPIO_EXTI_GET_FLAG(GPIO_PIN_1)) {
        __HAL_GPIO_EXTI_CLEAR_FLAG(GPIO_PIN_1);
        
        ADC_Filter();
        MIXER_CalcChannels();
        
        if (mixer_sync == MIX_NOT_DONE) {
            mixer_sync = MIX_DONE;
        }
    }
}

// PB3后台任务中断处理
void EXTI2_3_IRQHandler(void)
{
    if (__HAL_GPIO_EXTI_GET_FLAG(GPIO_PIN_3)) {
        __HAL_GPIO_EXTI_CLEAR_FLAG(GPIO_PIN_3);
        
        if (func_callback) {
            func_callback();
            func_callback = NULL;
        }
    }
}

// PB4 UART处理中断
void EXTI4_15_IRQHandler(void)
{
    if (__HAL_GPIO_EXTI_GET_FLAG(GPIO_PIN_4)) {
        __HAL_GPIO_EXTI_CLEAR_FLAG(GPIO_PIN_4);
        
        if (func_callback) {
            func_callback();
            func_callback = NULL;
        }
    }
}
```

## 🎵 PWM控制 (硬件驱动，无需软中断)

### 蜂鸣器控制 (PA8/TIM1)：
```c
// 设置蜂鸣器频率
void Buzzer_SetFrequency(uint16_t frequency)
{
    uint32_t period = 1000000 / frequency;  // 1MHz时钟
    __HAL_TIM_SET_AUTORELOAD(&htim1, period - 1);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, period / 2);
}

// 开启蜂鸣器
void Buzzer_On(void)
{
    HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
}
```

### 震动电机控制 (PE3/TIM3)：
```c
// 设置震动强度
void Vibrator_SetIntensity(uint8_t intensity)
{
    uint32_t period = __HAL_TIM_GET_AUTORELOAD(&htim3) + 1;
    uint32_t pulse = (period * intensity) / 100;
    __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, pulse);
}

// 开启震动
void Vibrator_On(void)
{
    HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_1);
}
```

## 🔍 配置要点总结

### 软中断引脚 (PB1, PB3, PB4)：
- ✅ **配置为EXTI模式** (外部中断)
- ✅ **设置上拉电阻** (防止浮空)
- ✅ **配置触发边沿** (上升沿/下降沿)
- ✅ **设置中断优先级** (1, 2, 3)

### PWM引脚 (PA8, PE3)：
- ✅ **配置为定时器输出** (TIM1_CH1, TIM3_CH1)
- ✅ **不需要EXTI配置** (硬件PWM)
- ✅ **不需要中断优先级** (硬件驱动)
- ✅ **通过定时器控制** (频率、占空比)

## ⚠️ 注意事项

1. **软中断是虚拟的**: 通过`HAL_NVIC_SetPendingIRQ()`触发，不依赖外部信号
2. **PWM是硬件的**: 由定时器硬件自动生成，CPU开销极小
3. **优先级很重要**: 混音器 > UART > 后台任务
4. **避免冲突**: 同一EXTI线上不能有多个引脚同时使用

---
*配置状态: ✅ 已完成*
*软中断引脚: PB1, PB3, PB4*
*PWM引脚: PA8, PE3*
