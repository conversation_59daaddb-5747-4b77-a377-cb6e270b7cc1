# CRSF Controller 校准功能说明

## 🎯 校准功能概述

本遥控器支持两种校准模式：
1. **开机自动校准** - 首次使用或校准数据丢失时自动启动
2. **菜单手动校准** - 通过菜单系统手动启动校准

## 📊 ADC通道说明

### 摇杆通道 (需要校准中心点和行程)
```
ADC_CH1 - 摇杆CH1 (副翼)
ADC_CH2 - 摇杆CH2 (升降)  
ADC_CH3 - 摇杆CH3 (油门)
ADC_CH4 - 摇杆CH4 (方向)
```

### 三段开关 (需要校准三个位置)
```
ADC_SWA - 三段开关A (飞行模式选择)
ADC_SWB - 三段开关B (相机参数选择)
```

### 电位器 (需要校准行程)
```
ADC_VRA - 电位器A (云台俯仰拨轮)
ADC_VRB - 电位器B (相机参数调整拨轮)
```

### 系统监测 (无需校准)
```
ADC_VBAT - 电池电压检测
ADC_BIN  - 外接电源检测
```

## 🚀 开机校准流程

### 触发条件
- 首次开机使用
- EEPROM中无有效校准数据
- 校准数据损坏或异常

### 校准步骤

#### 步骤1: 摇杆居中 (3秒)
```
操作: 将所有摇杆置于中心位置
目的: 校准摇杆中心点
提示: "将摇杆置于中心位置"
```

#### 步骤2: 摇杆全行程 (持续)
```
操作: 转动所有摇杆到各个极限位置
目的: 校准摇杆最小值和最大值
提示: "转动摇杆到各个极限位置"
完成条件: 所有摇杆都达到足够的行程范围
```

#### 步骤3: 开关下位 (1秒稳定)
```
操作: 将SWA和SWB开关拨到下位
目的: 校准开关下位置值
提示: "将开关拨到下位"
```

#### 步骤4: 开关中位 (1秒稳定)
```
操作: 将SWA和SWB开关拨到中位
目的: 校准开关中位置值
提示: "将开关拨到中位"
```

#### 步骤5: 开关上位 (1秒稳定)
```
操作: 将SWA和SWB开关拨到上位
目的: 校准开关上位置值
提示: "将开关拨到上位"
```

#### 步骤6: 保存数据
```
操作: 自动保存校准数据到EEPROM
提示: "正在保存校准数据..."
```

#### 步骤7: 完成
```
提示: "校准完成，按任意键继续"
音效: 成功提示音
```

## 🎮 菜单校准功能

### 进入方式
```
主菜单 -> 校准 -> 选择校准类型
```

### 校准选项

#### 1. 摇杆校准
- 仅校准4个摇杆通道
- 包含中心点和行程校准
- 适用于摇杆漂移或行程异常

#### 2. 开关校准  
- 仅校准2个三段开关
- 校准三个位置的ADC值
- 适用于开关位置检测异常

#### 3. 完整校准
- 等同于开机校准
- 校准所有需要校准的通道
- 适用于全面重新校准

#### 4. 重置校准
- 恢复默认校准值
- 清除所有校准数据
- 适用于校准数据严重错误

#### 5. 校准状态
- 显示当前校准数据状态
- 显示各通道校准值
- 用于检查校准结果

## 📈 校准算法说明

### 摇杆校准算法
```c
// 中心点校准 (1秒采样平均)
center_value = average(samples_1000ms);

// 行程校准 (持续更新最值)
while (moving) {
    current_value = adc_read();
    min_value = min(min_value, current_value);
    max_value = max(max_value, current_value);
}

// 校准完成条件
range = max_value - min_value;
if (range > MOVEMENT_THRESHOLD * 2) {
    calibration_complete = true;
}
```

### 开关校准算法
```c
// 位置检测 (1秒稳定性检查)
if (abs(current_value - last_value) < TOLERANCE) {
    if (stable_time > 1000ms) {
        position_value[position] = current_value;
        next_position();
    }
}

// 位置判断算法
switch_position_t get_position(uint16_t value) {
    dist_low = abs(value - low_value);
    dist_mid = abs(value - mid_value);  
    dist_high = abs(value - high_value);
    
    return position_with_min_distance;
}
```

## ⚙️ 校准参数配置

### 关键参数
```c
#define CALIB_SAMPLE_TIME_MS        1000    // 采样时间
#define CALIB_SAMPLE_COUNT_MIN      50      // 最小采样数
#define CALIB_CENTER_TOLERANCE      50      // 中心位置容差
#define CALIB_MOVEMENT_THRESHOLD    200     // 移动阈值
#define CALIB_SWITCH_TOLERANCE      100     // 开关位置容差
```

### 校准质量检查
- **摇杆行程检查**: 最小行程 > 400 (约10%)
- **中心点检查**: 中心点在 40%-60% 范围内
- **开关间距检查**: 相邻位置差值 > 200
- **数据一致性检查**: min < center < max

## 🔧 故障排除

### 常见问题

#### 1. 摇杆校准失败
**现象**: 无法完成摇杆行程校准
**原因**: 摇杆行程不足或硬件故障
**解决**: 
- 确保摇杆能完全转动到极限位置
- 检查摇杆机械结构
- 检查ADC硬件连接

#### 2. 开关位置检测错误
**现象**: 开关位置显示错误
**原因**: 开关校准数据异常
**解决**:
- 重新进行开关校准
- 检查开关机械接触
- 检查ADC分压电路

#### 3. 校准数据丢失
**现象**: 每次开机都要重新校准
**原因**: EEPROM写入失败或数据损坏
**解决**:
- 检查EEPROM硬件连接
- 检查I2C通信
- 重新格式化EEPROM

#### 4. 校准界面无响应
**现象**: 校准过程中界面卡死
**原因**: 任务调度或显示问题
**解决**:
- 重启设备
- 检查OLED显示连接
- 检查按键响应

## 💡 使用建议

### 最佳实践
1. **首次使用**: 务必完成开机校准
2. **定期校准**: 建议每月进行一次完整校准
3. **环境变化**: 温度变化较大时重新校准
4. **维护后**: 拆装维护后必须重新校准

### 校准技巧
1. **摇杆居中**: 使用机械限位或目视确保居中
2. **行程校准**: 缓慢转动，确保到达真正的极限位置
3. **开关校准**: 确保开关完全到位，听到咔嗒声
4. **环境稳定**: 在稳定的温度环境下进行校准

这个校准系统参考了主流遥控器的设计，具有简洁的操作流程和可靠的算法，确保校准的准确性和用户体验。
