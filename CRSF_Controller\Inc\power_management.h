/**
 * @file power_management.h
 * @brief 电源管理接口
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __POWER_MANAGEMENT_H
#define __POWER_MANAGEMENT_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"

/* 电源状态定义 */
typedef enum {
    POWER_STATE_UNKNOWN = 0,
    POWER_STATE_BATTERY,        // 电池供电
    POWER_STATE_USB,            // USB供电
    POWER_STATE_EXTERNAL,       // 外部电源供电
    POWER_STATE_CHARGING,       // 充电中
    POWER_STATE_CHARGED,        // 充电完成
    POWER_STATE_LOW_BATTERY,    // 低电量
    POWER_STATE_CRITICAL        // 电量危险
} power_state_t;

/* 充电状态定义 */
typedef enum {
    CHARGE_STATE_NOT_CHARGING = 0,
    CHARGE_STATE_CHARGING,
    CHARGE_STATE_COMPLETE,
    CHARGE_STATE_ERROR
} charge_state_t;

/* 电源信息结构 */
typedef struct {
    power_state_t state;        // 电源状态
    charge_state_t charge_state; // 充电状态
    uint16_t battery_voltage;   // 电池电压 (mV)
    uint8_t battery_percent;    // 电池电量百分比
    uint16_t external_voltage;  // 外部电源电压 (mV)
    bool usb_connected;         // USB连接状态
    bool external_connected;    // 外部电源连接状态
    bool charging_active;       // 充电激活状态
    bool stdby_active;          // STDBY信号状态
    uint32_t last_update_time;  // 上次更新时间
} power_info_t;

/* 电源配置结构 */
typedef struct {
    uint16_t low_battery_threshold;     // 低电量阈值 (mV)
    uint16_t critical_battery_threshold; // 危险电量阈值 (mV)
    uint16_t full_battery_voltage;      // 满电电压 (mV)
    uint16_t empty_battery_voltage;     // 空电电压 (mV)
    uint32_t auto_shutdown_time;        // 自动关机时间 (ms)
    uint32_t low_battery_warning_interval; // 低电量警告间隔 (ms)
    bool auto_shutdown_enabled;        // 自动关机使能
} power_config_t;

/* 全局变量声明 */
extern power_info_t power_info;
extern power_config_t power_config;

/* 函数声明 */

/* 初始化和配置 */
error_code_t Power_Init(void);
error_code_t Power_Configure(const power_config_t* config);
void Power_SetAutoShutdownTime(uint32_t time_minutes);

/* 电源控制 */
void Power_Shutdown(void);
void Power_Reset(void);
bool Power_IsShutdownRequested(void);
void Power_CancelShutdown(void);

/* 状态监测 */
void Power_UpdateStatus(void);
power_state_t Power_GetState(void);
charge_state_t Power_GetChargeState(void);
uint16_t Power_GetBatteryVoltage(void);
uint8_t Power_GetBatteryPercent(void);
bool Power_IsUSBConnected(void);
bool Power_IsExternalConnected(void);
bool Power_IsCharging(void);
bool Power_IsLowBattery(void);
bool Power_IsCriticalBattery(void);

/* 电压转换 */
uint16_t Power_ADCToBatteryVoltage(uint16_t adc_value);
uint16_t Power_ADCToExternalVoltage(uint16_t adc_value);
uint8_t Power_VoltageToPercent(uint16_t voltage);

/* 充电管理 */
void Power_StartCharging(void);
void Power_StopCharging(void);
bool Power_IsChargingComplete(void);
uint32_t Power_GetChargingTime(void);

/* 低功耗管理 */
void Power_EnterSleepMode(void);
void Power_EnterStopMode(void);
void Power_ExitLowPowerMode(void);
bool Power_IsInLowPowerMode(void);

/* 任务函数 */
void Power_Task(void* parameters);

/* 回调函数 */
void Power_OnStateChanged(power_state_t old_state, power_state_t new_state);
void Power_OnLowBattery(void);
void Power_OnCriticalBattery(void);
void Power_OnUSBConnected(void);
void Power_OnUSBDisconnected(void);
void Power_OnChargingStarted(void);
void Power_OnChargingComplete(void);

/* 工具函数 */
const char* Power_GetStateString(power_state_t state);
const char* Power_GetChargeStateString(charge_state_t state);
void Power_PrintStatus(void);

/* 调试功能 */
#if DEBUG_ENABLED
void Power_PrintInfo(void);
void Power_TestVoltageConversion(void);
#endif

/* 默认配置值 */
#define DEFAULT_LOW_BATTERY_THRESHOLD       3300    // 3.3V
#define DEFAULT_CRITICAL_BATTERY_THRESHOLD  3000    // 3.0V
#define DEFAULT_FULL_BATTERY_VOLTAGE        4200    // 4.2V
#define DEFAULT_EMPTY_BATTERY_VOLTAGE       3000    // 3.0V
#define DEFAULT_AUTO_SHUTDOWN_TIME          600000  // 10分钟
#define DEFAULT_LOW_BATTERY_WARNING_INTERVAL 30000  // 30秒

/* 电压分压比 (根据硬件设计调整) */
#define BATTERY_VOLTAGE_DIVIDER_RATIO       2.0f    // 假设2:1分压
#define EXTERNAL_VOLTAGE_DIVIDER_RATIO      3.0f    // 假设3:1分压

/* ADC参考电压 */
#define ADC_REFERENCE_VOLTAGE               3300    // 3.3V (mV)

#ifdef __cplusplus
}
#endif

#endif /* __POWER_MANAGEMENT_H */
