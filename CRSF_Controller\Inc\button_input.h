/**
 * @file button_input.h
 * @brief 按键输入处理接口
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __BUTTON_INPUT_H
#define __BUTTON_INPUT_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"

/* 按键定义 */
typedef enum {
    BUTTON_KEY0 = 0,        // PC9 - 一键起落按键，菜单模式复用为确定键
    BUTTON_KEY1,            // PC8 - 双控对频按键，菜单模式复用为退出键
    BUTTON_KEY2,            // PC7 - 自动返航按键，菜单模式复用为上键
    BUTTON_KEY3,            // PC6 - 起落架收放，菜单模式复用为下键
    BUTTON_KEY4,            // PD15 - 快门键，菜单模式复用为右键
    BUTTON_KEY5,            // PD14 - 照片/视频切换，菜单模式复用为左键
    BUTTON_PWR_SW,          // PD11 - 电源开关按键
    BUTTON_COUNT
} button_id_t;

/* 菜单模式按键映射 */
typedef enum {
    MENU_BUTTON_ENTER = BUTTON_KEY0,    // 确定键
    MENU_BUTTON_BACK = BUTTON_KEY1,     // 退出键
    MENU_BUTTON_UP = BUTTON_KEY2,       // 上键
    MENU_BUTTON_DOWN = BUTTON_KEY3,     // 下键
    MENU_BUTTON_RIGHT = BUTTON_KEY4,    // 右键
    MENU_BUTTON_LEFT = BUTTON_KEY5      // 左键
} menu_button_t;

/* 按键状态 */
typedef enum {
    BUTTON_STATE_RELEASED = 0,
    BUTTON_STATE_PRESSED,
    BUTTON_STATE_LONG_PRESSED,
    BUTTON_STATE_REPEAT
} button_state_t;

/* 按键事件 */
typedef enum {
    BUTTON_EVENT_NONE = 0,
    BUTTON_EVENT_PRESS,
    BUTTON_EVENT_RELEASE,
    BUTTON_EVENT_LONG_PRESS,
    BUTTON_EVENT_REPEAT,
    BUTTON_EVENT_CLICK,
    BUTTON_EVENT_DOUBLE_CLICK
} button_event_t;

/* 按键配置 */
typedef struct {
    uint32_t debounce_time_ms;      // 去抖时间
    uint32_t long_press_time_ms;    // 长按时间
    uint32_t repeat_delay_ms;       // 重复延时
    uint32_t repeat_rate_ms;        // 重复速率
    uint32_t double_click_time_ms;  // 双击时间
    bool repeat_enabled;            // 重复使能
    bool long_press_enabled;        // 长按使能
    bool double_click_enabled;      // 双击使能
} button_config_t;

/* 按键状态信息 */
typedef struct {
    button_state_t state;           // 当前状态
    button_event_t last_event;      // 最后事件
    uint32_t press_time;            // 按下时间
    uint32_t release_time;          // 释放时间
    uint32_t last_repeat_time;      // 上次重复时间
    uint32_t click_count;           // 点击计数
    bool raw_state;                 // 原始状态
    bool debounced_state;           // 去抖后状态
    bool event_pending;             // 事件待处理
} button_status_t;

/* 按键回调函数类型 */
typedef void (*button_callback_t)(button_id_t button, button_event_t event);

/* 全局变量声明 */
extern button_config_t button_config;
extern button_status_t button_status[BUTTON_COUNT];

/* 函数声明 */

/* 初始化和配置 */
error_code_t Button_Input_Init(void);
error_code_t Button_Input_Configure(const button_config_t* config);
void Button_Input_SetCallback(button_callback_t callback);

/* 按键读取和处理 */
void Button_Input_Scan(void);
bool Button_Input_IsPressed(button_id_t button);
bool Button_Input_IsReleased(button_id_t button);
button_state_t Button_Input_GetState(button_id_t button);
button_event_t Button_Input_GetEvent(button_id_t button);
void Button_Input_ClearEvent(button_id_t button);

/* 按键状态查询 */
uint32_t Button_Input_GetPressTime(button_id_t button);
uint32_t Button_Input_GetReleaseTime(button_id_t button);
bool Button_Input_IsLongPressed(button_id_t button);
bool Button_Input_IsDoubleClicked(button_id_t button);

/* 任务函数 */
void Button_Input_Task(void* parameters);

/* 工具函数 */
void Button_Input_GetAllStates(button_input_t* button_input);
bool Button_Input_AnyPressed(void);
uint8_t Button_Input_GetPressedCount(void);
uint32_t Button_Input_GetPressedMask(void);

/* 调试功能 */
#if DEBUG_ENABLED
void Button_Input_PrintStatus(void);
void Button_Input_PrintEvents(void);
#endif

/* 默认配置值 */
#define BUTTON_DEFAULT_DEBOUNCE_TIME    50      // 50ms去抖
#define BUTTON_DEFAULT_LONG_PRESS_TIME  1000    // 1s长按
#define BUTTON_DEFAULT_REPEAT_DELAY     500     // 500ms重复延时
#define BUTTON_DEFAULT_REPEAT_RATE      100     // 100ms重复速率
#define BUTTON_DEFAULT_DOUBLE_CLICK_TIME 300    // 300ms双击时间

/* 按键状态结构 */
typedef struct {
    bool key0;              // 一键起落/确定键
    bool key1;              // 双控对频/退出键
    bool key2;              // 自动返航/上键
    bool key3;              // 起落架收放/下键
    bool key4;              // 快门/右键
    bool key5;              // 照片视频切换/左键
    bool pwr_sw;            // 电源开关
    bool menu_mode;         // 菜单模式标志
} button_input_t;

/* 按键组合检测 */
bool Button_Input_IsMenuCombo(void);        // KEY4 + KEY5 组合 (进入/退出菜单)
bool Button_Input_IsExitMenuCombo(void);    // KEY4 + KEY5 组合 (退出菜单)

/* 菜单模式控制 */
void Button_Input_EnterMenuMode(void);
void Button_Input_ExitMenuMode(void);
bool Button_Input_IsMenuMode(void);

/* 按键GPIO映射 */
#define BUTTON_GPIO_PORT(btn) \
    ((btn) == BUTTON_KEY0 ? KEY0_GPIO_Port : \
     (btn) == BUTTON_KEY1 ? KEY1_GPIO_Port : \
     (btn) == BUTTON_KEY2 ? KEY2_GPIO_Port : \
     (btn) == BUTTON_KEY3 ? KEY3_GPIO_Port : \
     (btn) == BUTTON_KEY4 ? KEY4_GPIO_Port : \
     (btn) == BUTTON_KEY5 ? KEY5_GPIO_Port : \
     (btn) == BUTTON_PWR_SW ? PWR_SW_GPIO_Port : NULL)

#define BUTTON_GPIO_PIN(btn) \
    ((btn) == BUTTON_KEY0 ? KEY0_Pin : \
     (btn) == BUTTON_KEY1 ? KEY1_Pin : \
     (btn) == BUTTON_KEY2 ? KEY2_Pin : \
     (btn) == BUTTON_KEY3 ? KEY3_Pin : \
     (btn) == BUTTON_KEY4 ? KEY4_Pin : \
     (btn) == BUTTON_KEY5 ? KEY5_Pin : \
     (btn) == BUTTON_PWR_SW ? PWR_SW_Pin : 0)

#ifdef __cplusplus
}
#endif

#endif /* __BUTTON_INPUT_H */
