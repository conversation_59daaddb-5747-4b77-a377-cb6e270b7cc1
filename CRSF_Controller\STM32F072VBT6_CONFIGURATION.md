# STM32F072VBT6 配置说明

## 📖 **概述**

已将CRSF Controller的STM32CubeMX配置从STM32F072CBT6 (LQFP48) 升级到STM32F072VBT6 (LQFP100)，提供更多的GPIO引脚和扩展功能。

## 🔄 **主要变更**

### MCU型号变更
```
原型号: STM32F072CBT6 (LQFP48, 48引脚)
新型号: STM32F072VBT6 (LQFP100, 100引脚)

封装变更: LQFP48 → LQFP100
引脚数量: 22个配置引脚 → 50个配置引脚
```

### 新增外设支持
```
新增外设:
✅ I2C1 - 额外的I2C接口
✅ CRC - 硬件CRC计算单元
✅ IWDG - 独立看门狗
✅ TIM2 - CRSF定时器
✅ TIM3 - 多通道PWM定时器
✅ TIM15 - ADC触发定时器
✅ USB_DEVICE - USB设备类支持
```

## 📌 **引脚分配**

### 保持不变的核心引脚
```
ADC输入 (PA0-PA7, PB0-PB1):
PA0 - ADC_CH1 (右摇杆X)
PA1 - ADC_CH2 (右摇杆Y)  
PA2 - ADC_CH3 (左摇杆Y)
PA3 - ADC_CH4 (左摇杆X)
PA4 - ADC_SWA (三段开关A)
PA5 - ADC_SWB (三段开关B)
PA6 - ADC_VRA (电位器A)
PA7 - ADC_VRB (电位器B)
PB0 - ADC_VBAT (电池电压)
PB1 - ADC_VIN (外接电源)

基本通信接口:
PA9  - USART1_TX (CRSF发送)
PA10 - USART1_RX (CRSF接收)
PB10 - I2C2_SCL (OLED时钟)
PB11 - I2C2_SDA (OLED数据)
PA11 - USB_DM
PA12 - USB_DP

调试接口:
PA13 - SWDIO
PA14 - SWCLK

时钟源:
PF0 - OSC_IN (8MHz晶振)
PF1 - OSC_OUT
PC14 - OSC32_IN (32.768kHz晶振)
PC15 - OSC32_OUT
```

### 新增的扩展引脚 ⭐
```
I2C1接口 (额外I2C):
PB8 - I2C1_SCL (时钟)
PB9 - I2C1_SDA (数据)

替代UART引脚:
PB6 - USART1_TX_ALT (备用发送)
PB7 - USART1_RX_ALT (备用接收)

软件中断引脚:
PB1 - EXTI1 (混音器计算)
PB3 - EXTI3 (一次性任务)
PB4 - EXTI4 (UART处理)

多通道PWM (TIM3):
PA8  - TIM1_CH1 (蜂鸣器PWM)
PE3  - TIM3_CH1 (震动电机PWM)
PC6  - TIM3_CH1_ALT (备用PWM1)
PC7  - TIM3_CH2 (PWM通道2)
PC8  - TIM3_CH3 (PWM通道3)
PC9  - TIM3_CH4 (PWM通道4)
PD2  - TIM3_ETR (外部触发)

扩展GPIO:
PE0  - GPIO_EXT1 (扩展GPIO1)
PE1  - GPIO_EXT2 (扩展GPIO2)
PE2  - GPIO_EXT3 (扩展GPIO3)
PB5  - GPIO_EXT4 (扩展GPIO4) ⭐ 新增
PE4  - LED1 (LED指示1)
PE5  - LED2 (LED指示2)
PE6  - LED3 (LED指示3)
PE7  - LED4 (LED指示4)
PE8  - LED5 (LED指示5) ⭐ 新增
PE9  - LED6 (LED指示6) ⭐ 新增
PE10 - LED7 (LED指示7) ⭐ 新增
PE11 - LED8 (LED指示8) ⭐ 新增
PE12 - GPIO_EXT5 (扩展GPIO5) ⭐ 新增
PE13 - GPIO_EXT6 (扩展GPIO6) ⭐ 新增
PE14 - GPIO_EXT7 (扩展GPIO7) ⭐ 新增
PE15 - GPIO_SPARE (备用GPIO)
PC13 - STATUS_LED (状态LED)
```

## 🔧 **外设配置详解**

### I2C1配置
```c
模式: 快速模式 (Fast Mode)
速度: 100kHz
地址长度: 7位

用途:
- 额外的传感器接口
- 外部EEPROM
- 实时时钟 (RTC)
- 温度传感器
```

### TIM3多通道PWM
```c
预分频: 47 (1MHz时钟)
周期: 999 (1kHz PWM)
通道: 4个独立PWM通道

用途:
- 多路舵机控制
- RGB LED控制
- 多个震动电机
- 风扇速度控制
```

### 扩展LED阵列
```c
LED1-LED8: 8个独立LED指示
- 系统状态指示
- 通道活动指示
- 错误代码显示
- 用户自定义指示
```

## 🔌 **DMA配置**

### DMA通道分配
```
DMA1_CH1: ADC (P2M, Circular, High)
DMA1_CH2: USART1_TX (M2P, Normal, Medium)
DMA1_CH3: USART1_RX (P2M, Circular, Medium)
DMA1_CH4: I2C2_TX (M2P, Normal, Low)
DMA1_CH5: I2C2_RX (P2M, Normal, Low)
DMA1_CH6: I2C1_TX (M2P, Normal, Low) ⭐ 新增
DMA1_CH7: I2C1_RX (P2M, Normal, Low) ⭐ 新增
```

### 中断优先级
```
TIM2 (CRSF): 优先级0 (最高)
EXTI1 (混音器): 优先级1 (高)
EXTI4-15 (UART): 优先级2 (中)
EXTI2-3 (任务): 优先级3 (低)
USART1: 优先级4
TIM15 (ADC触发): 优先级5
I2C1/I2C2: 优先级6
USB: 优先级7 (最低)
```

## 🚀 **扩展功能应用**

### 1. 多路舵机控制
```c
// TIM3四通道PWM输出
TIM3_CH1 → 舵机1 (副翼)
TIM3_CH2 → 舵机2 (升降)
TIM3_CH3 → 舵机3 (方向)
TIM3_CH4 → 舵机4 (油门)
```

### 3. 扩展存储
```c
// I2C1连接外部EEPROM
I2C1 → AT24C256 (32KB)
- 模型数据存储
- 飞行日志记录
- 用户配置备份
```

### 4. 状态指示系统
```c
// 8路LED状态指示
LED1: 电源状态
LED2: CRSF连接状态
LED3: 数据传输状态
LED4: 错误指示
LED5: 通道1活动
LED6: 通道2活动
LED7: 通道3活动
LED8: 通道4活动
```

## 📋 **移植注意事项**

### 硬件设计
```
PCB设计:
- 从48引脚升级到100引脚封装
- 增加I2C1的外部接口
- 预留扩展GPIO的连接器
- 增加LED指示电路

电源设计:
- 确保3.3V电源能力足够
- 增加去耦电容
- 考虑数字噪声隔离
```

### 软件适配
```c
// 在main.c中添加新外设初始化
MX_I2C1_Init();
MX_TIM2_Init();
MX_TIM3_Init();
MX_TIM15_Init();
MX_CRC_Init();
MX_IWDG_Init();

// 更新中断处理函数
void EXTI2_3_IRQHandler(void);
void EXTI4_15_IRQHandler(void);
void TIM2_IRQHandler(void);
void TIM15_IRQHandler(void);
void I2C1_IRQHandler(void);
```

### 配置验证
```
验证清单:
☑ MCU型号正确 (STM32F072VBT6)
☑ 封装类型正确 (LQFP100)
☑ 引脚分配无冲突
☑ 时钟配置正确 (48MHz)
☑ 外设参数合理
☑ DMA通道无冲突
☑ 中断优先级合理
☑ 编译无错误
```

## 🎯 **总结**

STM32F072VBT6配置提供了：

✅ **更多GPIO**: 从22个增加到50个配置引脚
✅ **扩展接口**: I2C1等额外通信接口
✅ **多路PWM**: TIM3四通道PWM输出
✅ **丰富指示**: 8路LED状态指示
✅ **精确ADC**: TIM15硬件触发ADC采样
✅ **硬件加速**: CRC、IWDG等硬件单元
✅ **向后兼容**: 保持原有核心功能不变

这个配置为CRSF Controller提供了强大的扩展能力，支持更复杂的应用场景！
