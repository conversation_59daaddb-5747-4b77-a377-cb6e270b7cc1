/**
 * @file crsf_protocol.h
 * @brief CRSF协议定义
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __CRSF_PROTOCOL_H
#define __CRSF_PROTOCOL_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"

/* CRSF协议常量 */
#define CRSF_SYNC_BYTE                  0xC8
#define CRSF_FRAME_SIZE_MAX             64
#define CRSF_PAYLOAD_SIZE_MAX           62
#define CRSF_FRAME_NOT_COUNTED_BYTES    2
#define CRSF_FRAME_CRC_SIZE             1
#define CRSF_FRAME_LENGTH_EXT_TYPE_CRC  4

/* CRSF地址定义 */
#define CRSF_ADDRESS_BROADCAST          0x00
#define CRSF_ADDRESS_USB                0x10
#define CRSF_ADDRESS_TBS_CORE_PNP_PRO   0x80
#define CRSF_ADDRESS_RESERVED1          0x8A
#define CRSF_ADDRESS_CURRENT_SENSOR     0xC0
#define CRSF_ADDRESS_GPS                0xC2
#define CRSF_ADDRESS_TBS_BLACKBOX       0xC4
#define CRSF_ADDRESS_FLIGHT_CONTROLLER  0xC8
#define CRSF_ADDRESS_RESERVED2          0xCA
#define CRSF_ADDRESS_RACE_TAG           0xCC
#define CRSF_ADDRESS_RADIO_TRANSMITTER  0xEA
#define CRSF_ADDRESS_CRSF_RECEIVER      0xEC
#define CRSF_ADDRESS_CRSF_TRANSMITTER   0xEE

/* ELRS地址定义 */
#define ELRS_ADDRESS                    0xEE
#define ELRS_RX_ADDRESS                 0xEC
#define ADDR_MODULE                     0xEE
#define ADDR_RADIO                      0xEA

/* CRSF帧类型定义 */
typedef enum {
    CRSF_FRAMETYPE_GPS = 0x02,
    CRSF_FRAMETYPE_VARIO = 0x07,
    CRSF_FRAMETYPE_BATTERY_SENSOR = 0x08,
    CRSF_FRAMETYPE_BARO_ALTITUDE = 0x09,
    CRSF_FRAMETYPE_HEARTBEAT = 0x0B,
    CRSF_FRAMETYPE_VTX = 0x0F,
    CRSF_FRAMETYPE_VTX_TELEM = 0x10,
    CRSF_FRAMETYPE_LINK_STATISTICS = 0x14,
    CRSF_FRAMETYPE_RC_CHANNELS_PACKED = 0x16,
    CRSF_FRAMETYPE_ATTITUDE = 0x1E,
    CRSF_FRAMETYPE_FLIGHT_MODE = 0x21,
    CRSF_FRAMETYPE_DEVICE_PING = 0x28,
    CRSF_FRAMETYPE_DEVICE_INFO = 0x29,
    CRSF_FRAMETYPE_PARAMETER_SETTINGS_ENTRY = 0x2B,
    CRSF_FRAMETYPE_PARAMETER_READ = 0x2C,
    CRSF_FRAMETYPE_PARAMETER_WRITE = 0x2D,
    CRSF_FRAMETYPE_ELRS_STATUS = 0x2E,
    CRSF_FRAMETYPE_COMMAND = 0x32,
    CRSF_FRAMETYPE_RADIO_ID = 0x3A,
    CRSF_FRAMETYPE_KISS_REQ = 0x78,
    CRSF_FRAMETYPE_KISS_RESP = 0x79,
    CRSF_FRAMETYPE_MSP_REQ = 0x7A,
    CRSF_FRAMETYPE_MSP_RESP = 0x7B,
    CRSF_FRAMETYPE_MSP_WRITE = 0x7C,
    CRSF_FRAMETYPE_ARDUPILOT_RESP = 0x80
} crsf_frame_type_e;

/* CRSF命令定义 */
typedef enum {
    CRSF_COMMAND_SUBCMD = 0x10
} crsf_command_e;

typedef enum {
    CRSF_COMMAND_MODEL_SELECT_ID = 0x05
} crsf_subcommand_e;

/* CRSF参数类型定义 */
typedef enum {
    CRSF_UINT8 = 0,
    CRSF_INT8 = 1,
    CRSF_UINT16 = 2,
    CRSF_INT16 = 3,
    CRSF_UINT32 = 4,
    CRSF_INT32 = 5,
    CRSF_UINT64 = 6,
    CRSF_INT64 = 7,
    CRSF_FLOAT = 8,
    CRSF_TEXT_SELECTION = 9,
    CRSF_STRING = 10,
    CRSF_FOLDER = 11,
    CRSF_INFO = 12,
    CRSF_COMMAND = 13,
    CRSF_VTX = 15,
    CRSF_OUT_OF_RANGE = 127
} crsf_value_type_e;

/* CRSF通道定义 */
#define CRSF_NUM_CHANNELS               16
#define CRSF_CHANNEL_VALUE_MIN          172
#define CRSF_CHANNEL_VALUE_1000         191
#define CRSF_CHANNEL_VALUE_MID          992
#define CRSF_CHANNEL_VALUE_2000         1792
#define CRSF_CHANNEL_VALUE_MAX          1811

/* CRSF时序定义 */
#define CRSF_TIME_NEEDED_PER_FRAME_US   1100
#define CRSF_TIME_BETWEEN_FRAMES_US     6667    // 150Hz
#define CRSF_FRAME_PERIOD_MIN           4000    // 250Hz
#define CRSF_FRAME_PERIOD_MAX           20000   // 50Hz

/* CRSF数据结构 */
#pragma pack(push, 1)

typedef struct {
    uint8_t device_addr;
    uint8_t frame_size;
    uint8_t type;
} crsf_header_t;

typedef struct {
    uint8_t dest_addr;
    uint8_t orig_addr;
    uint8_t type;
} crsf_ext_header_t;

typedef struct {
    crsf_header_t header;
    uint8_t payload[CRSF_PAYLOAD_SIZE_MAX];
    uint8_t crc;
} crsf_frame_t;

typedef struct {
    uint16_t ch0  : 11;
    uint16_t ch1  : 11;
    uint16_t ch2  : 11;
    uint16_t ch3  : 11;
    uint16_t ch4  : 11;
    uint16_t ch5  : 11;
    uint16_t ch6  : 11;
    uint16_t ch7  : 11;
    uint16_t ch8  : 11;
    uint16_t ch9  : 11;
    uint16_t ch10 : 11;
    uint16_t ch11 : 11;
    uint16_t ch12 : 11;
    uint16_t ch13 : 11;
    uint16_t ch14 : 11;
    uint16_t ch15 : 11;
} crsf_channels_t;

typedef struct {
    uint8_t uplink_RSSI_1;
    uint8_t uplink_RSSI_2;
    uint8_t uplink_Link_quality;
    int8_t uplink_SNR;
    uint8_t active_antenna;
    uint8_t rf_Mode;
    uint8_t uplink_TX_Power;
    uint8_t downlink_RSSI;
    uint8_t downlink_Link_quality;
    int8_t downlink_SNR;
} crsf_link_statistics_t;

typedef struct {
    uint16_t voltage;       // mV * 100
    uint16_t current;       // mA * 100
    uint32_t capacity;      // mAh
    uint8_t remaining;      // %
} crsf_battery_sensor_t;

typedef struct {
    int32_t latitude;       // degree / 10,000,000
    int32_t longitude;      // degree / 10,000,000
    uint16_t groundspeed;   // km/h / 100
    uint16_t heading;       // degree / 100
    uint16_t altitude;      // meter - 1000m offset
    uint8_t satellites;     // count
} crsf_gps_t;

typedef struct {
    int16_t pitch;          // rad / 10000
    int16_t roll;           // rad / 10000
    int16_t yaw;            // rad / 10000
} crsf_attitude_t;

typedef struct {
    char flight_mode[16];
} crsf_flight_mode_t;

#pragma pack(pop)

/* CRSF设备信息 */
#define CRSF_MAX_NAME_LEN               16
#define CRSF_MAX_PARAMS                 100
#define CRSF_MAX_DEVICES                4

/* CRC多项式 */
#define CRSF_CRC_POLY                   0xD5

/* 函数声明 */
uint8_t crsf_crc8(const uint8_t *ptr, uint8_t len);
uint8_t crsf_crc8_ba(const uint8_t *ptr, uint8_t len);

#ifdef __cplusplus
}
#endif

#endif /* __CRSF_PROTOCOL_H */
