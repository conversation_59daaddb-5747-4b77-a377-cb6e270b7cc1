/**
 * @file mixer.c
 * @brief 混音器模块实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "mixer.h"
#include "adc_input.h"
#include "eeprom.h"

/* 全局变量定义 */
mixer_config_t mixer_config;
uint16_t mixer_inputs[MIXER_INPUT_COUNT];
uint16_t mixer_outputs[MIXER_OUTPUT_COUNT];

/* 私有变量 */
static bool mixer_initialized = false;
static bool failsafe_active = false;

/**
 * @brief 混音器初始化
 */
error_code_t MIXER_Init(void)
{
    if (mixer_initialized) {
        return ERR_OK;
    }

    /* 初始化输入输出数组 */
    memset(mixer_inputs, 0, sizeof(mixer_inputs));
    memset(mixer_outputs, 0, sizeof(mixer_outputs));

    /* 加载配置 */
    if (MIXER_LoadConfig() != ERR_OK) {
        /* 加载失败，使用默认配置 */
        MIXER_ResetConfig();
    }

    mixer_initialized = true;
    return ERR_OK;
}

/**
 * @brief 重置为默认配置
 */
void MIXER_ResetConfig(void)
{
    /* 清空配置 */
    memset(&mixer_config, 0, sizeof(mixer_config));

    /* 设置默认直通映射 */
    for (uint8_t i = 0; i < MIXER_OUTPUT_COUNT && i < MIXER_INPUT_COUNT; i++) {
        mixer_config.channels[i].rules[i].input = i;
        mixer_config.channels[i].rules[i].weight = MIXER_DEFAULT_WEIGHT;
        mixer_config.channels[i].rules[i].offset = MIXER_DEFAULT_OFFSET;
        mixer_config.channels[i].rules[i].enabled = true;
        
        mixer_config.channels[i].min_limit = MIXER_DEFAULT_MIN;
        mixer_config.channels[i].max_limit = MIXER_DEFAULT_MAX;
        mixer_config.channels[i].center_trim = MIXER_DEFAULT_TRIM;
        mixer_config.channels[i].reversed = false;
        mixer_config.channels[i].enabled = true;
    }

    /* 设置失控保护值 */
    mixer_config.failsafe_enabled = true;
    for (uint8_t i = 0; i < MIXER_OUTPUT_COUNT; i++) {
        if (i == MIXER_OUTPUT_CH3) {  // 油门通道
            mixer_config.failsafe_values[i] = MIXER_CHAN_MIN;  // 油门最小
        } else {
            mixer_config.failsafe_values[i] = MIXER_CHAN_CENTER;  // 其他通道居中
        }
    }
}

/**
 * @brief 更新输入值
 */
void MIXER_UpdateInputs(void)
{
    /* 从ADC获取原始值并转换为混音器输入 */
    mixer_inputs[MIXER_INPUT_CH1] = MIXER_ScaleInput(ADC_Input_GetCalibratedValue(ADC_CH1));
    mixer_inputs[MIXER_INPUT_CH2] = MIXER_ScaleInput(ADC_Input_GetCalibratedValue(ADC_CH2));
    mixer_inputs[MIXER_INPUT_CH3] = MIXER_ScaleInput(ADC_Input_GetCalibratedValue(ADC_CH3));
    mixer_inputs[MIXER_INPUT_CH4] = MIXER_ScaleInput(ADC_Input_GetCalibratedValue(ADC_CH4));
    mixer_inputs[MIXER_INPUT_SWA] = MIXER_ScaleInput(ADC_Input_GetCalibratedValue(ADC_SWA));
    mixer_inputs[MIXER_INPUT_SWB] = MIXER_ScaleInput(ADC_Input_GetCalibratedValue(ADC_SWB));
    mixer_inputs[MIXER_INPUT_VRA] = MIXER_ScaleInput(ADC_Input_GetCalibratedValue(ADC_VRA));
    mixer_inputs[MIXER_INPUT_VRB] = MIXER_ScaleInput(ADC_Input_GetCalibratedValue(ADC_VRB));
}

/**
 * @brief 计算混音器输出
 */
void MIXER_CalcChannels(void)
{
    if (!mixer_initialized) {
        return;
    }

    /* 如果失控保护激活，使用失控保护值 */
    if (failsafe_active) {
        for (uint8_t i = 0; i < MIXER_OUTPUT_COUNT; i++) {
            mixer_outputs[i] = mixer_config.failsafe_values[i];
        }
        return;
    }

    /* 计算每个输出通道 */
    for (uint8_t out = 0; out < MIXER_OUTPUT_COUNT; out++) {
        mixer_channel_t* channel = &mixer_config.channels[out];
        
        if (!channel->enabled) {
            mixer_outputs[out] = MIXER_CHAN_CENTER;
            continue;
        }

        int32_t result = 0;
        bool has_input = false;

        /* 应用混音规则 */
        for (uint8_t in = 0; in < MIXER_INPUT_COUNT; in++) {
            mixer_rule_t* rule = &channel->rules[in];
            
            if (!rule->enabled || rule->weight == 0) {
                continue;
            }

            /* 计算加权输入 */
            int32_t input_value = mixer_inputs[in] - MIXER_CHAN_CENTER;
            int32_t weighted = (input_value * rule->weight) / 100;
            result += weighted + rule->offset;
            has_input = true;
        }

        /* 如果没有输入，使用中心值 */
        if (!has_input) {
            result = 0;
        }

        /* 应用中心微调 */
        result += channel->center_trim;

        /* 应用限制 */
        result = MIXER_ApplyLimits(result, 
                                  (channel->min_limit * MIXER_CHAN_RANGE) / 100,
                                  (channel->max_limit * MIXER_CHAN_RANGE) / 100);

        /* 转换回CRSF范围并应用反向 */
        int32_t output = MIXER_CHAN_CENTER + result;
        
        if (channel->reversed) {
            output = MIXER_CHAN_CENTER - result;
        }

        /* 确保在有效范围内 */
        output = MIXER_ApplyLimits(output, MIXER_CHAN_MIN, MIXER_CHAN_MAX);
        
        mixer_outputs[out] = (uint16_t)output;
    }
}

/**
 * @brief 缩放ADC输入到混音器范围
 */
int16_t MIXER_ScaleInput(uint16_t adc_value)
{
    /* ADC值 (0-4095) 转换为 CRSF值 (172-1811) */
    int32_t scaled = MIXER_CHAN_MIN + ((int32_t)adc_value * (MIXER_CHAN_MAX - MIXER_CHAN_MIN)) / ADC_RESOLUTION;
    return (int16_t)MIXER_ApplyLimits(scaled, MIXER_CHAN_MIN, MIXER_CHAN_MAX);
}

/**
 * @brief 缩放混音器输出到CRSF范围
 */
uint16_t MIXER_ScaleOutput(int16_t mixer_value)
{
    return (uint16_t)MIXER_ApplyLimits(mixer_value, MIXER_CHAN_MIN, MIXER_CHAN_MAX);
}

/**
 * @brief 应用限制
 */
int16_t MIXER_ApplyLimits(int16_t value, int16_t min_limit, int16_t max_limit)
{
    if (value < min_limit) {
        return min_limit;
    }
    if (value > max_limit) {
        return max_limit;
    }
    return value;
}

/**
 * @brief 设置输入值
 */
void MIXER_SetInput(mixer_input_t input, uint16_t value)
{
    if (input < MIXER_INPUT_COUNT) {
        mixer_inputs[input] = value;
    }
}

/**
 * @brief 获取输出值
 */
uint16_t MIXER_GetOutput(mixer_output_t output)
{
    if (output < MIXER_OUTPUT_COUNT) {
        return mixer_outputs[output];
    }
    return MIXER_CHAN_CENTER;
}

/**
 * @brief 设置通道混音规则
 */
void MIXER_SetChannelRule(mixer_output_t output, mixer_input_t input, int16_t weight, int16_t offset)
{
    if (output >= MIXER_OUTPUT_COUNT || input >= MIXER_INPUT_COUNT) {
        return;
    }

    mixer_rule_t* rule = &mixer_config.channels[output].rules[input];
    rule->input = input;
    rule->weight = weight;
    rule->offset = offset;
    rule->enabled = (weight != 0);
}

/**
 * @brief 启用/禁用通道
 */
void MIXER_EnableChannel(mixer_output_t output, bool enabled)
{
    if (output < MIXER_OUTPUT_COUNT) {
        mixer_config.channels[output].enabled = enabled;
    }
}

/**
 * @brief 设置通道限制
 */
void MIXER_SetChannelLimits(mixer_output_t output, int16_t min_limit, int16_t max_limit)
{
    if (output < MIXER_OUTPUT_COUNT) {
        mixer_config.channels[output].min_limit = min_limit;
        mixer_config.channels[output].max_limit = max_limit;
    }
}

/**
 * @brief 设置通道微调
 */
void MIXER_SetChannelTrim(mixer_output_t output, int16_t trim)
{
    if (output < MIXER_OUTPUT_COUNT) {
        mixer_config.channels[output].center_trim = trim;
    }
}

/**
 * @brief 设置通道反向
 */
void MIXER_SetChannelReverse(mixer_output_t output, bool reversed)
{
    if (output < MIXER_OUTPUT_COUNT) {
        mixer_config.channels[output].reversed = reversed;
    }
}

/**
 * @brief 启用失控保护
 */
void MIXER_EnableFailsafe(bool enabled)
{
    mixer_config.failsafe_enabled = enabled;
}

/**
 * @brief 设置失控保护值
 */
void MIXER_SetFailsafeValue(mixer_output_t output, uint16_t value)
{
    if (output < MIXER_OUTPUT_COUNT) {
        mixer_config.failsafe_values[output] = value;
    }
}

/**
 * @brief 激活失控保护
 */
void MIXER_ActivateFailsafe(void)
{
    if (mixer_config.failsafe_enabled) {
        failsafe_active = true;
    }
}

/**
 * @brief 取消失控保护
 */
void MIXER_DeactivateFailsafe(void)
{
    failsafe_active = false;
}

/**
 * @brief 检查失控保护是否激活
 */
bool MIXER_IsFailsafeActive(void)
{
    return failsafe_active;
}

/**
 * @brief 加载配置
 */
error_code_t MIXER_LoadConfig(void)
{
    /* 从EEPROM加载混音器配置 */
    return EEPROM_ReadArea(EEPROM_AREA_SYSTEM, 
                          sizeof(system_config_t), 
                          (uint8_t*)&mixer_config, 
                          sizeof(mixer_config));
}

/**
 * @brief 保存配置
 */
error_code_t MIXER_SaveConfig(void)
{
    /* 保存混音器配置到EEPROM */
    return EEPROM_WriteArea(EEPROM_AREA_SYSTEM, 
                           sizeof(system_config_t), 
                           (uint8_t*)&mixer_config, 
                           sizeof(mixer_config));
}
