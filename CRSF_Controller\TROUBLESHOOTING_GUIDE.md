# CRSF Controller 故障排除指南

## 🚨 **紧急故障处理**

### 系统无法启动
**症状**: 上电后无任何反应，LED不亮，显示屏无显示

**可能原因**:
1. 电源问题 - 电池电量不足或电源线路故障
2. 硬件故障 - MCU损坏或关键元件故障
3. 固件损坏 - Flash内容被破坏

**解决步骤**:
```
1. 检查电源
   - 测量电池电压 (应>3.0V)
   - 检查电源开关和线路
   - 尝试外接电源供电

2. 硬件检查
   - 检查MCU供电 (3.3V)
   - 检查晶振工作 (8MHz)
   - 检查复位电路

3. 固件恢复
   - 使用ST-Link连接SWD接口
   - 重新烧录引导程序
   - 通过引导程序升级固件
```

### 系统频繁重启
**症状**: 系统启动后不久自动重启，循环重启

**可能原因**:
1. 看门狗超时 - 主循环阻塞或死锁
2. 硬件异常 - 电源不稳定或干扰
3. 软件异常 - 内存越界或栈溢出

**解决步骤**:
```
1. 禁用看门狗测试
   - 注释掉看门狗初始化代码
   - 重新编译烧录测试

2. 检查电源稳定性
   - 测量电源纹波 (<100mV)
   - 检查去耦电容
   - 添加电源滤波

3. 软件调试
   - 使用调试器单步执行
   - 检查栈使用情况
   - 添加异常处理代码
```

## 🔧 **硬件故障诊断**

### ADC输入异常
**症状**: 摇杆或开关读数异常，数值跳动或固定不变

**诊断步骤**:
```c
// ADC自检代码
bool ADC_SelfTest(void)
{
    // 1. 检查ADC基准电压
    uint16_t vref = ADC_GetValue(ADC_CHANNEL_VREFINT);
    if (vref < 1500 || vref > 1700) {
        USB_CDC_Printf("VREF error: %d\n", vref);
        return false;
    }
    
    // 2. 检查各通道是否有响应
    for (int i = 0; i < 8; i++) {
        uint16_t value = ADC_GetValue(i);
        if (value == 0 || value == 4095) {
            USB_CDC_Printf("ADC[%d] stuck: %d\n", i, value);
            return false;
        }
    }
    
    // 3. 检查噪声水平
    uint16_t noise = ADC_GetNoiseLevel(0);
    if (noise > 50) {
        USB_CDC_Printf("ADC noise too high: %d\n", noise);
        return false;
    }
    
    return true;
}
```

**常见问题**:
| 现象 | 原因 | 解决方法 |
|------|------|----------|
| 读数为0 | 输入短路到地 | 检查连接线路 |
| 读数为4095 | 输入悬空或短路到VCC | 检查输入电路 |
| 数值跳动 | 噪声干扰 | 添加滤波电容 |
| 响应迟缓 | 输入阻抗过高 | 降低输入阻抗 |

### UART通信故障
**症状**: CRSF数据无法发送或接收异常

**诊断步骤**:
```c
// UART自检代码
bool UART_SelfTest(void)
{
    // 1. 检查UART配置
    if (huart1.Init.BaudRate != 420000) {
        USB_CDC_Printf("UART baudrate error: %d\n", huart1.Init.BaudRate);
        return false;
    }
    
    // 2. 发送测试数据
    uint8_t test_data[] = {0xC8, 0x18, 0x16, 0x00, 0x01, 0x02};
    HAL_StatusTypeDef status = HAL_UART_Transmit(&huart1, test_data, 6, 100);
    if (status != HAL_OK) {
        USB_CDC_Printf("UART TX error: %d\n", status);
        return false;
    }
    
    // 3. 检查DMA状态
    if (huart1.hdmatx->State != HAL_DMA_STATE_READY) {
        USB_CDC_Printf("UART DMA error\n");
        return false;
    }
    
    return true;
}
```

**常见问题**:
| 现象 | 原因 | 解决方法 |
|------|------|----------|
| 无数据输出 | TX引脚配置错误 | 检查GPIO配置 |
| 数据乱码 | 波特率不匹配 | 确认420K波特率 |
| 发送卡死 | DMA配置错误 | 检查DMA通道 |
| 接收丢包 | 缓冲区溢出 | 增大接收缓冲区 |

### I2C通信故障
**症状**: OLED显示异常或无显示

**诊断步骤**:
```c
// I2C自检代码
bool I2C_SelfTest(void)
{
    // 1. 检查I2C时钟
    if (hi2c2.Init.Timing != 0x2000090E) {
        USB_CDC_Printf("I2C timing error\n");
        return false;
    }
    
    // 2. 扫描I2C设备
    for (uint8_t addr = 0x08; addr < 0x78; addr++) {
        if (HAL_I2C_IsDeviceReady(&hi2c2, addr << 1, 1, 10) == HAL_OK) {
            USB_CDC_Printf("I2C device found: 0x%02X\n", addr);
        }
    }
    
    // 3. 测试OLED通信
    uint8_t cmd = 0xAE;  // Display OFF command
    HAL_StatusTypeDef status = HAL_I2C_Mem_Write(&hi2c2, 0x3C << 1, 0x00, 1, &cmd, 1, 100);
    if (status != HAL_OK) {
        USB_CDC_Printf("OLED communication error: %d\n", status);
        return false;
    }
    
    return true;
}
```

### USB连接问题
**症状**: PC无法识别USB设备

**诊断步骤**:
```c
// USB自检代码
bool USB_SelfTest(void)
{
    // 1. 检查USB时钟
    RCC_OscInitTypeDef osc_init;
    HAL_RCC_GetOscConfig(&osc_init);
    if (osc_init.HSI48State != RCC_HSI48_ON) {
        USB_CDC_Printf("HSI48 not enabled\n");
        return false;
    }
    
    // 2. 检查USB状态
    if (hUsbDeviceFS.dev_state != USBD_STATE_CONFIGURED) {
        USB_CDC_Printf("USB not configured: %d\n", hUsbDeviceFS.dev_state);
        return false;
    }
    
    // 3. 测试数据传输
    uint8_t test_msg[] = "USB Test OK\n";
    if (CDC_Transmit_FS(test_msg, sizeof(test_msg)) != USBD_OK) {
        USB_CDC_Printf("USB TX error\n");
        return false;
    }
    
    return true;
}
```

## 🔍 **软件故障诊断**

### CRSF时序异常
**症状**: CRSF输出不稳定，周期不准确

**诊断代码**:
```c
// CRSF时序监控
void CRSF_TimingMonitor(void)
{
    static uint32_t last_time = 0;
    static uint32_t error_count = 0;
    
    uint32_t current_time = micros();
    if (last_time > 0) {
        uint32_t period = current_time - last_time;
        
        // 检查4ms ±50μs精度
        if (abs(period - 4000) > 50) {
            error_count++;
            USB_CDC_Printf("CRSF timing error: %dus (errors: %d)\n", 
                          period, error_count);
            
            // 如果错误过多，重新初始化定时器
            if (error_count > 10) {
                TIM2_Reinit();
                error_count = 0;
            }
        }
    }
    last_time = current_time;
}
```

### 内存泄漏检测
**症状**: 系统运行一段时间后变慢或崩溃

**诊断代码**:
```c
// 内存使用监控
void Memory_Monitor(void)
{
    static uint32_t max_stack_used = 0;
    
    // 检查栈使用情况
    extern uint32_t _estack;
    uint32_t stack_used = (uint32_t)&_estack - (uint32_t)__get_MSP();
    
    if (stack_used > max_stack_used) {
        max_stack_used = stack_used;
        USB_CDC_Printf("Max stack used: %d bytes\n", max_stack_used);
    }
    
    // 栈溢出检查
    if (stack_used > 14000) {  // 16KB - 2KB安全余量
        USB_CDC_Printf("Stack overflow warning!\n");
        SOUND_PlayError();
    }
    
    // 检查堆使用情况 (如果使用malloc)
    #ifdef USE_MALLOC
    struct mallinfo mi = mallinfo();
    USB_CDC_Printf("Heap used: %d bytes\n", mi.uordblks);
    #endif
}
```

### 中断优先级冲突
**症状**: 系统响应异常，某些功能不工作

**诊断代码**:
```c
// 中断优先级检查
void Interrupt_PriorityCheck(void)
{
    USB_CDC_Printf("Interrupt Priorities:\n");
    USB_CDC_Printf("TIM2: %d\n", NVIC_GetPriority(TIM2_IRQn));
    USB_CDC_Printf("EXTI1: %d\n", NVIC_GetPriority(EXTI1_IRQn));
    USB_CDC_Printf("EXTI4: %d\n", NVIC_GetPriority(EXTI4_15_IRQn));
    USB_CDC_Printf("SysTick: %d\n", NVIC_GetPriority(SysTick_IRQn));
    
    // 检查优先级配置是否正确
    if (NVIC_GetPriority(TIM2_IRQn) > NVIC_GetPriority(EXTI1_IRQn)) {
        USB_CDC_Printf("Priority error: TIM2 should be higher than EXTI1\n");
    }
}
```

## 🛠️ **维护和预防**

### 定期自检
```c
// 系统健康检查
void System_HealthCheck(void)
{
    bool all_ok = true;
    
    // 硬件自检
    if (!ADC_SelfTest()) all_ok = false;
    if (!UART_SelfTest()) all_ok = false;
    if (!I2C_SelfTest()) all_ok = false;
    if (!USB_SelfTest()) all_ok = false;
    
    // 软件自检
    Memory_Monitor();
    CRSF_TimingMonitor();
    Interrupt_PriorityCheck();
    
    // 性能监控
    Performance_Monitor();
    
    if (all_ok) {
        USB_CDC_Printf("System health check: PASS\n");
        SOUND_PlaySuccess();
    } else {
        USB_CDC_Printf("System health check: FAIL\n");
        SOUND_PlayError();
    }
}
```

### 数据备份
```c
// 重要数据备份
void System_Backup(void)
{
    // 备份校准数据
    calibration_data_t cal_backup;
    EEPROM_ReadCalibration(&cal_backup);
    
    // 备份配置数据
    system_config_t config_backup;
    EEPROM_ReadConfig(&config_backup);
    
    // 备份到多个位置
    EEPROM_WriteBackup(BACKUP_SLOT_1, &cal_backup, &config_backup);
    EEPROM_WriteBackup(BACKUP_SLOT_2, &cal_backup, &config_backup);
    
    USB_CDC_Printf("System backup completed\n");
}
```

### 固件验证
```c
// 固件完整性检查
bool Firmware_Verify(void)
{
    // 计算固件CRC32
    uint32_t calc_crc = HAL_CRC_Calculate(&hcrc, 
                                         (uint32_t*)APPLICATION_START_ADDR, 
                                         APPLICATION_SIZE / 4);
    
    // 读取存储的CRC32
    uint32_t stored_crc = *(uint32_t*)(APPLICATION_START_ADDR + 0x108);
    
    if (calc_crc != stored_crc) {
        USB_CDC_Printf("Firmware CRC error: calc=0x%08X, stored=0x%08X\n", 
                      calc_crc, stored_crc);
        return false;
    }
    
    USB_CDC_Printf("Firmware verification: PASS\n");
    return true;
}
```

## 📞 **技术支持**

### 日志收集
```c
// 系统日志导出
void System_ExportLogs(void)
{
    USB_CDC_Printf("=== CRSF Controller System Log ===\n");
    USB_CDC_Printf("Firmware Version: %s\n", FIRMWARE_VERSION);
    USB_CDC_Printf("Build Date: %s %s\n", __DATE__, __TIME__);
    USB_CDC_Printf("System Uptime: %d seconds\n", HAL_GetTick() / 1000);
    
    // 硬件信息
    USB_CDC_Printf("\n--- Hardware Info ---\n");
    USB_CDC_Printf("MCU: STM32F072CBT6\n");
    USB_CDC_Printf("Flash Size: %dKB\n", FLASH_SIZE);
    USB_CDC_Printf("RAM Size: 16KB\n");
    
    // 运行状态
    USB_CDC_Printf("\n--- Runtime Status ---\n");
    USB_CDC_Printf("CPU Usage: %d%%\n", cpu_usage);
    USB_CDC_Printf("Free RAM: %d bytes\n", get_free_ram());
    USB_CDC_Printf("CRSF Errors: %d\n", crsf_error_count);
    USB_CDC_Printf("Watchdog Resets: %d\n", watchdog_reset_count);
    
    // 配置信息
    USB_CDC_Printf("\n--- Configuration ---\n");
    USB_CDC_Printf("Model: %s\n", current_model.name);
    USB_CDC_Printf("Calibration Date: %s\n", calibration_date);
    
    USB_CDC_Printf("=== End of Log ===\n");
}
```

### 远程诊断
```c
// 远程诊断命令处理
void Remote_Diagnostic(const char* command)
{
    if (strcmp(command, "status") == 0) {
        System_HealthCheck();
    } else if (strcmp(command, "logs") == 0) {
        System_ExportLogs();
    } else if (strcmp(command, "backup") == 0) {
        System_Backup();
    } else if (strcmp(command, "verify") == 0) {
        Firmware_Verify();
    } else if (strcmp(command, "reset") == 0) {
        USB_CDC_Printf("System reset in 3 seconds...\n");
        HAL_Delay(3000);
        NVIC_SystemReset();
    } else {
        USB_CDC_Printf("Unknown command: %s\n", command);
        USB_CDC_Printf("Available commands: status, logs, backup, verify, reset\n");
    }
}
```

## 🎯 **故障排除总结**

### 故障分类
1. **硬件故障**: 元件损坏、连接问题、电源异常
2. **软件故障**: 代码错误、配置问题、时序异常
3. **系统故障**: 内存问题、中断冲突、性能瓶颈

### 诊断流程
1. **现象观察**: 详细记录故障现象和触发条件
2. **硬件检查**: 使用万用表和示波器检查硬件
3. **软件调试**: 使用调试器和日志分析软件问题
4. **系统测试**: 运行自检程序验证修复效果

### 预防措施
1. **定期维护**: 每月进行系统健康检查
2. **数据备份**: 定期备份重要配置和校准数据
3. **固件更新**: 及时更新到最新稳定版本
4. **环境保护**: 避免在恶劣环境中使用

这份故障排除指南帮助用户快速定位和解决CRSF Controller的各种问题！
