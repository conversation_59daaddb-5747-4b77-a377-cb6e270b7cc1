# 看门狗(IWDG)配置指南

## 🎯 配置目标
为CRSF_Controller配置独立看门狗，实现2秒超时保护，防止系统死锁。

## ⚙️ STM32CubeMX配置步骤

### 1. 启用IWDG
```
Pinout & Configuration → System Core → IWDG
☑ Activated (勾选激活)
```

### 2. 参数配置
```
Parameter Settings:
- Prescaler divider: 32 (32分频)
- Down-counter reload value: 2312
- Window value: 4095 (默认，不使用窗口功能)
```

### 3. 超时时间计算
```
LSI频率 = 37kHz (STM32F072内部低速振荡器)
超时时间 = (Reload × Prescaler) / LSI频率
超时时间 = (2312 × 32) / 37000 ≈ 2.0秒 ✅
```

## 📋 当前IOC配置

### ✅ 已修正的配置：
```ini
IWDG.IPParameters=Prescaler,Reload
IWDG.Prescaler=IWDG_PRESCALER_32    # 32分频
IWDG.Reload=2312                    # 重载值2312
```

### 🔧 配置验证：
- **超时时间**: 2.0秒 ✅ (匹配代码中的WATCHDOG_TIMEOUT_MS)
- **分频系数**: 32 ✅ (平衡精度和范围)
- **重载值**: 2312 ✅ (计算得出)

## 💻 代码中的看门狗使用

### 1. 初始化 (自动生成)
```c
// 在main.c中自动生成
void MX_IWDG_Init(void)
{
  hiwdg.Instance = IWDG;
  hiwdg.Init.Prescaler = IWDG_PRESCALER_32;
  hiwdg.Init.Reload = 2312;
  if (HAL_IWDG_Init(&hiwdg) != HAL_OK)
  {
    Error_Handler();
  }
}
```

### 2. 软件看门狗管理
```c
// 在clock_system.c中实现
void CLOCK_ResetWatchdog(void)
{
    wdg_time = msecs;  // 重置软件看门狗时间
}

bool CLOCK_IsWatchdogExpired(void)
{
    return (msecs - wdg_time) > WATCHDOG_TIMEOUT_MS;  // 2000ms
}
```

### 3. 硬件看门狗喂狗
```c
// 在主循环中定期调用
void HAL_IWDG_Refresh(void)
{
    HAL_IWDG_Refresh(&hiwdg);
}
```

### 4. 看门狗监控 (SysTick中断)
```c
void SysTick_Handler(void)
{
    msecs++;
    
    // 检查软件看门狗
    if (msecs - wdg_time > WATCHDOG_TIMEOUT_MS) {
        HAL_NVIC_SetPendingIRQ(EXTI2_IRQn);  // 触发看门狗处理
        return;
    }
    
    // 其他处理...
}
```

## 🔄 看门狗工作流程

### 1. 正常工作流程
```
1. 系统启动 → MX_IWDG_Init() → 看门狗开始计数
2. 主循环 → CLOCK_ResetWatchdog() → 重置软件看门狗
3. 定期 → HAL_IWDG_Refresh() → 喂硬件看门狗
4. 循环执行...
```

### 2. 异常处理流程
```
1. 系统死锁/卡死
2. 超过2秒未喂狗
3. 硬件看门狗触发复位
4. 系统重启 → 恢复正常运行
```

## 🛡️ 双重保护机制

### 软件看门狗 (2秒)
- **目的**: 早期检测系统异常
- **触发**: EXTI2中断处理
- **优势**: 可以记录错误信息

### 硬件看门狗 (2秒)
- **目的**: 最终保护，强制复位
- **触发**: 硬件自动复位
- **优势**: 完全独立，不受软件影响

## 📊 不同超时时间配置

### 1秒超时 (快速响应)
```
Prescaler: 32
Reload: 1156
超时时间 = (1156 × 32) / 37000 ≈ 1.0秒
```

### 2秒超时 (当前配置)
```
Prescaler: 32
Reload: 2312
超时时间 = (2312 × 32) / 37000 ≈ 2.0秒 ✅
```

### 5秒超时 (宽松模式)
```
Prescaler: 64
Reload: 2890
超时时间 = (2890 × 64) / 37000 ≈ 5.0秒
```

## 🔍 调试和监控

### 1. 看门狗状态监控
```c
void Watchdog_Status(void)
{
    uint32_t wdg_remaining = WATCHDOG_TIMEOUT_MS - (msecs - wdg_time);
    USB_CDC_Printf("Watchdog: %dms remaining\r\n", wdg_remaining);
}
```

### 2. 复位原因检查
```c
void Check_ResetSource(void)
{
    if (__HAL_RCC_GET_FLAG(RCC_FLAG_IWDGRST)) {
        USB_CDC_Printf("System reset by IWDG\r\n");
        __HAL_RCC_CLEAR_RESET_FLAGS();
    }
}
```

## ✅ 配置验证清单

- ✅ **IWDG已激活**: VP_IWDG_VS_IWDG.Mode=IWDG_Activate
- ✅ **分频系数**: IWDG_PRESCALER_32
- ✅ **重载值**: 2312
- ✅ **超时时间**: 2.0秒 (匹配代码)
- ✅ **初始化函数**: MX_IWDG_Init已包含在生成列表中
- ✅ **软件管理**: clock_system.c中已实现

## 🚨 注意事项

1. **看门狗启动后无法停止**: 一旦启动，只能通过复位停止
2. **必须定期喂狗**: 超过2秒未喂狗将导致系统复位
3. **调试时注意**: 断点调试时看门狗仍在运行
4. **低功耗模式**: 看门狗在STOP模式下暂停，STANDBY模式下复位

---
*配置完成时间: 2025-07-30*
*超时时间: 2.0秒*
*状态: ✅ 已优化*
