/**
 * @file power_management.c
 * @brief 电源管理实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "power_management.h"
#include "hal_drivers.h"
#include "adc_input.h"
#include "buzzer_vibrator.h"
#include "menu_system.h"

/* 全局变量定义 */
power_info_t power_info;
power_config_t power_config;

/* 私有变量 */
static bool power_initialized = false;
static bool shutdown_requested = false;
static uint32_t last_activity_time = 0;
static uint32_t last_low_battery_warning = 0;
static uint32_t charging_start_time = 0;

/* 私有函数声明 */
static void Power_UpdateBatteryStatus(void);
static void Power_UpdateChargeStatus(void);
static void Power_CheckAutoShutdown(void);
static void Power_HandleLowBattery(void);

/**
 * @brief 电源管理初始化
 */
error_code_t Power_Init(void)
{
    if (power_initialized) {
        return ERR_OK;
    }

    /* 初始化电源信息 */
    memset(&power_info, 0, sizeof(power_info));
    power_info.state = POWER_STATE_UNKNOWN;
    power_info.charge_state = CHARGE_STATE_NOT_CHARGING;

    /* 设置默认配置 */
    power_config.low_battery_threshold = DEFAULT_LOW_BATTERY_THRESHOLD;
    power_config.critical_battery_threshold = DEFAULT_CRITICAL_BATTERY_THRESHOLD;
    power_config.full_battery_voltage = DEFAULT_FULL_BATTERY_VOLTAGE;
    power_config.empty_battery_voltage = DEFAULT_EMPTY_BATTERY_VOLTAGE;
    power_config.auto_shutdown_time = DEFAULT_AUTO_SHUTDOWN_TIME;
    power_config.low_battery_warning_interval = DEFAULT_LOW_BATTERY_WARNING_INTERVAL;
    power_config.auto_shutdown_enabled = true;

    /* 初始化时间戳 */
    last_activity_time = millis();
    
    /* 更新初始状态 */
    Power_UpdateStatus();

    power_initialized = true;
    return ERR_OK;
}

/**
 * @brief 更新电源状态
 */
void Power_UpdateStatus(void)
{
    if (!power_initialized) {
        return;
    }

    power_state_t old_state = power_info.state;
    
    /* 更新电池状态 */
    Power_UpdateBatteryStatus();
    
    /* 更新充电状态 */
    Power_UpdateChargeStatus();
    
    /* 检查USB连接 */
    power_info.usb_connected = Power_IsUSBConnected();
    
    /* 检查外部电源连接 */
    power_info.external_connected = Power_IsExternalConnected();
    
    /* 确定电源状态 */
    if (power_info.charging_active) {
        if (power_info.stdby_active) {
            power_info.state = POWER_STATE_CHARGED;
        } else {
            power_info.state = POWER_STATE_CHARGING;
        }
    } else if (power_info.usb_connected || power_info.external_connected) {
        if (power_info.usb_connected) {
            power_info.state = POWER_STATE_USB;
        } else {
            power_info.state = POWER_STATE_EXTERNAL;
        }
    } else {
        if (power_info.battery_voltage < power_config.critical_battery_threshold) {
            power_info.state = POWER_STATE_CRITICAL;
        } else if (power_info.battery_voltage < power_config.low_battery_threshold) {
            power_info.state = POWER_STATE_LOW_BATTERY;
        } else {
            power_info.state = POWER_STATE_BATTERY;
        }
    }
    
    power_info.last_update_time = millis();
    
    /* 状态改变回调 */
    if (old_state != power_info.state) {
        Power_OnStateChanged(old_state, power_info.state);
    }
}

/**
 * @brief 更新电池状态
 */
static void Power_UpdateBatteryStatus(void)
{
    /* 从ADC获取电池电压 */
    uint16_t vbat_adc = ADC_Input_GetCalibratedValue(6);  // PC0 - VBAT
    power_info.battery_voltage = Power_ADCToBatteryVoltage(vbat_adc);
    
    /* 计算电池电量百分比 */
    power_info.battery_percent = Power_VoltageToPercent(power_info.battery_voltage);
    
    /* 更新菜单显示 */
    Menu_SetBatteryLevel(power_info.battery_percent);
}

/**
 * @brief 更新充电状态
 */
static void Power_UpdateChargeStatus(void)
{
    /* 检查STDBY引脚状态 */
    power_info.stdby_active = (HAL_GPIO_ReadPin(STDBY_GPIO_Port, STDBY_Pin) == GPIO_PIN_RESET);
    
    /* 检查外部电源 */
    uint16_t bin_adc = ADC_Input_GetCalibratedValue(7);  // PC1 - BIN
    power_info.external_voltage = Power_ADCToExternalVoltage(bin_adc);
    power_info.external_connected = (power_info.external_voltage > 4000);  // 4V阈值
    
    /* 确定充电状态 */
    if (power_info.external_connected || power_info.usb_connected) {
        if (power_info.stdby_active) {
            power_info.charge_state = CHARGE_STATE_COMPLETE;
            power_info.charging_active = false;
        } else {
            power_info.charge_state = CHARGE_STATE_CHARGING;
            power_info.charging_active = true;
            if (charging_start_time == 0) {
                charging_start_time = millis();
            }
        }
    } else {
        power_info.charge_state = CHARGE_STATE_NOT_CHARGING;
        power_info.charging_active = false;
        charging_start_time = 0;
    }
}

/**
 * @brief ADC值转换为电池电压
 */
uint16_t Power_ADCToBatteryVoltage(uint16_t adc_value)
{
    /* ADC值转换为电压 (mV) */
    uint32_t voltage = (adc_value * ADC_REFERENCE_VOLTAGE) / ADC_RESOLUTION;
    
    /* 考虑分压比 */
    voltage = (uint32_t)(voltage * BATTERY_VOLTAGE_DIVIDER_RATIO);
    
    return (uint16_t)voltage;
}

/**
 * @brief ADC值转换为外部电源电压
 */
uint16_t Power_ADCToExternalVoltage(uint16_t adc_value)
{
    /* ADC值转换为电压 (mV) */
    uint32_t voltage = (adc_value * ADC_REFERENCE_VOLTAGE) / ADC_RESOLUTION;
    
    /* 考虑分压比 */
    voltage = (uint32_t)(voltage * EXTERNAL_VOLTAGE_DIVIDER_RATIO);
    
    return (uint16_t)voltage;
}

/**
 * @brief 电压转换为电量百分比
 */
uint8_t Power_VoltageToPercent(uint16_t voltage)
{
    if (voltage >= power_config.full_battery_voltage) {
        return 100;
    }
    
    if (voltage <= power_config.empty_battery_voltage) {
        return 0;
    }
    
    /* 线性插值计算百分比 */
    uint32_t range = power_config.full_battery_voltage - power_config.empty_battery_voltage;
    uint32_t offset = voltage - power_config.empty_battery_voltage;
    
    return (uint8_t)((offset * 100) / range);
}

/**
 * @brief 检查USB连接状态
 */
bool Power_IsUSBConnected(void)
{
    /* 检查USB VBUS电压 */
    /* 这里可以通过检测USB引脚状态或专用的VBUS检测引脚 */
    /* 简化实现：通过外部电源检测 */
    return power_info.external_connected;
}

/**
 * @brief 检查外部电源连接状态
 */
bool Power_IsExternalConnected(void)
{
    return power_info.external_connected;
}

/**
 * @brief 关机
 */
void Power_Shutdown(void)
{
    /* 播放关机音效 */
    Feedback_Shutdown();
    
    /* 等待音效播放完成 */
    delay_ms(1000);
    
    /* 控制电源开关 */
    HAL_GPIO_WritePin(PWR_OFF_GPIO_Port, PWR_OFF_Pin, GPIO_PIN_SET);
    
    /* 进入低功耗模式 */
    Power_EnterStopMode();
}

/**
 * @brief 电源管理任务
 */
void Power_Task(void* parameters)
{
    (void)parameters;
    
    /* 更新电源状态 */
    Power_UpdateStatus();
    
    /* 检查自动关机 */
    Power_CheckAutoShutdown();
    
    /* 处理低电量 */
    Power_HandleLowBattery();
}

/**
 * @brief 检查自动关机
 */
static void Power_CheckAutoShutdown(void)
{
    if (!power_config.auto_shutdown_enabled) {
        return;
    }
    
    /* 如果正在充电或连接外部电源，不自动关机 */
    if (power_info.charging_active || power_info.external_connected) {
        last_activity_time = millis();
        return;
    }
    
    uint32_t current_time = millis();
    if (current_time - last_activity_time >= power_config.auto_shutdown_time) {
        shutdown_requested = true;
        Power_Shutdown();
    }
}

/**
 * @brief 处理低电量
 */
static void Power_HandleLowBattery(void)
{
    uint32_t current_time = millis();
    
    if (Power_IsLowBattery()) {
        /* 低电量警告 */
        if (current_time - last_low_battery_warning >= power_config.low_battery_warning_interval) {
            Power_OnLowBattery();
            last_low_battery_warning = current_time;
        }
    }
    
    if (Power_IsCriticalBattery()) {
        /* 危险电量，强制关机 */
        Power_OnCriticalBattery();
        Power_Shutdown();
    }
}

/**
 * @brief 进入停止模式
 */
void Power_EnterStopMode(void)
{
    /* 配置唤醒源 */
    HAL_PWR_EnableWakeUpPin(PWR_WAKEUP_PIN1);
    
    /* 进入停止模式 */
    HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
    
    /* 唤醒后重新配置系统时钟 */
    SystemClock_Config();
}

/**
 * @brief 状态查询函数
 */
power_state_t Power_GetState(void) { return power_info.state; }
charge_state_t Power_GetChargeState(void) { return power_info.charge_state; }
uint16_t Power_GetBatteryVoltage(void) { return power_info.battery_voltage; }
uint8_t Power_GetBatteryPercent(void) { return power_info.battery_percent; }
bool Power_IsCharging(void) { return power_info.charging_active; }
bool Power_IsLowBattery(void) { return power_info.battery_voltage < power_config.low_battery_threshold; }
bool Power_IsCriticalBattery(void) { return power_info.battery_voltage < power_config.critical_battery_threshold; }

/**
 * @brief 回调函数 (弱定义，用户可重写)
 */
__weak void Power_OnStateChanged(power_state_t old_state, power_state_t new_state)
{
    (void)old_state;
    (void)new_state;
}

__weak void Power_OnLowBattery(void)
{
    Feedback_Warning();
}

__weak void Power_OnCriticalBattery(void)
{
    Feedback_Error();
}

__weak void Power_OnUSBConnected(void)
{
    Feedback_Success();
}

__weak void Power_OnChargingStarted(void)
{
    Buzzer_PlaySound(SOUND_BEEP_SHORT);
}

__weak void Power_OnChargingComplete(void)
{
    Buzzer_PlaySound(SOUND_SUCCESS);
}
