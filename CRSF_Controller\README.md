# CRSF Controller for STM32F072

基于STM32F072的CRSF协议遥控器，移植自simpleTx_esp32项目。

## 项目特性

- **MCU**: STM32F072CBT6
- **协议**: CRSF (Crossfire) 协议支持
- **通信**: 全双工UART + DMA
- **显示**: SSD1306 OLED (I2C)
- **输入**: 8通道ADC + 6个按键
- **任务调度**: 基于定时器的协作式调度

## 硬件连接

### UART连接 (ELRS模块)
- **UART1**: PA9(TX), PA10(RX)
- **波特率**: 400000 bps
- **模式**: 全双工 + DMA

### I2C连接 (OLED显示)
- **I2C1**: PB6(SCL), PB7(SDA)
- **地址**: 0x3C (SSD1306)

### ADC输入 (摇杆和电位器)
- **ADC1**: 
  - PA0: 副翼 (Aileron)
  - PA1: 升降舵 (Elevator) 
  - PA2: 油门 (Throttle)
  - PA3: 方向舵 (Rudder)
  - PA4: AUX1 (辅助通道1)
  - PA5: AUX2 (辅助通道2)
  - PA6: AUX3 (辅助通道3)
  - PA7: AUX4 (辅助通道4)

### 按键输入
- **PB0**: 确定键 (ENTER)
- **PB1**: 返回键 (BACK)
- **PB2**: 上键 (UP)
- **PB3**: 下键 (DOWN)
- **PB4**: 左键 (LEFT)
- **PB5**: 右键 (RIGHT)

### 定时器配置
- **TIM2**: 微秒级时基 (1MHz)
- **TIM6**: 任务调度 (1kHz)
- **TIM7**: ADC触发 (100Hz)

## 软件架构

### 任务调度系统
采用协作式任务调度，替代ESP32的FreeRTOS：
- **主任务**: CRSF通信和数据处理
- **UI任务**: 按键处理和OLED显示
- **ADC任务**: 模拟输入采集

### CRSF协议栈
完整实现CRSF协议：
- 通道数据发送 (16通道，11位精度)
- 遥测数据接收
- ELRS参数配置
- 设备发现和状态监控

### 内存管理
- 静态内存分配
- DMA缓冲区管理
- 参数缓存优化

## 编译说明

### 开发环境要求
- **编译器**: ARM GCC (推荐版本 10.3 或更高)
- **构建工具**: GNU Make
- **调试器**: ST-Link v2/v3
- **IDE**: STM32CubeIDE, Keil MDK, 或 VS Code (可选)

### 依赖库
- STM32F0xx HAL Driver (已包含在项目中)
- CMSIS (已包含在项目中)

### 编译步骤

#### 方法1: 使用Makefile (推荐)
```bash
# 克隆或下载项目
cd CRSF_Controller

# 编译项目
make all

# 清理编译文件
make clean

# 生成的文件在 build/ 目录下：
# - CRSF_Controller.elf (ELF可执行文件)
# - CRSF_Controller.hex (Intel HEX格式)
# - CRSF_Controller.bin (二进制格式)
```

#### 方法2: 使用STM32CubeIDE
1. 打开STM32CubeIDE
2. 选择 File -> Import -> Existing Projects into Workspace
3. 选择CRSF_Controller文件夹
4. 配置目标MCU为STM32F072CBT6
5. 点击Build按钮编译

#### 方法3: 使用Keil MDK
1. 打开Keil MDK
2. 创建新项目，选择STM32F072CBT6
3. 添加所有源文件和头文件路径
4. 配置编译选项
5. 编译生成.hex文件

### 下载程序
```bash
# 使用ST-Link工具下载
st-flash write build/CRSF_Controller.bin 0x8000000

# 或使用OpenOCD
openocd -f interface/stlink.cfg -f target/stm32f0x.cfg -c "program build/CRSF_Controller.elf verify reset exit"
```

### 配置选项
在 `Inc/config.h` 中可配置：
- `DEBUG_ENABLED`: 调试输出开关 (0/1)
- `ADC_SAMPLE_RATE`: ADC采样率 (Hz)
- `CRSF_UPDATE_RATE`: CRSF更新频率 (Hz)
- `BUTTON_DEBOUNCE_MS`: 按键去抖时间 (ms)
- `OLED_I2C_ADDRESS`: OLED I2C地址
- `CRSF_UART_BAUDRATE`: CRSF串口波特率

### 编译选项
在Makefile中可调整：
- `DEBUG = 1`: 启用调试信息
- `OPT = -Og`: 优化级别 (-O0, -O1, -O2, -O3, -Os, -Og)

## 使用说明

### 开机流程
1. 上电后系统自动初始化
2. OLED显示启动画面 "CRSF Controller STM32F072 v1.0"
3. 自动搜索ELRS TX模块
4. 检测RX模块连接状态
5. 进入主界面显示系统状态

### 按键操作说明
- **确定键 (ENTER)**: 进入菜单/确认选择/确认编辑
- **返回键 (BACK)**: 返回上级菜单/取消操作
- **上键 (UP)**: 向上导航/增加数值
- **下键 (DOWN)**: 向下导航/减少数值
- **左键 (LEFT)**: 返回上级/减少数值
- **右键 (RIGHT)**: 进入子菜单/增加数值

### 主界面信息显示
- **TX模块状态**: 显示TX模块名称和连接状态
- **RF模式**: 当前射频模式 (包率)
- **链路质量**: LQ百分比和RSSI值
- **包统计**: 好包/坏包计数
- **RX状态**: RX模块连接状态和名称
- **电池电量**: 当前电池电量百分比
- **操作提示**: "Press ENTER for Menu"

### 菜单系统
#### 主菜单结构
```
Main Menu
├── Settings (系统设置)
│   ├── Backlight (背光亮度: 0-100%)
│   ├── Auto Off (自动关机: 0-60分钟)
│   ├── Beeper (蜂鸣器: Off/On/Quiet)
│   ├── Save Settings (保存设置)
│   └── Back (返回)
├── Calibration (校准)
│   ├── Stick Calibration (摇杆校准)
│   ├── Reset Calibration (重置校准)
│   └── Back (返回)
├── ELRS Config (ELRS配置)
│   ├── Packet Rate (包率: 25-500Hz)
│   ├── TX Power (发射功率: 10mW-2000mW)
│   ├── Telem Ratio (遥测比率: Off-1:2)
│   └── Back (返回)
├── System Info (系统信息)
│   ├── Firmware: v1.0.0
│   ├── Hardware: v1.0
│   ├── CPU Usage: 0%
│   ├── Memory: 0KB
│   └── Back (返回)
└── About (关于)
```

### 摇杆校准流程
1. 进入 Main Menu -> Calibration -> Stick Calibration
2. 按ENTER开始校准
3. 将所有摇杆和电位器移动到中心位置，按ENTER
4. 将所有摇杆移动到最小位置，按ENTER
5. 将所有摇杆移动到最大位置，按ENTER
6. 校准完成，数据自动保存

### ELRS配置说明
#### 包率设置 (Packet Rate)
- **25Hz**: 最远距离，最低延迟要求
- **50Hz**: 远距离，低延迟要求
- **100Hz**: 平衡距离和延迟 (推荐)
- **150Hz**: 中等距离，中等延迟
- **200Hz**: 近距离，低延迟
- **250Hz**: 近距离，最低延迟
- **333Hz**: 竞速模式
- **500Hz**: 极速模式

#### 发射功率设置 (TX Power)
- **10mW**: 室内/近距离使用
- **25mW**: 一般使用 (推荐)
- **50mW**: 中距离使用
- **100mW**: 远距离使用
- **更高功率**: 根据当地法规使用

#### 遥测比率 (Telem Ratio)
- **Off**: 关闭遥测
- **1:128**: 最低遥测频率
- **1:64**: 低遥测频率
- **1:32**: 中低遥测频率
- **1:16**: 中等遥测频率 (推荐)
- **1:8**: 中高遥测频率
- **1:4**: 高遥测频率
- **1:2**: 最高遥测频率

## 功能特性

### ELRS配置
- 包率设置 (25/50/100/150/200Hz)
- 发射功率调整
- 遥测比率配置
- 模型匹配设置

### 通道映射
- 标准AETR布局
- 4个辅助通道
- 通道反向设置
- 端点调整

### 遥测显示
- 电池电压
- 信号强度
- 飞行模式
- GPS信息 (可选)

## 故障排除

### 常见问题
1. **无法连接ELRS模块**
   - 检查UART连接
   - 确认波特率设置
   - 检查模块供电

2. **OLED无显示**
   - 检查I2C连接
   - 确认设备地址
   - 检查供电电压

3. **摇杆无响应**
   - 检查ADC连接
   - 校准中点值
   - 检查供电稳定性

### 调试方法
- 使用串口输出调试信息
- LED指示灯状态
- OLED显示错误信息

## 开发计划

### 已完成功能
- [x] 基础HAL驱动
- [x] CRSF协议实现
- [x] OLED显示驱动
- [x] 按键处理
- [x] ADC采集

### 待开发功能
- [ ] 参数存储 (EEPROM)
- [ ] 无线配置 (WiFi)
- [ ] 固件升级
- [ ] 多模型支持

## 许可证

本项目基于GPL v3许可证开源，详见LICENSE文件。

## 致谢

本项目基于以下开源项目：
- [simpleTx_esp32](https://github.com/kkbin505/Arduino-Transmitter-for-ELRS)
- [ExpressLRS](https://github.com/ExpressLRS/ExpressLRS)
- [DeviationTX](https://github.com/DeviationTX/deviation)

## 联系方式

如有问题或建议，请提交Issue或Pull Request。
