/**
 * @file main_v2.c
 * @brief 主程序 - 分层架构版本 (基于deviation设计)
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "config.h"
#include "hal_drivers.h"
#include "clock_system.h"
#include "crsf_protocol_v2.h"
#include "adc_input.h"
#include "button_input.h"
#include "oled_display.h"
#include "menu_system.h"
#include "mixer.h"
#include "sound.h"
#include "eeprom.h"
#include "power_management.h"
#include "calibration.h"
#include "usb_cdc.h"

/* 私有函数声明 */
static error_code_t System_Init(void);
static void System_MainLoop(void);
static void System_LowPriorityTasks(void);
static void System_MediumPriorityTasks(void);

/**
 * @brief 主函数
 */
int main(void)
{
    /* 系统初始化 */
    if (System_Init() != ERR_OK) {
        Error_Handler();
    }

    /* 显示启动画面 */
    OLED_ShowSplashScreen("CRSF Controller", "Deviation Architecture");
    HAL_Delay(2000);

    /* 检查是否需要开机校准 */
    if (Calibration_IsNeeded()) {
        Calibration_StartBoot();
        
        while (!Calibration_IsBootComplete()) {
            Calibration_ProcessBoot();
            HAL_Delay(50);
            
            /* 处理按键 */
            Button_Input_Scan();
            if (Button_Input_GetEvent(BUTTON_KEY1) == BUTTON_EVENT_PRESS) {
                break;  // 按退出键跳过校准
            }
        }
        
        HAL_Delay(2000);
    }

    /* 启动时钟系统 */
    CLOCK_Init();

    /* 设置周期性任务 */
    CLOCK_SetCallback(PRIORITY_MEDIUM, MEDIUM_PRIORITY_MSEC);
    CLOCK_SetCallback(PRIORITY_LOW, LOW_PRIORITY_MSEC);

    /* 启动CRSF协议 */
    CRSF_Protocol_Start();

    /* 播放开机音效 */
    SOUND_PlayStartup();

    /* 重置看门狗 */
    CLOCK_ResetWatchdog();

    DEBUG_PRINT("System initialized with deviation architecture\r\n");
    USB_CDC_Printf("CRSF Controller v2.0 - Deviation Architecture\r\n");

    /* 进入主循环 */
    System_MainLoop();

    return 0;
}

/**
 * @brief 系统初始化
 */
static error_code_t System_Init(void)
{
    /* HAL库初始化 */
    if (HAL_Init() != HAL_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 系统时钟配置 */
    SystemClock_Config();

    /* 硬件驱动初始化 */
    if (HAL_GPIO_Init_All() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    if (HAL_UART_Init_All() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    if (HAL_TIM_Init_All() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    if (HAL_I2C_Init_All() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    if (HAL_ADC_Init_All() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    if (HAL_USB_Init() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 外设模块初始化 */
    if (ADC_Input_Init() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    if (Button_Input_Init() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    if (OLED_Init() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    if (Menu_Init() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    if (MIXER_Init() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 音效系统初始化 */
    SOUND_Init();

    if (EEPROM_Init() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    if (Power_Init() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    if (USB_CDC_Init() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    if (CRSF_Protocol_Init() != ERR_OK) {
        return ERR_HARDWARE_FAULT;
    }

    /* 启动外设 */
    ADC_Input_Start();
    USB_CDC_Start();

    return ERR_OK;
}

/**
 * @brief 主循环
 */
static void System_MainLoop(void)
{
    while (1) {
        /* 处理时钟系统事件 */
        CLOCK_ProcessEvents();

        /* 处理中优先级任务 */
        if (CLOCK_IsReady(PRIORITY_MEDIUM)) {
            CLOCK_ClearReady(PRIORITY_MEDIUM);
            System_MediumPriorityTasks();
        }

        /* 处理低优先级任务 */
        if (CLOCK_IsReady(PRIORITY_LOW)) {
            CLOCK_ClearReady(PRIORITY_LOW);
            System_LowPriorityTasks();
        }

        /* 重置看门狗 */
        CLOCK_ResetWatchdog();

        /* 进入低功耗模式 */
        __WFI();
    }
}

/**
 * @brief 中优先级任务 (2ms周期)
 */
static void System_MediumPriorityTasks(void)
{
    /* 按键扫描 */
    Button_Input_Scan();

    /* 音效处理已集成到时钟系统中，无需额外处理 */
}

/**
 * @brief 低优先级任务 (10ms周期)
 */
static void System_LowPriorityTasks(void)
{
    static uint32_t task_counter = 0;
    task_counter++;

    /* 菜单系统 (每次) */
    Menu_Task(NULL);

    /* 电源管理 (每10次 = 100ms) */
    if (task_counter % 10 == 0) {
        Power_Task(NULL);
    }

    /* USB CDC (每次) */
    USB_CDC_Task(NULL);

    /* CRSF接收数据处理 (每次) - 在主循环中处理，避免中断阻塞 */
    if (CRSF_HasRxData()) {
        CRSF_ProcessRxData();  // 分片处理，每次最多8字节
    }

    /* 显示更新 (每5次 = 50ms) */
    if (task_counter % 5 == 0) {
        OLED_Update();
    }

    /* 状态输出 (每100次 = 1秒) */
    if (task_counter % 100 == 0) {
        USB_CDC_Printf("CRSF: State=%d, Runtime=%dus, Mixer=%s\r\n",
                      CRSF_GetState(),
                      CRSF_GetMixerRuntime(),
                      (mixer_sync == MIX_DONE) ? "OK" : "BUSY");
    }
}

/**
 * @brief ADC滤波函数 (在EXTI1中断中调用)
 */
void ADC_Filter(void)
{
    /* 更新ADC滤波 */
    ADC_Input_UpdateFilters();
}

/**
 * @brief 音效回调函数
 */
uint32_t Sound_Callback(void)
{
    /* 音效处理逻辑 */
    static uint32_t sound_counter = 0;
    sound_counter++;

    /* 这里可以添加复杂的音效序列 */
    if (sound_counter < 10) {
        return 100;  // 继续，100ms后再次调用
    }

    return 0;  // 停止音效
}

/**
 * @brief 错误处理函数
 */
void Error_Handler(void)
{
    /* 禁用中断 */
    __disable_irq();

    /* 停止所有外设 */
    CRSF_Protocol_Stop();
    CLOCK_StopTimer();

    /* 错误指示 */
    while (1) {
        HAL_GPIO_TogglePin(LED4_GPIO_Port, LED4_Pin);
        HAL_Delay(200);
    }
}

/**
 * @brief 系统时钟配置
 */
void SystemClock_Config(void)
{
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

    /* 配置主振荡器 */
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI | RCC_OSCILLATORTYPE_HSI48;
    RCC_OscInitStruct.HSIState = RCC_HSI_ON;
    RCC_OscInitStruct.HSI48State = RCC_HSI48_ON;
    RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
    RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL12;
    RCC_OscInitStruct.PLL.PREDIV = RCC_PREDIV_DIV1;

    if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK) {
        Error_Handler();
    }

    /* 配置系统时钟 */
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK | RCC_CLOCKTYPE_PCLK1;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;

    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_1) != HAL_OK) {
        Error_Handler();
    }

    /* 配置SysTick为1ms */
    HAL_SYSTICK_Config(HAL_RCC_GetHCLKFreq() / 1000);
    HAL_SYSTICK_CLKSourceConfig(SYSTICK_CLKSOURCE_HCLK);
    HAL_NVIC_SetPriority(SysTick_IRQn, 0, 0);
}

#if DEBUG_ENABLED
/**
 * @brief 重定向printf到USB CDC
 */
int _write(int file, char *ptr, int len)
{
    (void)file;
    
    if (USB_CDC_IsConfigured()) {
        USB_CDC_Transmit((uint8_t*)ptr, len);
    } else {
        HAL_UART_Transmit(&huart2, (uint8_t*)ptr, len, HAL_MAX_DELAY);
    }
    
    return len;
}
#endif
