#!/usr/bin/env python3
"""
STM32CubeMX配置验证脚本
验证CRSF_Controller.ioc文件是否正确配置
"""

import sys
import re
from pathlib import Path

def validate_ioc_config(ioc_file_path):
    """验证IOC配置文件"""
    
    print("🔍 验证STM32CubeMX配置文件...")
    print("=" * 60)
    
    if not Path(ioc_file_path).exists():
        print(f"❌ 错误: 找不到IOC文件: {ioc_file_path}")
        return False
    
    with open(ioc_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    errors = []
    warnings = []
    success_count = 0
    total_checks = 0
    
    # 检查项目列表
    checks = [
        # 时钟配置检查
        {
            'name': '时钟配置',
            'items': [
                ('HSE晶振', r'RCC\.HSEState=RCC_HSE_ON'),
                ('PLL配置', r'RCC\.PLLMul=RCC_PLL_MUL6'),
                ('系统时钟', r'RCC\.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK'),
                ('HSI48使能', r'RCC\.HSI48State=RCC_HSI48_ON'),
            ]
        },
        
        # ADC配置检查
        {
            'name': 'ADC配置',
            'items': [
                ('ADC通道数', r'ADC\.NbrOfConversion=13'),
                ('ADC分辨率', r'ADC\.Resolution=ADC_RESOLUTION_12B'),
                ('ADC触发源', r'ADC\.ExternalTrigConv=ADC_EXTERNALTRIGCONV_T15_TRGO'),
                ('DMA连续请求', r'ADC\.DMAContinuousRequests=ENABLE'),
                ('连续转换', r'ADC\.ContinuousConvMode=ENABLE'),
            ]
        },
        
        # 定时器配置检查
        {
            'name': '定时器配置',
            'items': [
                ('TIM2分频', r'TIM2\.Prescaler=47'),
                ('TIM2周期', r'TIM2\.Period=0xFFFFFFFF'),
                ('TIM15分频', r'TIM15\.Prescaler=47'),
                ('TIM15周期', r'TIM15\.Period=999'),
                ('TIM1分频', r'TIM1\.Prescaler=47'),
                ('TIM3分频', r'TIM3\.Prescaler=47'),
            ]
        },
        
        # UART配置检查
        {
            'name': 'UART配置',
            'items': [
                ('UART波特率', r'USART1\.BaudRate=420000'),
                ('UART字长', r'USART1\.WordLength=UART_WORDLENGTH_8B'),
                ('UART停止位', r'USART1\.StopBits=UART_STOPBITS_1'),
                ('UART校验', r'USART1\.Parity=UART_PARITY_NONE'),
            ]
        },
        
        # I2C配置检查
        {
            'name': 'I2C配置',
            'items': [
                ('I2C时钟', r'I2C2\.ClockSpeed=100000'),
                ('I2C模式', r'I2C2\.AddressingMode=I2C_ADDRESSINGMODE_7BIT'),
            ]
        },
        
        # GPIO配置检查
        {
            'name': 'GPIO配置',
            'items': [
                ('PB1软中断', r'PB1\.Signal=GPXTI1'),
                ('PB2软中断', r'PB2\.Signal=GPXTI2'),
                ('PB3软中断', r'PB3\.Signal=GPXTI3'),
                ('PB4软中断', r'PB4\.Signal=GPXTI4'),
                ('PA8 PWM', r'PA8\.Signal=S_TIM1_CH1'),
                ('PE3 PWM', r'PE3\.Signal=S_TIM3_CH1'),
            ]
        },
        
        # 中断优先级检查
        {
            'name': '中断优先级',
            'items': [
                ('TIM2优先级', r'NVIC\.TIM2_IRQn=true:0:0'),
                ('EXTI0_1优先级', r'NVIC\.EXTI0_1_IRQn=true:1:0'),
                ('EXTI4_15优先级', r'NVIC\.EXTI4_15_IRQn=true:2:0'),
                ('EXTI2_3优先级', r'NVIC\.EXTI2_3_IRQn=true:3:0'),
                ('USART1优先级', r'NVIC\.USART1_IRQn=true:4:0'),
                ('TIM15优先级', r'NVIC\.TIM15_IRQn=true:5:0'),
            ]
        },
        
        # 看门狗配置检查
        {
            'name': '看门狗配置',
            'items': [
                ('IWDG分频', r'IWDG\.Prescaler=IWDG_PRESCALER_32'),
                ('IWDG重载值', r'IWDG\.Reload=2312'),
            ]
        },
        
        # DMA配置检查
        {
            'name': 'DMA配置',
            'items': [
                ('ADC DMA', r'Dma\.ADC\.0\.Instance=DMA1_Channel1'),
                ('ADC DMA模式', r'Dma\.ADC\.0\.Mode=DMA_CIRCULAR'),
                ('UART TX DMA', r'Dma\.USART1_TX\.0\.Instance=DMA1_Channel2'),
                ('UART RX DMA', r'Dma\.USART1_RX\.0\.Instance=DMA1_Channel3'),
            ]
        }
    ]
    
    # 执行检查
    for check_group in checks:
        print(f"\n📋 {check_group['name']}:")
        group_success = 0
        
        for item_name, pattern in check_group['items']:
            total_checks += 1
            if re.search(pattern, content):
                print(f"  ✅ {item_name}")
                success_count += 1
                group_success += 1
            else:
                print(f"  ❌ {item_name}")
                errors.append(f"{check_group['name']}: {item_name}")
        
        print(f"  📊 {group_success}/{len(check_group['items'])} 项通过")
    
    # 额外检查：ADC通道映射
    print(f"\n📋 ADC通道映射:")
    adc_channels = [
        ('PA0', 'ADC_CHANNEL_0'),
        ('PA1', 'ADC_CHANNEL_1'),
        ('PA2', 'ADC_CHANNEL_2'),
        ('PA3', 'ADC_CHANNEL_3'),
        ('PA4', 'ADC_CHANNEL_4'),
        ('PA5', 'ADC_CHANNEL_5'),
        ('PA6', 'ADC_CHANNEL_6'),
        ('PA7', 'ADC_CHANNEL_7'),
        ('PC0', 'ADC_CHANNEL_10'),
        ('PC1', 'ADC_CHANNEL_11'),
        ('PC2', 'ADC_CHANNEL_12'),
        ('PC3', 'ADC_CHANNEL_13'),
        ('PC5', 'ADC_CHANNEL_15'),
    ]
    
    adc_success = 0
    for pin, channel in adc_channels:
        total_checks += 1
        if f"{pin}.Signal=ADC_IN" in content:
            print(f"  ✅ {pin} → {channel}")
            success_count += 1
            adc_success += 1
        else:
            print(f"  ❌ {pin} → {channel}")
            errors.append(f"ADC通道: {pin}")
    
    print(f"  📊 {adc_success}/{len(adc_channels)} 个ADC通道配置正确")
    
    # 总结报告
    print("\n" + "=" * 60)
    print("📊 配置验证总结:")
    print(f"✅ 通过: {success_count}/{total_checks} 项 ({success_count/total_checks*100:.1f}%)")
    
    if errors:
        print(f"❌ 失败: {len(errors)} 项")
        print("\n🔧 需要修复的配置:")
        for error in errors[:10]:  # 只显示前10个错误
            print(f"  • {error}")
        if len(errors) > 10:
            print(f"  ... 还有 {len(errors)-10} 个错误")
    
    if warnings:
        print(f"⚠️  警告: {len(warnings)} 项")
        for warning in warnings:
            print(f"  • {warning}")
    
    # 配置建议
    if success_count / total_checks >= 0.9:
        print("\n🎉 配置质量: 优秀")
        print("💡 建议: 配置基本完整，可以生成代码进行测试")
    elif success_count / total_checks >= 0.7:
        print("\n⚠️  配置质量: 良好")
        print("💡 建议: 修复关键错误后可以继续")
    else:
        print("\n❌ 配置质量: 需要改进")
        print("💡 建议: 请按照配置指南重新检查设置")
    
    return success_count / total_checks >= 0.8

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python validate_cubemx_config.py <IOC文件路径>")
        print("示例: python validate_cubemx_config.py STM32CubeMX/CRSF_Controller.ioc")
        sys.exit(1)
    
    ioc_file = sys.argv[1]
    success = validate_ioc_config(ioc_file)
    
    if success:
        print("\n🎯 验证完成: 配置符合要求")
        sys.exit(0)
    else:
        print("\n⚠️  验证完成: 配置需要改进")
        sys.exit(1)

if __name__ == "__main__":
    main()
