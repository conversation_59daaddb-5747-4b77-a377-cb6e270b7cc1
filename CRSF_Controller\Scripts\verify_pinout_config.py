#!/usr/bin/env python3
"""
STM32F072VBT6 引脚配置验证脚本
验证.ioc文件中的引脚配置是否与引脚图一致
"""

import re
import sys
from pathlib import Path

class PinoutVerifier:
    def __init__(self, ioc_file_path):
        self.ioc_file_path = Path(ioc_file_path)
        self.pin_config = {}
        self.errors = []
        self.warnings = []
        
        # 期望的引脚配置 (基于实际硬件)
        self.expected_config = {
            # 按键引脚 (GPIO输入，上拉)
            'PC6': {'signal': 'GPIO_Input', 'label': 'KEY3', 'pullup': True},
            'PC7': {'signal': 'GPIO_Input', 'label': 'KEY2', 'pullup': True},
            'PC8': {'signal': 'GPIO_Input', 'label': 'KEY1', 'pullup': True},
            'PC9': {'signal': 'GPIO_Input', 'label': 'KEY0', 'pullup': True},
            'PD14': {'signal': 'GPIO_Input', 'label': 'KEY5', 'pullup': True},
            'PD15': {'signal': 'GPIO_Input', 'label': 'KEY4', 'pullup': True},

            # 电源控制引脚
            'PD11': {'signal': 'GPIO_Input', 'label': 'PWR_SW', 'pullup': True},
            'PD12': {'signal': 'GPIO_Output', 'label': 'PWR_OFF'},
            'PD13': {'signal': 'GPIO_Input', 'label': 'STDBY'},

            # PWM输出
            'PA8': {'signal': 'S_TIM1_CH1', 'label': 'BUZZER_PWM'},
            'PE3': {'signal': 'S_TIM3_CH1', 'label': 'VIBRATOR_PWM'},

            # LED输出
            'PB8': {'signal': 'GPIO_Output', 'label': 'LED4'},
            'PC4': {'signal': 'GPIO_Output', 'label': 'LED5'},
            
            # ADC输入
            'PA0': {'signal': 'ADC_IN0'},
            'PA1': {'signal': 'ADC_IN1'},
            'PA2': {'signal': 'ADC_IN2'},
            'PA3': {'signal': 'ADC_IN3'},
            'PA4': {'signal': 'ADC_IN4'},
            'PA5': {'signal': 'ADC_IN5'},
            'PC0': {'signal': 'ADC_IN10'},
            'PC1': {'signal': 'ADC_IN11'},
            'PC2': {'signal': 'ADC_IN12'},
            'PC3': {'signal': 'ADC_IN13'},
            
            # 通信接口
            'PB6': {'signal': 'USART1_TX'},
            'PB7': {'signal': 'USART1_RX'},
            'PB10': {'signal': 'I2C2_SCL'},
            'PB11': {'signal': 'I2C2_SDA'},
            'PA11': {'signal': 'USB_DM'},
            'PA12': {'signal': 'USB_DP'},
        }
    
    def load_ioc_file(self):
        """加载.ioc文件并解析引脚配置"""
        try:
            with open(self.ioc_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析引脚配置
            pin_pattern = r'(P[A-F]\d+)\.(.+?)=(.+)'
            matches = re.findall(pin_pattern, content)
            
            for pin, property_name, value in matches:
                if pin not in self.pin_config:
                    self.pin_config[pin] = {}
                self.pin_config[pin][property_name] = value
            
            print(f"✅ 成功加载 {len(self.pin_config)} 个引脚配置")
            return True
            
        except Exception as e:
            self.errors.append(f"无法加载.ioc文件: {e}")
            return False
    
    def verify_pin_config(self, pin, expected):
        """验证单个引脚配置"""
        if pin not in self.pin_config:
            self.errors.append(f"❌ {pin}: 引脚未配置")
            return False
        
        config = self.pin_config[pin]
        pin_ok = True
        
        # 检查信号配置
        if 'signal' in expected:
            actual_signal = config.get('Signal', '')
            if actual_signal != expected['signal']:
                self.errors.append(f"❌ {pin}: 信号配置错误 (期望: {expected['signal']}, 实际: {actual_signal})")
                pin_ok = False
        
        # 检查标签
        if 'label' in expected:
            actual_label = config.get('GPIO_Label', '')
            if actual_label != expected['label']:
                self.errors.append(f"❌ {pin}: 标签错误 (期望: {expected['label']}, 实际: {actual_label})")
                pin_ok = False
        
        # 检查上拉配置
        if 'pullup' in expected and expected['pullup']:
            actual_pullup = config.get('GPIO_PuPd', '')
            if actual_pullup != 'GPIO_PULLUP':
                self.errors.append(f"❌ {pin}: 上拉配置错误 (期望: GPIO_PULLUP, 实际: {actual_pullup})")
                pin_ok = False
        
        if pin_ok:
            print(f"✅ {pin}: 配置正确")
        
        return pin_ok
    
    def verify_all_pins(self):
        """验证所有引脚配置"""
        print("\n=== 引脚配置验证 ===")
        
        total_pins = len(self.expected_config)
        correct_pins = 0
        
        for pin, expected in self.expected_config.items():
            if self.verify_pin_config(pin, expected):
                correct_pins += 1
        
        print(f"\n📊 验证结果: {correct_pins}/{total_pins} 个引脚配置正确")
        
        return correct_pins == total_pins
    
    def check_conflicts(self):
        """检查引脚冲突"""
        print("\n=== 引脚冲突检查 ===")
        
        used_pins = set()
        conflicts = []
        
        for pin in self.pin_config:
            if pin in used_pins:
                conflicts.append(pin)
            used_pins.add(pin)
        
        if conflicts:
            for pin in conflicts:
                self.errors.append(f"❌ {pin}: 引脚冲突")
        else:
            print("✅ 无引脚冲突")
        
        return len(conflicts) == 0
    
    def check_timer_config(self):
        """检查定时器配置"""
        print("\n=== 定时器配置检查 ===")
        
        # 检查TIM1是否配置为PWM
        tim1_configured = False
        for pin, config in self.pin_config.items():
            if config.get('Signal', '') == 'S_TIM1_CH1':
                tim1_configured = True
                print(f"✅ TIM1_CH1 配置在 {pin}")
                break
        
        if not tim1_configured:
            self.errors.append("❌ TIM1_CH1 未配置")
        
        return tim1_configured
    
    def generate_report(self):
        """生成验证报告"""
        print("\n" + "="*50)
        print("           引脚配置验证报告")
        print("="*50)
        
        if not self.errors and not self.warnings:
            print("🎉 所有配置验证通过！")
            return True
        
        if self.errors:
            print(f"\n❌ 发现 {len(self.errors)} 个错误:")
            for error in self.errors:
                print(f"   {error}")
        
        if self.warnings:
            print(f"\n⚠️  发现 {len(self.warnings)} 个警告:")
            for warning in self.warnings:
                print(f"   {warning}")
        
        print("\n💡 建议:")
        print("   1. 在STM32CubeMX中修正上述配置问题")
        print("   2. 重新生成代码")
        print("   3. 重新运行此验证脚本")
        
        return len(self.errors) == 0
    
    def run_verification(self):
        """运行完整验证"""
        print("STM32F072VBT6 引脚配置验证工具")
        print("="*50)
        
        if not self.load_ioc_file():
            return False
        
        # 运行各项检查
        pin_config_ok = self.verify_all_pins()
        conflict_ok = self.check_conflicts()
        timer_ok = self.check_timer_config()
        
        # 生成报告
        return self.generate_report()

def main():
    if len(sys.argv) != 2:
        print("用法: python verify_pinout_config.py <path_to_ioc_file>")
        sys.exit(1)
    
    ioc_file = sys.argv[1]
    verifier = PinoutVerifier(ioc_file)
    
    success = verifier.run_verification()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
