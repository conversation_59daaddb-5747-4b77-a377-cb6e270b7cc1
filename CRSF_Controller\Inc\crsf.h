/**
 * @file crsf.h
 * @brief CRSF协议处理接口
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __CRSF_H
#define __CRSF_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"
#include "crsf_protocol.h"

/* CRSF状态定义 */
typedef enum {
    CRSF_STATE_IDLE = 0,
    CRSF_STATE_SYNC,
    CRSF_STATE_LENGTH,
    CRSF_STATE_DATA,
    CRSF_STATE_CRC
} crsf_state_t;

/* CRSF接收缓冲区 */
#define CRSF_RX_BUFFER_SIZE             256
#define CRSF_TX_BUFFER_SIZE             64

/* CRSF模块类型 */
typedef enum {
    MODULE_UNKNOWN = 0,
    MODULE_ELRS,
    MODULE_CROSSFIRE
} module_type_t;

/* 全局变量声明 */
extern uint16_t rcChannels[CRSF_MAX_CHANNELS];
extern crsf_device_t crsf_devices[CRSF_MAX_DEVICES];
extern elrs_info_t elrs_info;
extern elrs_info_t local_info;
extern module_type_t module_type;
extern crsf_link_statistics_t LinkStatistics;

extern uint8_t rxConnected;
extern uint8_t txConnected;
extern uint32_t crsfTime;
extern uint32_t lastCrsfTime;
extern uint32_t updateInterval;

/* CRSF接收状态 */
extern volatile uint8_t crsf_rx_buffer[CRSF_RX_BUFFER_SIZE];
extern volatile uint16_t crsf_rx_head;
extern volatile uint16_t crsf_rx_tail;
extern volatile bool crsf_frame_available;

/* CRSF发送缓冲区 */
extern uint8_t crsf_tx_buffer[CRSF_TX_BUFFER_SIZE];

/* 函数声明 */

/* 初始化和配置 */
error_code_t CRSF_Init(void);
error_code_t CRSF_Start(void);
error_code_t CRSF_Stop(void);
void CRSF_SetUpdateRate(uint32_t rate_hz);

/* 数据发送 */
error_code_t CRSF_SendChannels(const rc_input_t* rc_input);
error_code_t CRSF_SendFrame(uint8_t* frame, uint8_t length);
error_code_t CRSF_SendCommand(uint8_t dest_addr, uint8_t command, uint8_t* data, uint8_t length);

/* 数据接收和解析 */
void CRSF_ProcessRxData(void);
void CRSF_ParseFrame(uint8_t* frame, uint8_t length);
bool CRSF_IsFrameAvailable(void);
uint8_t CRSF_GetFrame(uint8_t* frame, uint8_t max_length);

/* ELRS特定功能 */
error_code_t CRSF_BroadcastPing(void);
error_code_t CRSF_GetElrsInfo(uint8_t target);
error_code_t CRSF_ReadParam(uint8_t param_num, uint8_t chunk_num, uint8_t target);
error_code_t CRSF_WriteParam(uint8_t param_num, uint8_t chunk_num, uint8_t target);
error_code_t CRSF_SendModelId(uint8_t model_id);

/* 设备管理 */
void CRSF_AddDevice(uint8_t* buffer);
void CRSF_ParseElrsInfo(uint8_t* buffer);
void CRSF_AddParam(uint8_t* buffer, uint8_t length);
uint8_t CRSF_CountParamsLoaded(uint8_t device_index);

/* 链路状态 */
void CRSF_CheckLinkState(void);
bool CRSF_IsConnected(void);
uint8_t CRSF_GetLinkQuality(void);
int8_t CRSF_GetRSSI(void);

/* 时序控制 */
void CRSF_SyncTiming(int32_t delay_us);
uint32_t CRSF_GetUpdateInterval(void);
bool CRSF_IsTimeToSend(void);

/* 工具函数 */
uint16_t CRSF_ConvertChannel(uint16_t adc_value);
void CRSF_PackChannels(const uint16_t* channels, uint8_t* packed_data);
void CRSF_UnpackChannels(const uint8_t* packed_data, uint16_t* channels);

/* 回调函数 */
void CRSF_OnFrameReceived(uint8_t frame_type, uint8_t* data, uint8_t length);
void CRSF_OnDeviceInfo(crsf_device_t* device);
void CRSF_OnLinkStatistics(crsf_link_statistics_t* stats);
void CRSF_OnBatteryInfo(crsf_battery_sensor_t* battery);
void CRSF_OnGpsInfo(crsf_gps_t* gps);
void CRSF_OnAttitude(crsf_attitude_t* attitude);
void CRSF_OnFlightMode(crsf_flight_mode_t* flight_mode);

/* 调试功能 */
#if DEBUG_ENABLED
void CRSF_PrintFrame(uint8_t* frame, uint8_t length);
void CRSF_PrintDeviceInfo(crsf_device_t* device);
void CRSF_PrintLinkStats(crsf_link_statistics_t* stats);
#endif

#ifdef __cplusplus
}
#endif

#endif /* __CRSF_H */
