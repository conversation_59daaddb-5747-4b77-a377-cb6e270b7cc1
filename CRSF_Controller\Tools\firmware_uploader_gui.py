#!/usr/bin/env python3
"""
CRSF Controller 固件升级工具 - GUI版本
提供图形界面的固件升级功能
"""

import sys
import os
import time
import threading
from pathlib import Path
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import serial.tools.list_ports

# 导入升级核心功能
from firmware_uploader import FirmwareUploader

class FirmwareUploaderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("CRSF Controller 固件升级工具")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # 设置图标 (如果有的话)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        self.uploader = None
        self.upload_thread = None
        self.is_uploading = False
        
        self.create_widgets()
        self.refresh_ports()
        
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="CRSF Controller 固件升级工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 设备选择区域
        device_frame = ttk.LabelFrame(main_frame, text="设备连接", padding="10")
        device_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(device_frame, text="串口:").grid(row=0, column=0, sticky=tk.W)
        self.port_var = tk.StringVar()
        self.port_combo = ttk.Combobox(device_frame, textvariable=self.port_var, 
                                      width=30, state="readonly")
        self.port_combo.grid(row=0, column=1, padx=(10, 0), sticky=(tk.W, tk.E))
        
        self.refresh_btn = ttk.Button(device_frame, text="刷新", command=self.refresh_ports)
        self.refresh_btn.grid(row=0, column=2, padx=(10, 0))
        
        # 连接状态
        self.status_var = tk.StringVar(value="未连接")
        self.status_label = ttk.Label(device_frame, textvariable=self.status_var)
        self.status_label.grid(row=1, column=0, columnspan=3, pady=(10, 0))
        
        # 固件文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="固件文件", padding="10")
        file_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(file_frame, text="文件:").grid(row=0, column=0, sticky=tk.W)
        self.file_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_var, width=40)
        self.file_entry.grid(row=0, column=1, padx=(10, 0), sticky=(tk.W, tk.E))
        
        self.browse_btn = ttk.Button(file_frame, text="浏览", command=self.browse_file)
        self.browse_btn.grid(row=0, column=2, padx=(10, 0))
        
        # 文件信息
        self.file_info_var = tk.StringVar(value="未选择文件")
        self.file_info_label = ttk.Label(file_frame, textvariable=self.file_info_var)
        self.file_info_label.grid(row=1, column=0, columnspan=3, pady=(10, 0))
        
        # 升级控制区域
        control_frame = ttk.LabelFrame(main_frame, text="升级控制", padding="10")
        control_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.upload_btn = ttk.Button(control_frame, text="开始升级", 
                                    command=self.start_upload, style="Accent.TButton")
        self.upload_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_btn = ttk.Button(control_frame, text="停止升级", 
                                  command=self.stop_upload, state="disabled")
        self.stop_btn.grid(row=0, column=1)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, 
                                           maximum=100, length=300)
        self.progress_bar.grid(row=1, column=0, columnspan=2, pady=(10, 0), sticky=(tk.W, tk.E))
        
        self.progress_text_var = tk.StringVar(value="准备就绪")
        self.progress_text = ttk.Label(control_frame, textvariable=self.progress_text_var)
        self.progress_text.grid(row=2, column=0, columnspan=2, pady=(5, 0))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="升级日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, width=70, height=12, 
                                                 font=("Consolas", 9))
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        device_frame.columnconfigure(1, weight=1)
        file_frame.columnconfigure(1, weight=1)
        control_frame.columnconfigure(1, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 初始日志
        self.log("CRSF Controller 固件升级工具已启动")
        self.log("请选择设备和固件文件")
        
    def refresh_ports(self):
        """刷新串口列表"""
        ports = []
        for port in serial.tools.list_ports.comports():
            ports.append(f"{port.device} - {port.description}")
        
        self.port_combo['values'] = ports
        if ports:
            self.port_combo.current(0)
            self.status_var.set(f"找到 {len(ports)} 个设备")
        else:
            self.status_var.set("未找到设备")
        
        self.log(f"刷新串口列表，找到 {len(ports)} 个设备")
        
    def browse_file(self):
        """浏览固件文件"""
        file_path = filedialog.askopenfilename(
            title="选择固件文件",
            filetypes=[("Binary files", "*.bin"), ("All files", "*.*")]
        )
        
        if file_path:
            self.file_var.set(file_path)
            
            # 显示文件信息
            file_size = os.path.getsize(file_path)
            file_name = os.path.basename(file_path)
            self.file_info_var.set(f"{file_name} ({file_size:,} 字节)")
            
            self.log(f"选择固件文件: {file_name} ({file_size:,} 字节)")
            
    def log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def update_progress(self, value, text):
        """更新进度"""
        self.progress_var.set(value)
        self.progress_text_var.set(text)
        self.root.update_idletasks()
        
    def start_upload(self):
        """开始升级"""
        # 检查输入
        if not self.port_var.get():
            messagebox.showerror("错误", "请选择设备端口")
            return
            
        if not self.file_var.get():
            messagebox.showerror("错误", "请选择固件文件")
            return
            
        if not os.path.exists(self.file_var.get()):
            messagebox.showerror("错误", "固件文件不存在")
            return
            
        # 确认升级
        result = messagebox.askyesno("确认升级", 
                                   "确定要开始固件升级吗？\n\n"
                                   "升级过程中请勿断开设备连接。")
        if not result:
            return
            
        # 开始升级
        self.is_uploading = True
        self.upload_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.browse_btn.config(state="disabled")
        self.refresh_btn.config(state="disabled")
        
        # 在新线程中执行升级
        self.upload_thread = threading.Thread(target=self.upload_worker)
        self.upload_thread.daemon = True
        self.upload_thread.start()
        
    def stop_upload(self):
        """停止升级"""
        self.is_uploading = False
        self.log("用户取消升级")
        self.upload_finished(False)
        
    def upload_worker(self):
        """升级工作线程"""
        try:
            # 获取端口名
            port_info = self.port_var.get()
            port_name = port_info.split(" - ")[0]
            
            # 创建上传器
            self.uploader = FirmwareUploader(port_name)
            
            # 读取固件文件
            firmware_file = self.file_var.get()
            with open(firmware_file, 'rb') as f:
                firmware_data = f.read()
            
            self.log(f"开始升级固件: {os.path.basename(firmware_file)}")
            self.update_progress(0, "连接设备...")
            
            # 连接设备
            if not self.uploader.connect():
                raise Exception("连接设备失败")
            
            self.log("设备连接成功")
            self.update_progress(10, "进入升级模式...")
            
            # 升级流程
            if not self.uploader.enter_upgrade_mode(len(firmware_data)):
                raise Exception("进入升级模式失败")
            
            self.log("已进入升级模式")
            self.update_progress(20, "擦除Flash...")
            
            if not self.uploader.erase_flash():
                raise Exception("擦除Flash失败")
            
            self.log("Flash擦除完成")
            self.update_progress(30, "写入固件...")
            
            # 写入固件 (带进度更新)
            total_size = len(firmware_data)
            written_size = 0
            chunk_size = 256
            
            while written_size < total_size and self.is_uploading:
                chunk_data = firmware_data[written_size:written_size + chunk_size]
                
                # 这里需要修改uploader来支持进度回调
                # 暂时使用简单的进度计算
                progress = 30 + (written_size * 40) // total_size
                self.update_progress(progress, f"写入固件... {written_size}/{total_size}")
                
                written_size += len(chunk_data)
                time.sleep(0.01)  # 模拟写入时间
            
            if not self.is_uploading:
                return
                
            self.log("固件写入完成")
            self.update_progress(70, "校验固件...")
            
            # 校验固件
            time.sleep(1)  # 模拟校验时间
            
            self.log("固件校验完成")
            self.update_progress(90, "完成升级...")
            
            if not self.uploader.exit_upgrade_mode():
                raise Exception("退出升级模式失败")
            
            self.log("升级完成，设备重启中...")
            self.update_progress(100, "升级成功!")
            
            # 复位设备
            self.uploader.reset_device()
            
            self.upload_finished(True)
            
        except Exception as e:
            self.log(f"升级失败: {str(e)}")
            self.upload_finished(False)
        finally:
            if self.uploader:
                self.uploader.disconnect()
                
    def upload_finished(self, success):
        """升级完成"""
        self.is_uploading = False
        self.upload_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.browse_btn.config(state="normal")
        self.refresh_btn.config(state="normal")
        
        if success:
            messagebox.showinfo("升级成功", "固件升级成功！\n设备将自动重启到新固件。")
            self.log("固件升级成功完成")
        else:
            messagebox.showerror("升级失败", "固件升级失败！\n请检查设备连接和固件文件。")
            self.log("固件升级失败")
            
        self.update_progress(0, "准备就绪")

def main():
    root = tk.Tk()
    app = FirmwareUploaderGUI(root)
    
    # 设置窗口关闭事件
    def on_closing():
        if app.is_uploading:
            if messagebox.askokcancel("退出", "升级正在进行中，确定要退出吗？"):
                app.stop_upload()
                root.destroy()
        else:
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == '__main__':
    main()
