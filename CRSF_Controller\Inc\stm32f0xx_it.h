/**
 * @file stm32f0xx_it.h
 * @brief 中断服务程序头文件
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __STM32F0XX_IT_H
#define __STM32F0XX_IT_H

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32f0xx_hal.h"

/* 中断处理程序声明 */
void NMI_Handler(void);
void HardFault_Handler(void);
void SVC_Handler(void);
void PendSV_Handler(void);
void SysTick_Handler(void);
void DMA1_Channel1_IRQHandler(void);
void DMA1_Channel2_3_IRQHandler(void);
void TIM6_IRQHandler(void);
void TIM7_IRQHandler(void);
void USART1_IRQHandler(void);
void USART2_IRQHandler(void);
void I2C1_IRQHandler(void);
void ADC1_IRQHandler(void);

#ifdef __cplusplus
}
#endif

#endif /* __STM32F0XX_IT_H */
