# STM32CubeMX 配置文件说明

## 📁 **文件说明**

### CRSF_Controller.ioc
这是STM32CubeMX的项目配置文件，包含了CRSF Controller的完整硬件配置。

## 🚀 **使用方法**

### 1. 导入配置
```
1. 启动STM32CubeMX
2. 选择 "File" → "Open Project"
3. 选择 CRSF_Controller.ioc 文件
4. 项目将自动加载所有配置
```

### 2. 生成代码
```
1. 检查配置是否正确
2. 点击 "Project Manager" 标签
3. 设置项目名称和路径
4. 选择工具链 (推荐Makefile)
5. 点击 "GENERATE CODE"
```

### 3. 集成应用代码
```
生成代码后需要添加应用层代码:
1. 复制 CRSF_Controller/Src/*.c 到 Core/Src/
2. 复制 CRSF_Controller/Inc/*.h 到 Core/Inc/
3. 修改 main.c 添加用户代码
4. 配置 Makefile 添加源文件
```

## ⚙️ **配置要点**

### 时钟配置
- **系统时钟**: 48MHz (HSE 8MHz × PLL 6)
- **外设时钟**: 48MHz
- **USB时钟**: HSI48 (48MHz)
- **ADC时钟**: 12MHz (PCLK/4)

### 外设配置
- **ADC**: 10通道，12位精度，连续转换，DMA传输
- **UART1**: 420000波特率，DMA收发
- **I2C2**: 100kHz标准模式，DMA传输
- **USB**: CDC虚拟串口
- **定时器**: TIM1(音效)，TIM2(CRSF)，TIM3(震动)，TIM7(ADC触发)

### DMA配置
- **DMA1_CH1**: ADC (P2M, Circular, High)
- **DMA1_CH2**: USART1_TX (M2P, Normal, Medium)
- **DMA1_CH3**: USART1_RX (P2M, Circular, Medium)
- **DMA1_CH4**: I2C2_TX (M2P, Normal, Low)
- **DMA1_CH5**: I2C2_RX (P2M, Normal, Low)

### 中断优先级
- **TIM2**: 0 (CRSF定时器 - 最高)
- **EXTI1**: 1 (混音器计算 - 高)
- **EXTI4-15**: 2 (UART处理 - 中)
- **EXTI3**: 3 (后台任务 - 低)

## 📋 **引脚分配**

### 输入引脚
```
ADC输入:
PA0 - ADC_IN0 (CH1右摇杆X)
PA1 - ADC_IN1 (CH2右摇杆Y)
PA2 - ADC_IN2 (CH3左摇杆Y)
PA3 - ADC_IN3 (CH4左摇杆X)
PA4 - ADC_IN4 (SWA三段开关)
PA5 - ADC_IN5 (SWB三段开关)
PA6 - ADC_IN6 (VRA电位器)
PA7 - ADC_IN7 (VRB电位器)
PB0 - ADC_IN8 (VBAT电池电压)
PB1 - ADC_IN9 (VIN外接电源)

按键输入:
PD0 - KEY1 (菜单键)
PD1 - KEY2 (上键)
PD2 - KEY3 (下键)
PD3 - KEY4 (左键) + EXTI3
PD4 - KEY5 (确认键) + EXTI4
```

### 输出引脚
```
通信接口:
PA9  - USART1_TX (CRSF发送)
PA10 - USART1_RX (CRSF接收)
PB10 - I2C2_SCL (OLED时钟)
PB11 - I2C2_SDA (OLED数据)
PA11 - USB_DM
PA12 - USB_DP

PWM输出:
PA8 - TIM1_CH1 (蜂鸣器PWM)
PE3 - TIM3_CH1 (震动电机PWM)

LED指示:
PE4 - LED1 (电源指示)
PE5 - LED2 (连接指示)
PE6 - LED3 (数据指示)
PE7 - LED4 (错误指示)
PC13 - STATUS_LED (状态LED)

软件中断:
PB1 - EXTI1 (混音器计算)
PB3 - EXTI3 (一次性任务)
```

### 调试接口
```
PA13 - SWDIO (SWD数据)
PA14 - SWCLK (SWD时钟)
```

## 🔧 **修改配置**

### 常见修改
1. **更改波特率**: USART1 → Parameter Settings → Baud Rate
2. **调整ADC采样**: ADC → Regular ConversionMode → Sampling Time
3. **修改PWM频率**: TIMx → Parameter Settings → Period
4. **更改中断优先级**: NVIC → Priority

### 重新生成代码
```
修改配置后:
1. 保存项目 (Ctrl+S)
2. 点击 "GENERATE CODE"
3. 选择 "Keep User Code when re-generating"
4. 确认生成
```

## ⚠️ **注意事项**

### 配置兼容性
- 确保STM32CubeMX版本 ≥ 6.3.0
- 使用STM32Cube FW_F0 V1.11.3或更高版本
- 检查引脚是否有冲突

### 代码保护
- 始终启用 "Keep User Code when re-generating"
- 在USER CODE区域添加应用代码
- 定期备份项目文件

### 性能优化
- 根据实际需求调整DMA优先级
- 优化中断优先级设置
- 合理配置时钟分频

## 🎯 **验证配置**

### 配置检查清单
- [ ] 时钟配置正确 (48MHz系统时钟)
- [ ] ADC通道配置完整 (10个通道)
- [ ] UART波特率正确 (420000)
- [ ] I2C速度合适 (100kHz)
- [ ] USB配置正确 (CDC类)
- [ ] 定时器配置正确 (PWM和定时)
- [ ] DMA通道无冲突
- [ ] 中断优先级合理
- [ ] 引脚分配无冲突

### 功能测试
生成代码后建议进行以下测试:
1. 编译是否成功
2. 下载是否正常
3. 基本外设是否工作
4. 中断响应是否正常
5. DMA传输是否正确

这个配置文件为CRSF Controller提供了完整的硬件抽象层基础！
