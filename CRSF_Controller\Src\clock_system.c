/**
 * @file clock_system.c
 * @brief 时钟系统和软件中断架构实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "clock_system.h"
#include "hal_drivers.h"
#include "adc_input.h"
#include "mixer.h"
#include "sound.h"

/* 全局变量定义 */
volatile uint32_t msecs = 0;
volatile uint32_t wdg_time = 0;
volatile uint8_t priority_ready = 0;
volatile mixer_sync_t mixer_sync = MIX_NOT_DONE;

/* 私有变量 */
static timer_callback_t timer_callback = NULL;
static func_callback_t func_callback = NULL;
static volatile uint8_t msec_callbacks = 0;
static volatile uint32_t msec_cbtime[PRIORITY_COUNT];

/**
 * @brief 时钟系统初始化
 */
error_code_t CLOCK_Init(void)
{
    /* 初始化变量 */
    msecs = 0;
    wdg_time = 0;
    priority_ready = 0;
    mixer_sync = MIX_NOT_DONE;
    timer_callback = NULL;
    func_callback = NULL;
    msec_callbacks = 0;
    
    /* 清除回调时间 */
    for (uint8_t i = 0; i < PRIORITY_COUNT; i++) {
        msec_cbtime[i] = 0;
    }
    
    /* 配置EXTI中断优先级 */
    HAL_NVIC_SetPriority(EXTI0_1_IRQn, 1, 0);      // PB1: 混音器计算 - 高优先级
    HAL_NVIC_SetPriority(EXTI2_3_IRQn, 3, 0);      // PB3: 一次性函数 - 低优先级
    HAL_NVIC_SetPriority(EXTI4_15_IRQn, 2, 0);     // PB4: UART处理 - 中优先级
    HAL_NVIC_SetPriority(TIM2_IRQn, 0, 0);         // 定时器最高优先级

    HAL_NVIC_EnableIRQ(EXTI0_1_IRQn);
    HAL_NVIC_EnableIRQ(EXTI2_3_IRQn);
    HAL_NVIC_EnableIRQ(EXTI4_15_IRQn);
    HAL_NVIC_EnableIRQ(TIM2_IRQn);
    
    /* 重置看门狗 */
    CLOCK_ResetWatchdog();
    
    return ERR_OK;
}

/**
 * @brief 启动定时器
 */
void CLOCK_StartTimer(uint32_t us, timer_callback_t cb)
{
    if (!cb) {
        return;
    }
    
    timer_callback = cb;
    
    /* 获取当前计数值 */
    uint32_t current_count = __HAL_TIM_GET_COUNTER(&htim2);
    
    /* 设置比较值 */
    __HAL_TIM_SET_COMPARE(&htim2, TIM_CHANNEL_1, current_count + us);
    
    /* 清除中断标志并使能中断 */
    __HAL_TIM_CLEAR_FLAG(&htim2, TIM_FLAG_CC1);
    __HAL_TIM_ENABLE_IT(&htim2, TIM_IT_CC1);
}

/**
 * @brief 停止定时器
 */
void CLOCK_StopTimer(void)
{
    __HAL_TIM_DISABLE_IT(&htim2, TIM_IT_CC1);
    timer_callback = NULL;
}

/**
 * @brief 获取当前时间
 */
uint32_t CLOCK_GetTime(void)
{
    return msecs;
}

/**
 * @brief 触发混音器计算
 */
void CLOCK_RunMixer(void)
{
    mixer_sync = MIX_NOT_DONE;
    HAL_NVIC_SetPendingIRQ(EXTI1_IRQn);  // PB1触发混音器计算
}

/**
 * @brief 运行一次性函数 (低优先级)
 */
void CLOCK_RunOnce(func_callback_t cb)
{
    if (cb) {
        func_callback = cb;
        HAL_NVIC_SetPendingIRQ(EXTI3_IRQn);  // PB3触发一次性函数
    }
}

/**
 * @brief 运行UART处理函数 (中优先级)
 */
void CLOCK_RunUART(func_callback_t cb)
{
    if (cb) {
        func_callback = cb;
        HAL_NVIC_SetPendingIRQ(EXTI4_IRQn);  // PB4触发UART处理
    }
}

/**
 * @brief 震动电机定时控制
 */
static uint32_t vibrator_stop_time = 0;
static bool vibrator_active = false;

void CLOCK_StartVibrator(uint32_t duration_ms)
{
    if (duration_ms > 0) {
        vibrator_stop_time = msecs + duration_ms;
        vibrator_active = true;
        VIBRATINGMOTOR_Start();
    }
}

void CLOCK_StopVibrator(void)
{
    vibrator_active = false;
    vibrator_stop_time = 0;
    VIBRATINGMOTOR_Stop();
}

bool CLOCK_IsVibratorActive(void)
{
    return vibrator_active;
}

/**
 * @brief 运行高优先级函数
 */
void CLOCK_RunHighPriority(func_callback_t cb)
{
    if (cb) {
        func_callback = cb;
        HAL_NVIC_SetPendingIRQ(EXTI0_IRQn);
    }
}

/**
 * @brief 运行低优先级函数
 */
void CLOCK_RunLowPriority(func_callback_t cb)
{
    if (cb) {
        func_callback = cb;
        HAL_NVIC_SetPendingIRQ(EXTI2_IRQn);
    }
}

/**
 * @brief 设置周期性回调
 */
void CLOCK_SetCallback(priority_level_t priority, uint32_t interval_ms)
{
    if (priority >= PRIORITY_COUNT) {
        return;
    }
    
    msec_callbacks |= (1 << priority);
    msec_cbtime[priority] = msecs + interval_ms;
}

/**
 * @brief 清除回调
 */
void CLOCK_ClearCallback(priority_level_t priority)
{
    if (priority >= PRIORITY_COUNT) {
        return;
    }
    
    msec_callbacks &= ~(1 << priority);
}

/**
 * @brief 检查优先级是否就绪
 */
bool CLOCK_IsReady(priority_level_t priority)
{
    return (priority_ready & (1 << priority)) != 0;
}

/**
 * @brief 清除就绪标志
 */
void CLOCK_ClearReady(priority_level_t priority)
{
    priority_ready &= ~(1 << priority);
}

/**
 * @brief 重置看门狗
 */
void CLOCK_ResetWatchdog(void)
{
    wdg_time = msecs;
}

/**
 * @brief 检查看门狗是否超时
 */
bool CLOCK_IsWatchdogExpired(void)
{
    return (msecs - wdg_time) > WATCHDOG_TIMEOUT_MS;
}

/**
 * @brief 主循环事件处理
 */
void CLOCK_ProcessEvents(void)
{
    /* 处理低优先级任务 */
    if (CLOCK_IsReady(PRIORITY_LOW)) {
        CLOCK_ClearReady(PRIORITY_LOW);
        
        /* 在这里添加低优先级任务 */
        // USB_CDC_Task();
        // Menu_Task();
        // Power_Task();
    }
    
    /* 处理音效任务 */
    if (CLOCK_IsReady(PRIORITY_SOUND)) {
        CLOCK_ClearReady(PRIORITY_SOUND);
        
        /* 音效处理 */
        // Sound_Task();
    }
    
    /* 检查看门狗 */
    if (CLOCK_IsWatchdogExpired()) {
        /* 看门狗超时处理 */
        Error_Handler();
    }
}

/**
 * @brief SysTick中断处理程序
 */
void SysTick_Handler(void)
{
    /* 增加毫秒计数 */
    msecs++;
    
    /* 检查看门狗 */
    if (msecs - wdg_time > WATCHDOG_TIMEOUT_MS) {
        HAL_NVIC_SetPendingIRQ(EXTI2_IRQn);  // 触发看门狗处理
        return;
    }
    
    /* 处理中优先级回调 */
    if (msec_callbacks & (1 << PRIORITY_MEDIUM)) {
        if (msecs >= msec_cbtime[PRIORITY_MEDIUM]) {
            if (mixer_sync == MIX_TIMER) {
                HAL_NVIC_SetPendingIRQ(EXTI1_IRQn);
            }
            priority_ready |= (1 << PRIORITY_MEDIUM);
            msec_cbtime[PRIORITY_MEDIUM] = msecs + MEDIUM_PRIORITY_MSEC;
        }
    }
    
    /* 处理低优先级回调 */
    if (msec_callbacks & (1 << PRIORITY_LOW)) {
        if (msecs >= msec_cbtime[PRIORITY_LOW]) {
            priority_ready |= (1 << PRIORITY_LOW);
            msec_cbtime[PRIORITY_LOW] = msecs + LOW_PRIORITY_MSEC;
        }
    }
    
    /* 处理音效回调 */
    if (msec_callbacks & (1 << PRIORITY_SOUND)) {
        if (msecs >= msec_cbtime[PRIORITY_SOUND]) {
            uint32_t next_ms = SOUND_Callback();  // 调用音效系统回调
            if (!next_ms) {
                msec_callbacks &= ~(1 << PRIORITY_SOUND);
            } else {
                msec_cbtime[PRIORITY_SOUND] = msecs + next_ms;
            }
        }
    }

    /* 处理震动电机超时 */
    if (vibrator_active && vibrator_stop_time > 0) {
        if (msecs >= vibrator_stop_time) {
            CLOCK_StopVibrator();
        }
    }

    /* 调用HAL的SysTick处理 */
    HAL_IncTick();
}

/**
 * @brief TIM2定时器中断处理程序
 */
void TIM2_IRQHandler(void)
{
    if (__HAL_TIM_GET_FLAG(&htim2, TIM_FLAG_CC1) != RESET) {
        if (timer_callback) {
            uint32_t next_us = timer_callback();
            
            __HAL_TIM_CLEAR_FLAG(&htim2, TIM_FLAG_CC1);
            
            if (next_us) {
                uint32_t current_ccr = __HAL_TIM_GET_COMPARE(&htim2, TIM_CHANNEL_1);
                __HAL_TIM_SET_COMPARE(&htim2, TIM_CHANNEL_1, current_ccr + next_us);
                return;
            }
        }
        CLOCK_StopTimer();
    }
}

/**
 * @brief EXTI0_1中断处理程序
 */
void EXTI0_1_IRQHandler(void)
{
    /* EXTI1: 混音器计算 (PB1) */
    if (__HAL_GPIO_EXTI_GET_FLAG(GPIO_PIN_1)) {
        __HAL_GPIO_EXTI_CLEAR_FLAG(GPIO_PIN_1);

        /* ADC滤波和混音器计算 */
        ADC_Filter();
        MIXER_CalcChannels();

        if (mixer_sync == MIX_NOT_DONE) {
            mixer_sync = MIX_DONE;
        }
    }
}

/**
 * @brief EXTI2_3中断处理程序
 */
void EXTI2_3_IRQHandler(void)
{
    /* EXTI3: 一次性函数执行 (PB3) */
    if (__HAL_GPIO_EXTI_GET_FLAG(GPIO_PIN_3)) {
        __HAL_GPIO_EXTI_CLEAR_FLAG(GPIO_PIN_3);

        if (func_callback) {
            func_callback();
            func_callback = NULL;
        }
    }
}

/**
 * @brief EXTI4_15中断处理程序
 */
void EXTI4_15_IRQHandler(void)
{
    /* EXTI4: UART处理 (PB4) */
    if (__HAL_GPIO_EXTI_GET_FLAG(GPIO_PIN_4)) {
        __HAL_GPIO_EXTI_CLEAR_FLAG(GPIO_PIN_4);

        if (func_callback) {
            func_callback();
            func_callback = NULL;
        }
    }
}
