/**
 * @file hal_drivers.h
 * @brief HAL驱动接口定义
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __HAL_DRIVERS_H
#define __HAL_DRIVERS_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"

/* 外设句柄声明 */
extern UART_HandleTypeDef huart1;      // CRSF UART
extern UART_HandleTypeDef huart2;      // Debug UART
extern I2C_HandleTypeDef hi2c1;        // OLED I2C
extern ADC_HandleTypeDef hadc1;        // ADC
extern TIM_HandleTypeDef htim2;        // 微秒定时器
extern TIM_HandleTypeDef htim6;        // 任务调度定时器
extern TIM_HandleTypeDef htim7;        // ADC触发定时器
extern DMA_HandleTypeDef hdma_usart1_rx;
extern DMA_HandleTypeDef hdma_usart1_tx;
extern DMA_HandleTypeDef hdma_adc1;

/* 系统初始化函数 */
error_code_t HAL_System_Init(void);
void SystemClock_Config(void);
void Error_Handler(void);

/* GPIO驱动函数 */
error_code_t HAL_GPIO_Init_All(void);
void HAL_GPIO_Button_Init(void);
void HAL_GPIO_ADC_Init(void);
void HAL_GPIO_UART_Init(void);
void HAL_GPIO_I2C_Init(void);

/* UART驱动函数 */
error_code_t HAL_UART_Init_All(void);
error_code_t HAL_UART_Transmit_DMA_Safe(UART_HandleTypeDef *huart, uint8_t *pData, uint16_t Size);
error_code_t HAL_UART_Receive_DMA_Safe(UART_HandleTypeDef *huart, uint8_t *pData, uint16_t Size);
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart);
void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart);
void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart);

/* I2C驱动函数 */
error_code_t HAL_I2C_Init_All(void);
error_code_t HAL_I2C_Transmit_Safe(I2C_HandleTypeDef *hi2c, uint16_t DevAddress, 
                                   uint8_t *pData, uint16_t Size, uint32_t Timeout);
error_code_t HAL_I2C_Mem_Write_Safe(I2C_HandleTypeDef *hi2c, uint16_t DevAddress, 
                                     uint16_t MemAddress, uint16_t MemAddSize, 
                                     uint8_t *pData, uint16_t Size, uint32_t Timeout);

/* ADC驱动函数 */
error_code_t HAL_ADC_Init_All(void);
error_code_t HAL_ADC_Start_DMA_Safe(void);
error_code_t HAL_ADC_Stop_DMA_Safe(void);

/* USB相关函数 */
error_code_t HAL_USB_Init(void);
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef *hadc);
void HAL_ADC_ConvHalfCpltCallback(ADC_HandleTypeDef *hadc);

/* 定时器驱动函数 */
error_code_t HAL_TIM_Init_All(void);
error_code_t HAL_TIM_Start_All(void);
error_code_t HAL_TIM_Stop_All(void);
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim);

/* 时间函数 */
uint32_t micros(void);
uint32_t millis(void);
void delay_us(uint32_t us);
void delay_ms(uint32_t ms);

/* DMA驱动函数 */
error_code_t HAL_DMA_Init_All(void);

/* 中断优先级配置 */
void HAL_NVIC_Config(void);

/* 电源管理 */
void HAL_PWR_EnterSleepMode(void);
void HAL_PWR_EnterStopMode(void);

/* 看门狗 */
error_code_t HAL_IWDG_Init(void);
void HAL_IWDG_Refresh(void);

#ifdef __cplusplus
}
#endif

#endif /* __HAL_DRIVERS_H */
