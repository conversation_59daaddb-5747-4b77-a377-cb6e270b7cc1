/**
 * @file menu_system.c
 * @brief 菜单系统实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "menu_system.h"
#include "crsf.h"
#include "adc_input.h"

/* 全局变量定义 */
menu_context_t menu_context;
main_screen_info_t main_screen_info;

/* 私有变量 */
static bool menu_initialized = false;
static menu_item_t menu_items[50]; // 菜单项池
static uint8_t menu_item_count = 0;

/* 私有函数声明 */
static menu_item_t* Menu_AllocateItem(void);
static void Menu_BuildMainMenu(void);
static void Menu_BuildSettingsMenu(void);
static void Menu_BuildCalibrationMenu(void);
static void Menu_BuildElrsConfigMenu(void);
static void Menu_BuildSystemInfoMenu(void);
static void Menu_DrawMenuItem(uint8_t y, menu_item_t* item, bool selected, bool editing);
static void Menu_DrawScrollBar(void);
static void Menu_HandleTimeout(void);

/* 菜单回调函数声明 */
static void Menu_Action_Calibrate(menu_item_t* item);
static void Menu_Action_CalibrateSwitch(menu_item_t* item);
static void Menu_Action_FullCalibrate(menu_item_t* item);
static void Menu_Action_Reset(menu_item_t* item);
static void Menu_Action_Save(menu_item_t* item);
static void Menu_Value_Changed_Rate(menu_item_t* item, int32_t old_value, int32_t new_value);
static void Menu_Value_Changed_Power(menu_item_t* item, int32_t old_value, int32_t new_value);

/**
 * @brief 菜单系统初始化
 */
error_code_t Menu_Init(void)
{
    if (menu_initialized) {
        return ERR_OK;
    }

    /* 初始化菜单上下文 */
    memset(&menu_context, 0, sizeof(menu_context));
    memset(&main_screen_info, 0, sizeof(main_screen_info));
    memset(menu_items, 0, sizeof(menu_items));
    
    menu_context.state = MENU_STATE_MAIN;
    menu_context.max_visible_items = MENU_MAX_VISIBLE_ITEMS;
    menu_context.auto_return_time = MENU_AUTO_RETURN_TIME;
    menu_context.need_refresh = true;
    
    /* 初始化主界面信息 */
    strcpy(main_screen_info.tx_name, "No TX");
    strcpy(main_screen_info.rx_name, "No RX");
    main_screen_info.tx_connected = false;
    main_screen_info.rx_connected = false;
    
    menu_item_count = 0;
    
    /* 构建菜单树 */
    Menu_BuildMenuTree();
    
    menu_initialized = true;
    return ERR_OK;
}

/**
 * @brief 构建菜单树
 */
error_code_t Menu_BuildMenuTree(void)
{
    /* 创建根菜单 */
    menu_context.root_menu = Menu_CreateItem("Main Menu", MENU_TYPE_FOLDER);
    menu_context.root_menu->id = MENU_ID_ROOT;
    
    /* 构建各级菜单 */
    Menu_BuildMainMenu();
    Menu_BuildSettingsMenu();
    Menu_BuildCalibrationMenu();
    Menu_BuildElrsConfigMenu();
    Menu_BuildSystemInfoMenu();
    
    /* 设置当前菜单为根菜单 */
    menu_context.current_menu = menu_context.root_menu;
    menu_context.selected_item = Menu_GetChild(menu_context.root_menu, 0);
    
    return ERR_OK;
}

/**
 * @brief 处理按键输入
 */
void Menu_HandleButton(button_id_t button, button_event_t event)
{
    if (event != BUTTON_EVENT_PRESS && event != BUTTON_EVENT_REPEAT) {
        return;
    }
    
    menu_context.last_activity = millis();
    menu_context.need_refresh = true;
    
    switch (menu_context.state) {
        case MENU_STATE_MAIN:
            if (button == BUTTON_ENTER) {
                menu_context.state = MENU_STATE_BROWSE;
                Menu_EnterMenu(menu_context.root_menu);
            }
            break;
            
        case MENU_STATE_BROWSE:
            switch (button) {
                case BUTTON_UP:
                    Menu_SelectPrevious();
                    break;
                case BUTTON_DOWN:
                    Menu_SelectNext();
                    break;
                case BUTTON_ENTER:
                    Menu_ExecuteAction();
                    break;
                case BUTTON_BACK:
                    Menu_ExitMenu();
                    break;
                case BUTTON_LEFT:
                    Menu_GoToParent();
                    break;
                case BUTTON_RIGHT:
                    if (menu_context.selected_item && 
                        menu_context.selected_item->type == MENU_TYPE_FOLDER) {
                        Menu_EnterMenu(menu_context.selected_item);
                    }
                    break;
            }
            break;
            
        case MENU_STATE_EDIT:
            switch (button) {
                case BUTTON_UP:
                case BUTTON_RIGHT:
                    Menu_IncreaseValue();
                    break;
                case BUTTON_DOWN:
                case BUTTON_LEFT:
                    Menu_DecreaseValue();
                    break;
                case BUTTON_ENTER:
                    Menu_ConfirmEdit();
                    break;
                case BUTTON_BACK:
                    Menu_CancelEdit();
                    break;
            }
            break;
            
        case MENU_STATE_CONFIRM:
            switch (button) {
                case BUTTON_ENTER:
                    menu_context.state = MENU_STATE_BROWSE;
                    break;
                case BUTTON_BACK:
                    menu_context.state = MENU_STATE_BROWSE;
                    break;
            }
            break;
    }
}

/**
 * @brief 选择下一个菜单项
 */
void Menu_SelectNext(void)
{
    if (!menu_context.current_menu) {
        return;
    }
    
    uint8_t child_count = Menu_GetChildCount(menu_context.current_menu);
    if (child_count == 0) {
        return;
    }
    
    menu_context.selected_index = (menu_context.selected_index + 1) % child_count;
    menu_context.selected_item = Menu_GetChild(menu_context.current_menu, menu_context.selected_index);
    
    Menu_UpdateScrollPosition();
}

/**
 * @brief 选择上一个菜单项
 */
void Menu_SelectPrevious(void)
{
    if (!menu_context.current_menu) {
        return;
    }
    
    uint8_t child_count = Menu_GetChildCount(menu_context.current_menu);
    if (child_count == 0) {
        return;
    }
    
    if (menu_context.selected_index == 0) {
        menu_context.selected_index = child_count - 1;
    } else {
        menu_context.selected_index--;
    }
    
    menu_context.selected_item = Menu_GetChild(menu_context.current_menu, menu_context.selected_index);
    Menu_UpdateScrollPosition();
}

/**
 * @brief 执行菜单动作
 */
void Menu_ExecuteAction(void)
{
    if (!menu_context.selected_item) {
        return;
    }
    
    switch (menu_context.selected_item->type) {
        case MENU_TYPE_FOLDER:
            Menu_EnterMenu(menu_context.selected_item);
            break;
            
        case MENU_TYPE_ACTION:
            if (menu_context.selected_item->action_callback) {
                menu_context.selected_item->action_callback(menu_context.selected_item);
            }
            break;
            
        case MENU_TYPE_VALUE:
        case MENU_TYPE_OPTION:
            Menu_StartEdit();
            break;
            
        case MENU_TYPE_BACK:
            Menu_GoToParent();
            break;
            
        default:
            break;
    }
}

/**
 * @brief 进入菜单
 */
void Menu_EnterMenu(menu_item_t* menu)
{
    if (!menu || menu->type != MENU_TYPE_FOLDER) {
        return;
    }
    
    menu_context.current_menu = menu;
    menu_context.selected_index = 0;
    menu_context.first_visible_index = 0;
    menu_context.selected_item = Menu_GetChild(menu, 0);
}

/**
 * @brief 退出菜单
 */
void Menu_ExitMenu(void)
{
    if (menu_context.current_menu == menu_context.root_menu) {
        menu_context.state = MENU_STATE_MAIN;
    } else {
        Menu_GoToParent();
    }
}

/**
 * @brief 返回父菜单
 */
void Menu_GoToParent(void)
{
    if (menu_context.current_menu && menu_context.current_menu->parent) {
        Menu_EnterMenu(menu_context.current_menu->parent);
    } else {
        menu_context.state = MENU_STATE_MAIN;
    }
}

/**
 * @brief 开始编辑
 */
void Menu_StartEdit(void)
{
    if (!menu_context.selected_item) {
        return;
    }
    
    menu_context.state = MENU_STATE_EDIT;
    menu_context.editing = true;
    menu_context.edit_value = menu_context.selected_item->value;
}

/**
 * @brief 确认编辑
 */
void Menu_ConfirmEdit(void)
{
    if (!menu_context.selected_item || !menu_context.editing) {
        return;
    }
    
    int32_t old_value = menu_context.selected_item->value;
    menu_context.selected_item->value = menu_context.edit_value;
    menu_context.selected_item->modified = true;
    
    /* 调用值改变回调 */
    if (menu_context.selected_item->value_changed_callback) {
        menu_context.selected_item->value_changed_callback(
            menu_context.selected_item, old_value, menu_context.edit_value);
    }
    
    menu_context.state = MENU_STATE_BROWSE;
    menu_context.editing = false;
}

/**
 * @brief 取消编辑
 */
void Menu_CancelEdit(void)
{
    menu_context.state = MENU_STATE_BROWSE;
    menu_context.editing = false;
}

/**
 * @brief 增加值
 */
void Menu_IncreaseValue(void)
{
    if (!menu_context.selected_item || !menu_context.editing) {
        return;
    }
    
    menu_item_t* item = menu_context.selected_item;
    
    if (item->type == MENU_TYPE_VALUE) {
        menu_context.edit_value += item->step;
        if (menu_context.edit_value > item->max_value) {
            menu_context.edit_value = item->max_value;
        }
    } else if (item->type == MENU_TYPE_OPTION) {
        menu_context.edit_value++;
        if (menu_context.edit_value >= item->option_count) {
            menu_context.edit_value = 0;
        }
    }
}

/**
 * @brief 减少值
 */
void Menu_DecreaseValue(void)
{
    if (!menu_context.selected_item || !menu_context.editing) {
        return;
    }
    
    menu_item_t* item = menu_context.selected_item;
    
    if (item->type == MENU_TYPE_VALUE) {
        menu_context.edit_value -= item->step;
        if (menu_context.edit_value < item->min_value) {
            menu_context.edit_value = item->min_value;
        }
    } else if (item->type == MENU_TYPE_OPTION) {
        if (menu_context.edit_value == 0) {
            menu_context.edit_value = item->option_count - 1;
        } else {
            menu_context.edit_value--;
        }
    }
}

/**
 * @brief 更新显示
 */
void Menu_UpdateDisplay(void)
{
    if (!menu_context.need_refresh) {
        return;
    }
    
    OLED_Clear();
    
    switch (menu_context.state) {
        case MENU_STATE_MAIN:
            Menu_ShowMainScreen();
            break;
        case MENU_STATE_BROWSE:
        case MENU_STATE_EDIT:
            Menu_ShowMenuScreen();
            break;
        case MENU_STATE_CONFIRM:
            Menu_ShowConfirmScreen("Confirmed!");
            break;
    }
    
    OLED_Update();
    menu_context.need_refresh = false;
}

/**
 * @brief 显示主界面
 */
void Menu_ShowMainScreen(void)
{
    OLED_SetFont(FONT_SIZE_6x8);
    
    /* 显示TX状态 */
    if (main_screen_info.tx_connected) {
        OLED_SetCursor(0, 0);
        OLED_Printf("TX: %s", main_screen_info.tx_name);
        
        OLED_SetCursor(0, 10);
        OLED_Printf("Mode: %d  LQ: %d%%", main_screen_info.rf_mode, main_screen_info.link_quality);
        
        OLED_SetCursor(0, 20);
        OLED_Printf("RSSI: %ddBm", main_screen_info.rssi);
        
        OLED_SetCursor(0, 30);
        OLED_Printf("Pkts: %d/%d", main_screen_info.bad_packets, main_screen_info.good_packets);
    } else {
        OLED_WriteStringAligned(20, "No TX Module", OLED_ALIGN_CENTER);
    }
    
    /* 显示RX状态 */
    if (main_screen_info.rx_connected) {
        OLED_SetCursor(0, 45);
        OLED_Printf("RX: %s", main_screen_info.rx_name);
    } else {
        OLED_SetCursor(0, 45);
        OLED_WriteString("RX: Not Connected");
    }
    
    /* 显示电池电量 */
    OLED_SetCursor(0, 55);
    OLED_Printf("Battery: %d%%", main_screen_info.battery_level);
    
    /* 显示操作提示 */
    OLED_WriteStringAligned(55, "Press ENTER for Menu", OLED_ALIGN_CENTER);
}

/**
 * @brief 任务函数
 */
void Menu_Task(void* parameters)
{
    (void)parameters;
    
    /* 处理超时 */
    Menu_HandleTimeout();
    
    /* 更新显示 */
    Menu_UpdateDisplay();
}

/**
 * @brief 分配菜单项
 */
static menu_item_t* Menu_AllocateItem(void)
{
    if (menu_item_count >= sizeof(menu_items) / sizeof(menu_items[0])) {
        return NULL;
    }

    menu_item_t* item = &menu_items[menu_item_count++];
    memset(item, 0, sizeof(menu_item_t));
    item->visible = true;
    item->enabled = true;

    return item;
}

/**
 * @brief 创建菜单项
 */
menu_item_t* Menu_CreateItem(const char* name, menu_type_t type)
{
    menu_item_t* item = Menu_AllocateItem();
    if (!item) {
        return NULL;
    }

    strncpy(item->name, name, sizeof(item->name) - 1);
    item->type = type;
    item->id = menu_item_count;

    return item;
}

/**
 * @brief 添加子菜单项
 */
void Menu_AddChild(menu_item_t* parent, menu_item_t* child)
{
    if (!parent || !child) {
        return;
    }

    child->parent = parent;

    if (!parent->children) {
        parent->children = child;
    } else {
        menu_item_t* last = parent->children;
        while (last->next) {
            last = last->next;
        }
        last->next = child;
    }
}

/**
 * @brief 获取子菜单项数量
 */
uint8_t Menu_GetChildCount(menu_item_t* parent)
{
    if (!parent) {
        return 0;
    }

    uint8_t count = 0;
    menu_item_t* child = parent->children;
    while (child) {
        if (child->visible) {
            count++;
        }
        child = child->next;
    }

    return count;
}

/**
 * @brief 获取指定索引的子菜单项
 */
menu_item_t* Menu_GetChild(menu_item_t* parent, uint8_t index)
{
    if (!parent) {
        return NULL;
    }

    uint8_t count = 0;
    menu_item_t* child = parent->children;
    while (child) {
        if (child->visible) {
            if (count == index) {
                return child;
            }
            count++;
        }
        child = child->next;
    }

    return NULL;
}

/**
 * @brief 构建主菜单
 */
static void Menu_BuildMainMenu(void)
{
    menu_item_t* settings = Menu_CreateItem("Settings", MENU_TYPE_FOLDER);
    settings->id = MENU_ID_SETTINGS;
    Menu_AddChild(menu_context.root_menu, settings);

    menu_item_t* calibration = Menu_CreateItem("Calibration", MENU_TYPE_FOLDER);
    calibration->id = MENU_ID_CALIBRATION;
    Menu_AddChild(menu_context.root_menu, calibration);

    menu_item_t* elrs_config = Menu_CreateItem("ELRS Config", MENU_TYPE_FOLDER);
    elrs_config->id = MENU_ID_ELRS_CONFIG;
    Menu_AddChild(menu_context.root_menu, elrs_config);

    menu_item_t* system_info = Menu_CreateItem("System Info", MENU_TYPE_FOLDER);
    system_info->id = MENU_ID_SYSTEM_INFO;
    Menu_AddChild(menu_context.root_menu, system_info);

    menu_item_t* about = Menu_CreateItem("About", MENU_TYPE_INFO);
    about->id = MENU_ID_ABOUT;
    Menu_AddChild(menu_context.root_menu, about);
}

/**
 * @brief 构建设置菜单
 */
static void Menu_BuildSettingsMenu(void)
{
    menu_item_t* settings = Menu_FindItemById(menu_context.root_menu, MENU_ID_SETTINGS);
    if (!settings) return;

    /* 背光亮度 */
    menu_item_t* backlight = Menu_CreateItem("Backlight", MENU_TYPE_VALUE);
    Menu_SetValueRange(backlight, 0, 100, 10);
    Menu_SetUnit(backlight, "%");
    backlight->value = 80;
    Menu_AddChild(settings, backlight);

    /* 自动关机时间 */
    menu_item_t* auto_off = Menu_CreateItem("Auto Off", MENU_TYPE_VALUE);
    Menu_SetValueRange(auto_off, 0, 60, 5);
    Menu_SetUnit(auto_off, "min");
    auto_off->value = 10;
    Menu_AddChild(settings, auto_off);

    /* 蜂鸣器 */
    static char* beeper_options[] = {"Off", "On", "Quiet"};
    menu_item_t* beeper = Menu_CreateItem("Beeper", MENU_TYPE_OPTION);
    Menu_SetOptions(beeper, beeper_options, 3);
    beeper->value = 1;
    Menu_AddChild(settings, beeper);

    /* 保存设置 */
    menu_item_t* save = Menu_CreateItem("Save Settings", MENU_TYPE_ACTION);
    Menu_SetActionCallback(save, Menu_Action_Save);
    Menu_AddChild(settings, save);

    /* 返回 */
    menu_item_t* back = Menu_CreateItem("Back", MENU_TYPE_BACK);
    Menu_AddChild(settings, back);
}

/**
 * @brief 构建校准菜单
 */
static void Menu_BuildCalibrationMenu(void)
{
    menu_item_t* calibration = Menu_FindItemById(menu_context.root_menu, MENU_ID_CALIBRATION);
    if (!calibration) return;

    /* 摇杆校准 */
    menu_item_t* stick_cal = Menu_CreateItem("摇杆校准", MENU_TYPE_ACTION);
    Menu_SetActionCallback(stick_cal, Menu_Action_Calibrate);
    Menu_AddChild(calibration, stick_cal);

    /* 开关校准 */
    menu_item_t* switch_cal = Menu_CreateItem("开关校准", MENU_TYPE_ACTION);
    Menu_SetActionCallback(switch_cal, Menu_Action_CalibrateSwitch);
    Menu_AddChild(calibration, switch_cal);

    /* 完整校准 */
    menu_item_t* full_cal = Menu_CreateItem("完整校准", MENU_TYPE_ACTION);
    Menu_SetActionCallback(full_cal, Menu_Action_FullCalibrate);
    Menu_AddChild(calibration, full_cal);

    /* 重置校准 */
    menu_item_t* reset_cal = Menu_CreateItem("重置校准", MENU_TYPE_ACTION);
    Menu_SetActionCallback(reset_cal, Menu_Action_Reset);
    Menu_AddChild(calibration, reset_cal);

    /* 校准状态 */
    menu_item_t* calib_status = Menu_CreateItem("校准状态", MENU_TYPE_INFO);
    Menu_AddChild(calibration, calib_status);

    /* 返回 */
    menu_item_t* back = Menu_CreateItem("返回", MENU_TYPE_BACK);
    Menu_AddChild(calibration, back);
}

/**
 * @brief 构建ELRS配置菜单
 */
static void Menu_BuildElrsConfigMenu(void)
{
    menu_item_t* elrs_config = Menu_FindItemById(menu_context.root_menu, MENU_ID_ELRS_CONFIG);
    if (!elrs_config) return;

    /* 包率设置 */
    static char* rate_options[] = {"25Hz", "50Hz", "100Hz", "150Hz", "200Hz"};
    menu_item_t* packet_rate = Menu_CreateItem("Packet Rate", MENU_TYPE_OPTION);
    Menu_SetOptions(packet_rate, rate_options, 5);
    Menu_SetValueChangedCallback(packet_rate, Menu_Value_Changed_Rate);
    packet_rate->value = 2; // 默认100Hz
    Menu_AddChild(elrs_config, packet_rate);

    /* 发射功率 */
    static char* power_options[] = {"10mW", "25mW", "50mW", "100mW"};
    menu_item_t* tx_power = Menu_CreateItem("TX Power", MENU_TYPE_OPTION);
    Menu_SetOptions(tx_power, power_options, 4);
    Menu_SetValueChangedCallback(tx_power, Menu_Value_Changed_Power);
    tx_power->value = 1; // 默认25mW
    Menu_AddChild(elrs_config, tx_power);

    /* 遥测比率 */
    static char* telem_options[] = {"Off", "1:128", "1:64", "1:32", "1:16", "1:8", "1:4", "1:2"};
    menu_item_t* telem_ratio = Menu_CreateItem("Telem Ratio", MENU_TYPE_OPTION);
    Menu_SetOptions(telem_ratio, telem_options, 8);
    telem_ratio->value = 4; // 默认1:16
    Menu_AddChild(elrs_config, telem_ratio);

    /* 返回 */
    menu_item_t* back = Menu_CreateItem("Back", MENU_TYPE_BACK);
    Menu_AddChild(elrs_config, back);
}

/**
 * @brief 构建系统信息菜单
 */
static void Menu_BuildSystemInfoMenu(void)
{
    menu_item_t* system_info = Menu_FindItemById(menu_context.root_menu, MENU_ID_SYSTEM_INFO);
    if (!system_info) return;

    /* 固件版本 */
    menu_item_t* firmware = Menu_CreateItem("Firmware: v1.0.0", MENU_TYPE_INFO);
    Menu_AddChild(system_info, firmware);

    /* 硬件版本 */
    menu_item_t* hardware = Menu_CreateItem("Hardware: v1.0", MENU_TYPE_INFO);
    Menu_AddChild(system_info, hardware);

    /* CPU使用率 */
    menu_item_t* cpu_usage = Menu_CreateItem("CPU Usage: 0%", MENU_TYPE_INFO);
    Menu_AddChild(system_info, cpu_usage);

    /* 内存使用 */
    menu_item_t* memory = Menu_CreateItem("Memory: 0KB", MENU_TYPE_INFO);
    Menu_AddChild(system_info, memory);

    /* 返回 */
    menu_item_t* back = Menu_CreateItem("Back", MENU_TYPE_BACK);
    Menu_AddChild(system_info, back);
}

/**
 * @brief 显示菜单界面
 */
void Menu_ShowMenuScreen(void)
{
    if (!menu_context.current_menu) {
        return;
    }

    /* 显示标题 */
    OLED_SetFont(FONT_SIZE_6x8);
    OLED_WriteStringAligned(0, menu_context.current_menu->name, OLED_ALIGN_CENTER);
    OLED_DrawHLine(0, 8, OLED_WIDTH, true);

    /* 显示菜单项 */
    uint8_t child_count = Menu_GetChildCount(menu_context.current_menu);
    uint8_t visible_count = MIN(child_count - menu_context.first_visible_index, menu_context.max_visible_items);

    for (uint8_t i = 0; i < visible_count; i++) {
        uint8_t item_index = menu_context.first_visible_index + i;
        menu_item_t* item = Menu_GetChild(menu_context.current_menu, item_index);
        if (item) {
            bool selected = (item_index == menu_context.selected_index);
            bool editing = (selected && menu_context.editing);
            Menu_DrawMenuItem(12 + i * MENU_ITEM_HEIGHT, item, selected, editing);
        }
    }

    /* 显示滚动条 */
    if (child_count > menu_context.max_visible_items) {
        Menu_DrawScrollBar();
    }

    /* 显示状态栏 */
    if (menu_context.state == MENU_STATE_EDIT) {
        OLED_WriteStringAligned(56, "Editing - ENTER to confirm", OLED_ALIGN_CENTER);
    } else {
        OLED_WriteStringAligned(56, "ENTER:Select  BACK:Return", OLED_ALIGN_CENTER);
    }
}

/**
 * @brief 绘制菜单项
 */
static void Menu_DrawMenuItem(uint8_t y, menu_item_t* item, bool selected, bool editing)
{
    if (!item) {
        return;
    }

    /* 绘制选择框 */
    if (selected) {
        OLED_FillRect(0, y - 1, OLED_WIDTH - MENU_SCROLL_BAR_WIDTH, MENU_ITEM_HEIGHT, true);
    }

    /* 设置文字颜色 */
    // 在实际的OLED库中，这里应该设置反色显示

    OLED_SetCursor(2, y);

    /* 绘制菜单项图标 */
    switch (item->type) {
        case MENU_TYPE_FOLDER:
            OLED_WriteString(">");
            break;
        case MENU_TYPE_ACTION:
            OLED_WriteString("*");
            break;
        case MENU_TYPE_VALUE:
        case MENU_TYPE_OPTION:
            OLED_WriteString(editing ? "E" : "=");
            break;
        case MENU_TYPE_INFO:
            OLED_WriteString("i");
            break;
        case MENU_TYPE_BACK:
            OLED_WriteString("<");
            break;
    }

    /* 绘制菜单项名称 */
    OLED_SetCursor(12, y);
    OLED_WriteString(item->name);

    /* 绘制值 */
    if (item->type == MENU_TYPE_VALUE || item->type == MENU_TYPE_OPTION) {
        char value_str[16];
        Menu_FormatValue(item, value_str, sizeof(value_str));

        uint8_t value_width = OLED_GetStringWidth(value_str);
        OLED_SetCursor(OLED_WIDTH - MENU_SCROLL_BAR_WIDTH - value_width - 2, y);
        OLED_WriteString(value_str);
    }
}

/**
 * @brief 绘制滚动条
 */
static void Menu_DrawScrollBar(void)
{
    uint8_t child_count = Menu_GetChildCount(menu_context.current_menu);
    if (child_count <= menu_context.max_visible_items) {
        return;
    }

    uint8_t scroll_height = 48; // 可滚动区域高度
    uint8_t bar_height = (menu_context.max_visible_items * scroll_height) / child_count;
    uint8_t bar_pos = (menu_context.first_visible_index * scroll_height) / child_count;

    /* 绘制滚动条背景 */
    OLED_DrawRect(OLED_WIDTH - MENU_SCROLL_BAR_WIDTH, 10, MENU_SCROLL_BAR_WIDTH, scroll_height, true);

    /* 绘制滚动条 */
    OLED_FillRect(OLED_WIDTH - MENU_SCROLL_BAR_WIDTH + 1, 10 + bar_pos,
                  MENU_SCROLL_BAR_WIDTH - 2, bar_height, true);
}

/**
 * @brief 格式化值显示
 */
void Menu_FormatValue(menu_item_t* item, char* buffer, size_t buffer_size)
{
    if (!item || !buffer) {
        return;
    }

    int32_t value = menu_context.editing ? menu_context.edit_value : item->value;

    switch (item->type) {
        case MENU_TYPE_VALUE:
            if (strlen(item->unit) > 0) {
                snprintf(buffer, buffer_size, "%ld%s", value, item->unit);
            } else {
                snprintf(buffer, buffer_size, "%ld", value);
            }
            break;

        case MENU_TYPE_OPTION:
            if (item->options && value >= 0 && value < item->option_count) {
                strncpy(buffer, item->options[value], buffer_size - 1);
                buffer[buffer_size - 1] = '\0';
            } else {
                snprintf(buffer, buffer_size, "%ld", value);
            }
            break;

        default:
            buffer[0] = '\0';
            break;
    }
}

/**
 * @brief 更新滚动位置
 */
void Menu_UpdateScrollPosition(void)
{
    if (menu_context.selected_index < menu_context.first_visible_index) {
        menu_context.first_visible_index = menu_context.selected_index;
    } else if (menu_context.selected_index >= menu_context.first_visible_index + menu_context.max_visible_items) {
        menu_context.first_visible_index = menu_context.selected_index - menu_context.max_visible_items + 1;
    }
}

/**
 * @brief 根据ID查找菜单项
 */
menu_item_t* Menu_FindItemById(menu_item_t* root, uint8_t id)
{
    if (!root) {
        return NULL;
    }

    if (root->id == id) {
        return root;
    }

    /* 递归查找子菜单 */
    menu_item_t* child = root->children;
    while (child) {
        menu_item_t* found = Menu_FindItemById(child, id);
        if (found) {
            return found;
        }
        child = child->next;
    }

    return NULL;
}

/**
 * @brief 设置值范围
 */
void Menu_SetValueRange(menu_item_t* item, int32_t min, int32_t max, int32_t step)
{
    if (!item) {
        return;
    }

    item->min_value = min;
    item->max_value = max;
    item->step = step;
}

/**
 * @brief 设置单位
 */
void Menu_SetUnit(menu_item_t* item, const char* unit)
{
    if (!item || !unit) {
        return;
    }

    strncpy(item->unit, unit, sizeof(item->unit) - 1);
    item->unit[sizeof(item->unit) - 1] = '\0';
}

/**
 * @brief 设置选项
 */
void Menu_SetOptions(menu_item_t* item, char** options, uint8_t count)
{
    if (!item) {
        return;
    }

    item->options = options;
    item->option_count = count;
}

/**
 * @brief 设置动作回调
 */
void Menu_SetActionCallback(menu_item_t* item, void (*callback)(menu_item_t*))
{
    if (!item) {
        return;
    }

    item->action_callback = callback;
}

/**
 * @brief 设置值改变回调
 */
void Menu_SetValueChangedCallback(menu_item_t* item, void (*callback)(menu_item_t*, int32_t, int32_t))
{
    if (!item) {
        return;
    }

    item->value_changed_callback = callback;
}

/**
 * @brief 显示确认界面
 */
void Menu_ShowConfirmScreen(const char* message)
{
    OLED_SetFont(FONT_SIZE_6x8);
    OLED_WriteStringAligned(20, message, OLED_ALIGN_CENTER);
    OLED_WriteStringAligned(40, "Press any key to continue", OLED_ALIGN_CENTER);
}

/**
 * @brief 处理超时
 */
static void Menu_HandleTimeout(void)
{
    if (menu_context.state == MENU_STATE_MAIN) {
        return;
    }

    uint32_t current_time = millis();
    if (current_time - menu_context.last_activity > menu_context.auto_return_time) {
        menu_context.state = MENU_STATE_MAIN;
        menu_context.need_refresh = true;
    }
}

/**
 * @brief 更新主界面信息
 */
void Menu_UpdateMainScreenInfo(const main_screen_info_t* info)
{
    if (!info) {
        return;
    }

    memcpy(&main_screen_info, info, sizeof(main_screen_info_t));

    if (menu_context.state == MENU_STATE_MAIN) {
        menu_context.need_refresh = true;
    }
}

/**
 * @brief 设置TX连接状态
 */
void Menu_SetTxConnected(bool connected, const char* name)
{
    main_screen_info.tx_connected = connected;
    if (name) {
        strncpy(main_screen_info.tx_name, name, sizeof(main_screen_info.tx_name) - 1);
        main_screen_info.tx_name[sizeof(main_screen_info.tx_name) - 1] = '\0';
    }

    if (menu_context.state == MENU_STATE_MAIN) {
        menu_context.need_refresh = true;
    }
}

/**
 * @brief 设置RX连接状态
 */
void Menu_SetRxConnected(bool connected, const char* name)
{
    main_screen_info.rx_connected = connected;
    if (name) {
        strncpy(main_screen_info.rx_name, name, sizeof(main_screen_info.rx_name) - 1);
        main_screen_info.rx_name[sizeof(main_screen_info.rx_name) - 1] = '\0';
    }

    if (menu_context.state == MENU_STATE_MAIN) {
        menu_context.need_refresh = true;
    }
}

/* 菜单回调函数实现 */

/**
 * @brief 摇杆校准动作回调
 */
static void Menu_Action_Calibrate(menu_item_t* item)
{
    (void)item;

    /* 启动摇杆校准 */
    Calibration_StartMenu();

    Menu_ShowConfirmScreen("摇杆校准已启动");
    menu_context.state = MENU_STATE_CONFIRM;
}

/**
 * @brief 开关校准动作回调
 */
static void Menu_Action_CalibrateSwitch(menu_item_t* item)
{
    (void)item;

    /* 启动开关校准 */
    // 这里可以添加专门的开关校准流程

    Menu_ShowConfirmScreen("开关校准已启动");
    menu_context.state = MENU_STATE_CONFIRM;
}

/**
 * @brief 完整校准动作回调
 */
static void Menu_Action_FullCalibrate(menu_item_t* item)
{
    (void)item;

    /* 启动完整校准 */
    Calibration_StartBoot();

    Menu_ShowConfirmScreen("完整校准已启动");
    menu_context.state = MENU_STATE_CONFIRM;
}

/**
 * @brief 重置动作回调
 */
static void Menu_Action_Reset(menu_item_t* item)
{
    (void)item;

    /* 重置校准数据 */
    // ADC_Input_ResetCalibration(ADC_CH_AILERON);

    Menu_ShowConfirmScreen("Calibration Reset");
    menu_context.state = MENU_STATE_CONFIRM;
}

/**
 * @brief 保存动作回调
 */
static void Menu_Action_Save(menu_item_t* item)
{
    (void)item;

    /* 保存设置到EEPROM */
    // Settings_Save();

    Menu_ShowConfirmScreen("Settings Saved");
    menu_context.state = MENU_STATE_CONFIRM;
}

/**
 * @brief 包率改变回调
 */
static void Menu_Value_Changed_Rate(menu_item_t* item, int32_t old_value, int32_t new_value)
{
    (void)old_value;

    /* 更新CRSF包率 */
    uint32_t rates[] = {25, 50, 100, 150, 200};
    if (new_value >= 0 && new_value < 5) {
        CRSF_SetUpdateRate(rates[new_value]);
    }
}

/**
 * @brief 发射功率改变回调
 */
static void Menu_Value_Changed_Power(menu_item_t* item, int32_t old_value, int32_t new_value)
{
    (void)item;
    (void)old_value;

    /* 更新发射功率 */
    // CRSF_SetTxPower(new_value);
}

/**
 * @brief 获取当前状态
 */
menu_state_t Menu_GetState(void)
{
    return menu_context.state;
}

/**
 * @brief 获取当前菜单
 */
menu_item_t* Menu_GetCurrentMenu(void)
{
    return menu_context.current_menu;
}

/**
 * @brief 获取选中的菜单项
 */
menu_item_t* Menu_GetSelectedItem(void)
{
    return menu_context.selected_item;
}

/**
 * @brief 检查是否在编辑状态
 */
bool Menu_IsEditing(void)
{
    return menu_context.editing;
}

/**
 * @brief 检查是否需要刷新
 */
bool Menu_NeedRefresh(void)
{
    return menu_context.need_refresh;
}
