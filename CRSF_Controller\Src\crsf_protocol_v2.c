/**
 * @file crsf_protocol_v2.c
 * @brief CRSF协议状态机实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "crsf_protocol_v2.h"
#include "crsf_protocol.h"
#include "mixer.h"
#include "hal_drivers.h"

/* 全局变量定义 */
volatile crsf_state_t crsf_state = CRSF_STATE_IDLE;
volatile uint16_t mixer_runtime = 0;
volatile int32_t correction = 0;
volatile uint32_t updateInterval = CRSF_FRAME_PERIOD;

/* 私有变量 */
static uint8_t tx_packet[CRSF_MAX_PACKET_SIZE];
static uint8_t rx_buffer[CRSF_RX_BUFFER_SIZE];  // 使用原有的缓冲区大小
static uint16_t rx_tail = 0;  // DMA接收尾指针
static bool protocol_running = false;

/* 外部变量声明 (来自原有crsf.c) */
extern uint8_t crsf_rx_buffer[CRSF_RX_BUFFER_SIZE];
extern uint16_t crsf_rx_tail;

/* 私有函数声明 */
static void CBUF_Push(uint8_t data);
static uint8_t CBUF_Pop(void);
static uint16_t CBUF_Len(void);

/**
 * @brief CRSF协议初始化
 */
error_code_t CRSF_Protocol_Init(void)
{
    /* 初始化状态 */
    crsf_state = CRSF_STATE_IDLE;
    mixer_runtime = 0;
    correction = 0;
    updateInterval = CRSF_FRAME_PERIOD;
    protocol_running = false;
    
    /* 初始化接收缓冲区 */
    rx_head = rx_tail = 0;
    
    return ERR_OK;
}

/**
 * @brief 启动CRSF协议
 */
error_code_t CRSF_Protocol_Start(void)
{
    if (protocol_running) {
        return ERR_OK;
    }
    
    /* 启动定时器 */
    CLOCK_StartTimer(updateInterval, CRSF_SerialCallback);
    
    /* 设置状态 */
    crsf_state = CRSF_STATE_DATA0;
    protocol_running = true;
    
    return ERR_OK;
}

/**
 * @brief 停止CRSF协议
 */
error_code_t CRSF_Protocol_Stop(void)
{
    if (!protocol_running) {
        return ERR_OK;
    }
    
    /* 停止定时器 */
    CLOCK_StopTimer();
    
    /* 重置状态 */
    crsf_state = CRSF_STATE_IDLE;
    protocol_running = false;
    
    return ERR_OK;
}

/**
 * @brief CRSF串口回调函数 (状态机核心)
 */
uint16_t CRSF_SerialCallback(void)
{
    uint8_t length;
    
    switch (crsf_state) {
        case CRSF_STATE_DATA0:
            /* 触发混音器计算 */
            CLOCK_RunMixer();
            crsf_state = CRSF_STATE_DATA1;
            return mixer_runtime;
            
        case CRSF_STATE_DATA1:
            /* 检查混音器是否完成 */
            if (mixer_sync != MIX_DONE && mixer_runtime < MIXER_RUNTIME_MAX) {
                mixer_runtime += MIXER_RUNTIME_STEP;
            }
            
            /* 构建并发送数据包 */
            length = CRSF_BuildRCDataPacket(tx_packet);
            if (length > 0) {
                HAL_UART_Transmit_DMA(&huart1, tx_packet, length);
            }
            
            /* 接收数据处理移到主循环，避免中断阻塞 */
            /* CLOCK_RunUART(CRSF_ProcessTelemetry); */
            
            /* 返回下一状态 */
            crsf_state = CRSF_STATE_DATA0;
            return CRSF_GetUpdateInterval() - mixer_runtime;
            
        default:
            crsf_state = CRSF_STATE_DATA0;
            return CRSF_FRAME_PERIOD;
    }
}

/**
 * @brief 构建RC数据包
 */
uint8_t CRSF_BuildRCDataPacket(uint8_t* packet)
{
    if (!packet) {
        return 0;
    }
    
    /* 更新混音器输入 */
    MIXER_UpdateInputs();
    
    /* 构建CRSF RC数据包 */
    packet[0] = CRSF_ADDRESS_FLIGHT_CONTROLLER;
    packet[1] = 24;  // 数据长度
    packet[2] = CRSF_FRAMETYPE_RC_CHANNELS_PACKED;
    
    /* 打包通道数据 (11位精度) */
    uint32_t channels[16];
    for (uint8_t i = 0; i < 8; i++) {
        channels[i] = MIXER_GetOutput(i);
    }
    
    /* 11位打包算法 */
    uint8_t* data = &packet[3];
    data[0] = (uint8_t)(channels[0] & 0x07FF);
    data[1] = (uint8_t)((channels[0] & 0x07FF) >> 8 | (channels[1] & 0x07FF) << 3);
    data[2] = (uint8_t)((channels[1] & 0x07FF) >> 5 | (channels[2] & 0x07FF) << 6);
    data[3] = (uint8_t)((channels[2] & 0x07FF) >> 2);
    data[4] = (uint8_t)((channels[2] & 0x07FF) >> 10 | (channels[3] & 0x07FF) << 1);
    data[5] = (uint8_t)((channels[3] & 0x07FF) >> 7 | (channels[4] & 0x07FF) << 4);
    data[6] = (uint8_t)((channels[4] & 0x07FF) >> 4 | (channels[5] & 0x07FF) << 7);
    data[7] = (uint8_t)((channels[5] & 0x07FF) >> 1);
    data[8] = (uint8_t)((channels[5] & 0x07FF) >> 9 | (channels[6] & 0x07FF) << 2);
    data[9] = (uint8_t)((channels[6] & 0x07FF) >> 6 | (channels[7] & 0x07FF) << 5);
    data[10] = (uint8_t)((channels[7] & 0x07FF) >> 3);
    
    /* 计算CRC */
    uint8_t crc = 0;
    for (uint8_t i = 2; i < 24; i++) {
        crc = crc8_dvb_s2(crc, packet[i]);
    }
    packet[24] = crc;
    
    return 25;  // 总长度
}

/**
 * @brief 获取更新间隔
 */
uint32_t CRSF_GetUpdateInterval(void)
{
    if (correction == 0) {
        return updateInterval;
    }
    
    uint32_t update = updateInterval + correction;
    
    /* 限制在有效范围内 */
    if (update < CRSF_FRAME_PERIOD_MIN) {
        update = CRSF_FRAME_PERIOD_MIN;
    } else if (update > CRSF_FRAME_PERIOD_MAX) {
        update = CRSF_FRAME_PERIOD_MAX;
    }
    
    correction -= (update - updateInterval);
    return update;
}

/**
 * @brief 处理接收数据 (分片处理，避免阻塞)
 */
void CRSF_ProcessRxData(void)
{
    /* 计算可用数据长度 */
    uint16_t dma_pos = CRSF_RX_BUFFER_SIZE - __HAL_DMA_GET_COUNTER(huart1.hdmarx);

    /* 每次最多处理的字节数 */
    uint8_t processed_count = 0;
    const uint8_t MAX_BYTES_PER_CALL = 8;  // 限制处理量，避免阻塞

    while (crsf_rx_tail != dma_pos && processed_count < MAX_BYTES_PER_CALL) {
        uint8_t byte = crsf_rx_buffer[crsf_rx_tail];
        crsf_rx_tail = (crsf_rx_tail + 1) % CRSF_RX_BUFFER_SIZE;

        CRSF_ProcessByte(byte);
        processed_count++;
    }

    /* 注意：不再自动触发下次处理，由主循环定期调用 */
}

/**
 * @brief 快速检查是否有接收数据
 */
bool CRSF_HasRxData(void)
{
    uint16_t dma_pos = CRSF_RX_BUFFER_SIZE - __HAL_DMA_GET_COUNTER(huart1.hdmarx);
    return (crsf_rx_tail != dma_pos);
}

/**
 * @brief 获取接收缓冲区中的数据数量
 */
uint16_t CRSF_GetRxDataCount(void)
{
    uint16_t dma_pos = CRSF_RX_BUFFER_SIZE - __HAL_DMA_GET_COUNTER(huart1.hdmarx);
    return (dma_pos + CRSF_RX_BUFFER_SIZE - crsf_rx_tail) % CRSF_RX_BUFFER_SIZE;
}

/**
 * @brief 处理遥测数据 (在软件中断中调用)
 */
void CRSF_ProcessTelemetry(void)
{
    /* 调用原有的DMA接收处理函数 */
    CRSF_ProcessRxData();
}

/* 原有的DMA接收处理更高效，不需要额外的缓冲区函数 */

/**
 * @brief 状态查询函数
 */
crsf_state_t CRSF_GetState(void) { return crsf_state; }
bool CRSF_IsTransmitting(void) { return protocol_running; }
uint16_t CRSF_GetMixerRuntime(void) { return mixer_runtime; }
