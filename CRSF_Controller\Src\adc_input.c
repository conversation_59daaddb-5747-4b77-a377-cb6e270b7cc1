/**
 * @file adc_input.c
 * @brief ADC输入处理实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "adc_input.h"
#include "hal_drivers.h"

/* 全局变量定义 */
uint16_t adc_buffer[ADC_CHANNELS];
adc_calibration_t adc_calibration[ADC_CH_COUNT];
adc_config_t adc_config;
adc_status_t adc_status;

/* 私有变量 */
static uint16_t adc_filter_buffer[ADC_CH_COUNT][ADC_FILTER_MAX_SAMPLES];
static uint8_t adc_filter_index[ADC_CH_COUNT];
static bool adc_initialized = false;
static bool adc_conversion_complete = false;

/* 私有函数声明 */
static uint16_t ADC_ApplyAverageFilter(adc_channel_t channel, uint16_t new_value);
static uint16_t ADC_ApplyMedianFilter(adc_channel_t channel, uint16_t new_value);
static uint16_t ADC_ApplyLowPassFilter(adc_channel_t channel, uint16_t new_value);
static void ADC_InitDefaultCalibration(void);
static uint16_t ADC_MedianOf3(uint16_t a, uint16_t b, uint16_t c);

/**
 * @brief ADC输入初始化
 */
error_code_t ADC_Input_Init(void)
{
    if (adc_initialized) {
        return ERR_OK;
    }

    /* 初始化ADC缓冲区 */
    memset(adc_buffer, 0, sizeof(adc_buffer));
    memset(adc_filter_buffer, 0, sizeof(adc_filter_buffer));
    memset(adc_filter_index, 0, sizeof(adc_filter_index));
    memset(&adc_status, 0, sizeof(adc_status));

    /* 设置默认配置 */
    adc_config.filter_type = ADC_FILTER_AVERAGE;
    adc_config.filter_samples = ADC_FILTER_DEFAULT_SAMPLES;
    adc_config.update_rate_hz = ADC_SAMPLE_RATE;
    adc_config.auto_calibration = false;

    /* 初始化默认校准值 */
    ADC_InitDefaultCalibration();

    /* 初始化状态 */
    adc_status.calibration_active = false;
    adc_status.sample_count = 0;
    adc_status.last_update_time = 0;

    adc_initialized = true;
    return ERR_OK;
}

/**
 * @brief 启动ADC采集
 */
error_code_t ADC_Input_Start(void)
{
    if (!adc_initialized) {
        return ERR_NOT_READY;
    }

    /* 启动ADC DMA */
    error_code_t result = HAL_ADC_Start_DMA_Safe();
    if (result != ERR_OK) {
        return result;
    }

    return ERR_OK;
}

/**
 * @brief 停止ADC采集
 */
error_code_t ADC_Input_Stop(void)
{
    return HAL_ADC_Stop_DMA_Safe();
}

/**
 * @brief 获取原始ADC值
 */
uint16_t ADC_Input_GetRawValue(adc_channel_t channel)
{
    if (channel >= ADC_CH_COUNT) {
        return 0;
    }

    return adc_status.raw_values[channel];
}

/**
 * @brief 获取滤波后的ADC值
 */
uint16_t ADC_Input_GetFilteredValue(adc_channel_t channel)
{
    if (channel >= ADC_CH_COUNT) {
        return 0;
    }

    return adc_status.filtered_values[channel];
}

/**
 * @brief 获取校准后的ADC值
 */
uint16_t ADC_Input_GetCalibratedValue(adc_channel_t channel)
{
    if (channel >= ADC_CH_COUNT) {
        return 0;
    }

    return adc_status.calibrated_values[channel];
}

/**
 * @brief 获取所有通道值
 */
void ADC_Input_GetAllValues(rc_input_t* rc_input)
{
    if (rc_input == NULL) {
        return;
    }

    rc_input->ch1 = adc_status.calibrated_values[ADC_CH1];
    rc_input->ch2 = adc_status.calibrated_values[ADC_CH2];
    rc_input->ch3 = adc_status.calibrated_values[ADC_CH3];
    rc_input->ch4 = adc_status.calibrated_values[ADC_CH4];
    rc_input->swa = adc_status.calibrated_values[ADC_SWA];
    rc_input->swb = adc_status.calibrated_values[ADC_SWB];
    rc_input->vbat = adc_status.calibrated_values[ADC_VBAT];
    rc_input->bin = adc_status.calibrated_values[ADC_BIN];
    rc_input->vra = adc_status.calibrated_values[ADC_VRA];
    rc_input->vrb = adc_status.calibrated_values[ADC_VRB];
}

/**
 * @brief 获取三段开关位置
 */
switch_position_t ADC_Input_GetSwitchPosition(adc_channel_t channel)
{
    if (channel != ADC_SWA && channel != ADC_SWB) {
        return SWITCH_POS_UNKNOWN;
    }

    uint16_t raw_value = adc_status.raw_values[channel];
    adc_calibration_t* cal = &adc_calibration[channel];

    /* 计算与各位置的距离 */
    uint16_t dist_low = abs(raw_value - cal->min_value);
    uint16_t dist_mid = abs(raw_value - cal->center_value);
    uint16_t dist_high = abs(raw_value - cal->max_value);

    /* 找到最近的位置 */
    if (dist_low <= dist_mid && dist_low <= dist_high) {
        return SWITCH_POS_LOW;
    } else if (dist_mid <= dist_high) {
        return SWITCH_POS_MID;
    } else {
        return SWITCH_POS_HIGH;
    }
}

/**
 * @brief 获取开关位置字符串
 */
const char* ADC_Input_GetSwitchPositionString(switch_position_t pos)
{
    switch (pos) {
        case SWITCH_POS_LOW:    return "下位";
        case SWITCH_POS_MID:    return "中位";
        case SWITCH_POS_HIGH:   return "上位";
        default:                return "未知";
    }
}

/**
 * @brief 应用滤波器
 */
uint16_t ADC_Input_ApplyFilter(adc_channel_t channel, uint16_t new_value)
{
    if (channel >= ADC_CH_COUNT) {
        return new_value;
    }

    switch (adc_config.filter_type) {
        case ADC_FILTER_AVERAGE:
            return ADC_ApplyAverageFilter(channel, new_value);
        case ADC_FILTER_MEDIAN:
            return ADC_ApplyMedianFilter(channel, new_value);
        case ADC_FILTER_LOW_PASS:
            return ADC_ApplyLowPassFilter(channel, new_value);
        default:
            return new_value;
    }
}

/**
 * @brief 应用校准
 */
uint16_t ADC_Input_ApplyCalibration(adc_channel_t channel, uint16_t raw_value)
{
    if (channel >= ADC_CH_COUNT) {
        return raw_value;
    }

    adc_calibration_t* cal = &adc_calibration[channel];
    uint16_t result;

    /* 应用死区 */
    uint16_t deadband_value = ADC_Input_ApplyDeadband(raw_value, cal->center_value, cal->deadband);

    /* 线性插值校准 */
    if (deadband_value <= cal->center_value) {
        /* 下半部分 */
        result = MAP(deadband_value, cal->min_value, cal->center_value, 0, ADC_RESOLUTION/2);
    } else {
        /* 上半部分 */
        result = MAP(deadband_value, cal->center_value, cal->max_value, ADC_RESOLUTION/2, ADC_RESOLUTION-1);
    }

    /* 应用反向 */
    if (cal->reversed) {
        result = ADC_RESOLUTION - 1 - result;
    }

    return CONSTRAIN(result, 0, ADC_RESOLUTION-1);
}

/**
 * @brief 应用死区
 */
uint16_t ADC_Input_ApplyDeadband(uint16_t value, uint16_t center, uint16_t deadband)
{
    if (value >= center - deadband && value <= center + deadband) {
        return center;
    }
    return value;
}

/**
 * @brief ADC转换完成回调
 */
void ADC_Input_ConversionComplete(void)
{
    adc_conversion_complete = true;
    adc_status.sample_count++;
}

/**
 * @brief ADC任务函数
 */
void ADC_Input_Task(void* parameters)
{
    (void)parameters;

    if (!adc_conversion_complete) {
        return;
    }

    adc_conversion_complete = false;
    uint32_t current_time = millis();

    /* 更新原始值 */
    for (uint8_t i = 0; i < ADC_CH_COUNT; i++) {
        adc_status.raw_values[i] = adc_buffer[i];
        
        /* 应用滤波 */
        adc_status.filtered_values[i] = ADC_Input_ApplyFilter((adc_channel_t)i, adc_status.raw_values[i]);
        
        /* 应用校准 */
        adc_status.calibrated_values[i] = ADC_Input_ApplyCalibration((adc_channel_t)i, adc_status.filtered_values[i]);
    }

    adc_status.last_update_time = current_time;
}

/**
 * @brief 初始化默认校准值
 */
static void ADC_InitDefaultCalibration(void)
{
    for (uint8_t i = 0; i < ADC_CH_COUNT; i++) {
        adc_calibration[i].min_value = ADC_DEFAULT_MIN;
        adc_calibration[i].center_value = ADC_DEFAULT_CENTER;
        adc_calibration[i].max_value = ADC_DEFAULT_MAX;
        adc_calibration[i].reversed = false;
        adc_calibration[i].deadband = ADC_DEFAULT_DEADBAND;
    }
}

/**
 * @brief 平均滤波器
 */
static uint16_t ADC_ApplyAverageFilter(adc_channel_t channel, uint16_t new_value)
{
    uint8_t samples = MIN(adc_config.filter_samples, ADC_FILTER_MAX_SAMPLES);
    uint8_t index = adc_filter_index[channel];
    
    /* 存储新值 */
    adc_filter_buffer[channel][index] = new_value;
    adc_filter_index[channel] = (index + 1) % samples;
    
    /* 计算平均值 */
    uint32_t sum = 0;
    for (uint8_t i = 0; i < samples; i++) {
        sum += adc_filter_buffer[channel][i];
    }
    
    return sum / samples;
}

/**
 * @brief 中值滤波器
 */
static uint16_t ADC_ApplyMedianFilter(adc_channel_t channel, uint16_t new_value)
{
    uint8_t index = adc_filter_index[channel];
    
    /* 存储新值 */
    adc_filter_buffer[channel][index] = new_value;
    adc_filter_index[channel] = (index + 1) % 3;
    
    /* 3点中值滤波 */
    return ADC_MedianOf3(adc_filter_buffer[channel][0], 
                        adc_filter_buffer[channel][1], 
                        adc_filter_buffer[channel][2]);
}

/**
 * @brief 低通滤波器
 */
static uint16_t ADC_ApplyLowPassFilter(adc_channel_t channel, uint16_t new_value)
{
    /* 简单的一阶低通滤波器 */
    static uint16_t last_values[ADC_CH_COUNT] = {0};
    
    /* 滤波系数 (0.1 = 10% 新值, 90% 旧值) */
    uint16_t alpha = 26; // 0.1 * 256
    
    last_values[channel] = ((256 - alpha) * last_values[channel] + alpha * new_value) / 256;
    
    return last_values[channel];
}

/**
 * @brief 三个数的中值
 */
static uint16_t ADC_MedianOf3(uint16_t a, uint16_t b, uint16_t c)
{
    if (a > b) {
        if (b > c) return b;
        else if (a > c) return c;
        else return a;
    } else {
        if (a > c) return a;
        else if (b > c) return c;
        else return b;
    }
}

/**
 * @brief 获取状态信息
 */
adc_status_t* ADC_Input_GetStatus(void)
{
    return &adc_status;
}

/**
 * @brief 检查是否已校准
 */
bool ADC_Input_IsCalibrated(adc_channel_t channel)
{
    if (channel >= ADC_CH_COUNT) {
        return false;
    }
    
    adc_calibration_t* cal = &adc_calibration[channel];
    return (cal->min_value != cal->max_value);
}
