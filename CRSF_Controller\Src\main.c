/**
 * @file main.c
 * @brief 主程序文件
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "config.h"
#include "hal_drivers.h"
#include "task_scheduler.h"
#include "crsf.h"
#include "adc_input.h"
#include "button_input.h"
#include "oled_display.h"
#include "menu_system.h"
#include "elrs_config.h"
#include "buzzer_vibrator.h"
#include "eeprom.h"
#include "power_management.h"
#include "calibration.h"
#include "usb_cdc.h"

/* 私有变量 */
static rc_input_t rc_input;
static button_input_t button_input;
static uint32_t last_crsf_send_time = 0;
static uint32_t crsf_send_interval = 10000; // 10ms = 100Hz

/* 私有函数声明 */
static void System_Init(void);
static void Tasks_Init(void);
static void Main_Loop(void);

/* 任务函数声明 */
static void CRSF_Task(void* parameters);
static void UI_Task(void* parameters);

/**
 * @brief 主函数
 */
int main(void)
{
    /* 系统初始化 */
    System_Init();
    
    /* 任务初始化 */
    Tasks_Init();
    
    /* 显示启动画面 */
    OLED_ShowSplashScreen("CRSF Controller", "STM32F072 v1.0");
    delay_ms(2000);

    /* 检查是否需要开机校准 */
    if (Calibration_IsNeeded()) {
        Calibration_StartBoot();

        /* 校准循环 */
        while (!Calibration_IsBootComplete()) {
            Calibration_ProcessBoot();
            delay_ms(50);

            /* 处理按键 */
            Button_Input_Scan();
            if (Button_Input_GetEvent(BUTTON_KEY1) == BUTTON_EVENT_PRESS) {
                break;  // 按退出键跳过校准
            }
        }

        /* 校准完成，延时显示结果 */
        delay_ms(2000);
    }

    /* 启动任务调度器 */
    TaskScheduler_Start();

    /* 主循环 */
    Main_Loop();
    
    return 0;
}

/**
 * @brief 系统初始化
 */
static void System_Init(void)
{
    /* HAL库初始化 */
    if (HAL_System_Init() != ERR_OK) {
        Error_Handler();
    }
    
    /* OLED显示初始化 */
    if (OLED_Init() != ERR_OK) {
        Error_Handler();
    }
    
    /* ADC输入初始化 */
    if (ADC_Input_Init() != ERR_OK) {
        Error_Handler();
    }
    
    /* 按键输入初始化 */
    if (Button_Input_Init() != ERR_OK) {
        Error_Handler();
    }
    
    /* CRSF协议初始化 */
    if (CRSF_Init() != ERR_OK) {
        Error_Handler();
    }
    
    /* 菜单系统初始化 */
    if (Menu_Init() != ERR_OK) {
        Error_Handler();
    }
    
    /* ELRS配置初始化 */
    if (ELRS_Config_Init() != ERR_OK) {
        Error_Handler();
    }

    /* 蜂鸣器和震动电机初始化 */
    if (Buzzer_Init() != ERR_OK) {
        Error_Handler();
    }

    if (Vibrator_Init() != ERR_OK) {
        Error_Handler();
    }

    /* EEPROM初始化 */
    if (EEPROM_Init() != ERR_OK) {
        Error_Handler();
    }

    /* 电源管理初始化 */
    if (Power_Init() != ERR_OK) {
        Error_Handler();
    }

    /* USB CDC初始化 */
    if (USB_CDC_Init() != ERR_OK) {
        Error_Handler();
    }

    /* 启动外设 */
    ADC_Input_Start();
    CRSF_Start();
    ELRS_Config_Start();
    USB_CDC_Start();

    /* 播放开机音效 */
    Feedback_Startup();

    DEBUG_PRINT("System initialized successfully\r\n");

    /* USB调试信息 */
    USB_CDC_Printf("CRSF Controller v1.0 Started\r\n");
    USB_CDC_Printf("Build: %s %s\r\n", __DATE__, __TIME__);
}

/**
 * @brief 任务初始化
 */
static void Tasks_Init(void)
{
    /* 初始化任务调度器 */
    if (TaskScheduler_Init() != ERR_OK) {
        Error_Handler();
    }
    
    /* 创建CRSF任务 */
    uint8_t crsf_task_id = TaskScheduler_CreateTask(
        "CRSF",
        CRSF_Task,
        NULL,
        TASK_PRIORITY_HIGH,
        TASK_PERIOD_10MS
    );
    
    if (crsf_task_id == TASK_ID_INVALID) {
        Error_Handler();
    }
    
    /* 创建UI任务 */
    uint8_t ui_task_id = TaskScheduler_CreateTask(
        "UI",
        UI_Task,
        NULL,
        TASK_PRIORITY_NORMAL,
        TASK_PERIOD_20MS
    );
    
    if (ui_task_id == TASK_ID_INVALID) {
        Error_Handler();
    }
    
    /* 创建ADC任务 */
    uint8_t adc_task_id = TaskScheduler_CreateTask(
        "ADC",
        ADC_Input_Task,
        NULL,
        TASK_PRIORITY_NORMAL,
        TASK_PERIOD_10MS
    );
    
    if (adc_task_id == TASK_ID_INVALID) {
        Error_Handler();
    }
    
    /* 创建按键任务 */
    uint8_t button_task_id = TaskScheduler_CreateTask(
        "Button",
        Button_Input_Task,
        NULL,
        TASK_PRIORITY_NORMAL,
        TASK_PERIOD_10MS
    );
    
    if (button_task_id == TASK_ID_INVALID) {
        Error_Handler();
    }
    
    /* 创建菜单任务 */
    uint8_t menu_task_id = TaskScheduler_CreateTask(
        "Menu",
        Menu_Task,
        NULL,
        TASK_PRIORITY_LOW,
        TASK_PERIOD_50MS
    );
    
    if (menu_task_id == TASK_ID_INVALID) {
        Error_Handler();
    }
    
    /* 创建ELRS配置任务 */
    uint8_t elrs_task_id = TaskScheduler_CreateTask(
        "ELRS",
        ELRS_Config_Task,
        NULL,
        TASK_PRIORITY_LOW,
        TASK_PERIOD_100MS
    );

    if (elrs_task_id == TASK_ID_INVALID) {
        Error_Handler();
    }

    /* 创建蜂鸣器任务 */
    uint8_t buzzer_task_id = TaskScheduler_CreateTask(
        "Buzzer",
        Buzzer_Task,
        NULL,
        TASK_PRIORITY_NORMAL,
        TASK_PERIOD_10MS
    );

    if (buzzer_task_id == TASK_ID_INVALID) {
        Error_Handler();
    }

    /* 创建震动电机任务 */
    uint8_t vibrator_task_id = TaskScheduler_CreateTask(
        "Vibrator",
        Vibrator_Task,
        NULL,
        TASK_PRIORITY_NORMAL,
        TASK_PERIOD_10MS
    );

    if (vibrator_task_id == TASK_ID_INVALID) {
        Error_Handler();
    }

    /* 创建电源管理任务 */
    uint8_t power_task_id = TaskScheduler_CreateTask(
        "Power",
        Power_Task,
        NULL,
        TASK_PRIORITY_HIGH,
        TASK_PERIOD_100MS
    );

    if (power_task_id == TASK_ID_INVALID) {
        Error_Handler();
    }

    /* 创建USB CDC任务 */
    uint8_t usb_task_id = TaskScheduler_CreateTask(
        "USB_CDC",
        USB_CDC_Task,
        NULL,
        TASK_PRIORITY_NORMAL,
        TASK_PERIOD_10MS
    );

    if (usb_task_id == TASK_ID_INVALID) {
        Error_Handler();
    }

    DEBUG_PRINT("Tasks initialized successfully\r\n");
}

/**
 * @brief 主循环
 */
static void Main_Loop(void)
{
    while (1) {
        /* 任务调度器会在TIM6中断中调用 */
        /* 这里可以执行一些低优先级的后台任务 */
        
        /* 喂看门狗 */
        // HAL_IWDG_Refresh();
        
        /* 进入低功耗模式 */
        __WFI();
    }
}

/**
 * @brief CRSF任务
 */
static void CRSF_Task(void* parameters)
{
    (void)parameters;
    
    /* 处理接收数据 */
    CRSF_ProcessRxData();
    
    /* 检查链路状态 */
    CRSF_CheckLinkState();
    
    /* 发送通道数据 */
    uint32_t current_time = micros();
    if (current_time - last_crsf_send_time >= crsf_send_interval) {
        /* 获取ADC输入 */
        ADC_Input_GetAllValues(&rc_input);
        
        /* 发送CRSF通道数据 */
        CRSF_SendChannels(&rc_input);
        
        last_crsf_send_time = current_time;
    }
}

/**
 * @brief UI任务
 */
static void UI_Task(void* parameters)
{
    (void)parameters;
    
    /* 获取按键输入 */
    Button_Input_GetAllStates(&button_input);
    
    /* 处理按键事件 */
    for (uint8_t i = 0; i < BUTTON_COUNT; i++) {
        button_event_t event = Button_Input_GetEvent((button_id_t)i);
        if (event != BUTTON_EVENT_NONE) {
            Menu_HandleButton((button_id_t)i, event);
        }
    }
    
    /* 更新菜单显示 */
    Menu_UpdateDisplay();
}

/**
 * @brief TIM6中断回调 - 任务调度器时钟节拍
 */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
    if (htim->Instance == TIM6) {
        TaskScheduler_Tick();
    }
}

/**
 * @brief UART接收完成回调
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1) {
        /* CRSF数据接收完成 */
        // 在DMA模式下，数据会自动写入缓冲区
        // CRSF_ProcessRxData()会在任务中处理
    }
}

/**
 * @brief UART发送完成回调
 */
void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1) {
        /* CRSF数据发送完成 */
        // 可以在这里设置发送完成标志
    }
}

/**
 * @brief ADC转换完成回调
 */
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef *hadc)
{
    if (hadc->Instance == ADC1) {
        ADC_Input_ConversionComplete();
    }
}

/**
 * @brief ADC转换半完成回调
 */
void HAL_ADC_ConvHalfCpltCallback(ADC_HandleTypeDef *hadc)
{
    if (hadc->Instance == ADC1) {
        ADC_Input_ConversionHalfComplete();
    }
}

/**
 * @brief CRSF回调函数实现
 */
void CRSF_OnDeviceInfo(crsf_device_t* device)
{
    /* 传递给ELRS配置管理器 */
    ELRS_Config_OnDeviceInfo(device);
    
    DEBUG_PRINT("Device found: %s (0x%02X)\r\n", device->name, device->address);
}

void CRSF_OnLinkStatistics(crsf_link_statistics_t* stats)
{
    /* 更新菜单显示 */
    Menu_SetLinkQuality(stats->uplink_Link_quality, stats->uplink_RSSI_1);
    Menu_SetRfMode(stats->rf_Mode);
    
    DEBUG_PRINT("Link Quality: %d%%, RSSI: %ddBm\r\n", 
                stats->uplink_Link_quality, stats->uplink_RSSI_1);
}

void CRSF_OnBatteryInfo(crsf_battery_sensor_t* battery)
{
    /* 更新电池电量显示 */
    uint8_t battery_percent = battery->remaining;
    Menu_SetBatteryLevel(battery_percent);
    
    DEBUG_PRINT("Battery: %d%%, Voltage: %dmV\r\n", 
                battery->remaining, battery->voltage);
}

/**
 * @brief 按键回调函数
 */
void Button_Callback(button_id_t button, button_event_t event)
{
    /* 按键事件会在UI任务中处理 */
    DEBUG_PRINT("Button %d event %d\r\n", button, event);
}

#if DEBUG_ENABLED
/**
 * @brief 重定向printf到USB CDC
 */
int _write(int file, char *ptr, int len)
{
    (void)file;

    /* 优先使用USB CDC输出 */
    if (USB_CDC_IsConfigured()) {
        USB_CDC_Transmit((uint8_t*)ptr, len);
    } else {
        /* USB未连接时使用UART2输出 */
        HAL_UART_Transmit(&huart2, (uint8_t*)ptr, len, HAL_MAX_DELAY);
    }

    return len;
}
#endif
