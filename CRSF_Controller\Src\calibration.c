/**
 * @file calibration.c
 * @brief 校准功能实现
 * <AUTHOR> Controller Team
 * @date 2024
 */

#include "calibration.h"
#include "adc_input.h"
#include "button_input.h"
#include "oled_display.h"
#include "buzzer_vibrator.h"
#include "eeprom.h"

/* 全局变量定义 */
calibration_context_t calib_context;

/* 私有变量 */
static uint16_t stick_min_values[4];       // 摇杆最小值
static uint16_t stick_max_values[4];       // 摇杆最大值
static uint16_t stick_center_values[4];    // 摇杆中心值
static uint16_t switch_positions[2][3];    // 开关位置值 [开关][位置]
static uint16_t pot_min_values[2];         // 电位器最小值 [VRA, VRB]
static uint16_t pot_max_values[2];         // 电位器最大值 [VRA, VRB]
static uint16_t pot_center_values[2];      // 电位器中心值 [VRA, VRB]

/* 私有函数声明 */
static void Calibration_InitContext(void);
static void Calibration_ProcessStickCalibration(void);
static void Calibration_ProcessSwitchCalibration(void);
static void Calibration_ProcessPotentiometerCalibration(void);
static void Calibration_CollectSample(adc_channel_t channel, uint16_t* target);
static bool Calibration_CheckStickMovement(void);
static bool Calibration_CheckPotentiometerMovement(void);

/**
 * @brief 检查是否需要校准
 */
bool Calibration_IsNeeded(void)
{
    /* 检查EEPROM中是否有有效的校准数据 */
    calibration_data_t calib_data;
    if (EEPROM_LoadCalibrationData(&calib_data) != ERR_OK) {
        return true;  // 无法加载校准数据，需要校准
    }
    
    /* 检查校准数据是否合理 */
    for (uint8_t i = 0; i < 4; i++) {  // 检查摇杆通道
        if (calib_data.adc_calibration[i].min_value >= calib_data.adc_calibration[i].max_value) {
            return true;  // 校准数据异常
        }
    }
    
    return false;  // 校准数据正常
}

/**
 * @brief 开始开机校准
 */
error_code_t Calibration_StartBoot(void)
{
    Calibration_InitContext();
    calib_context.state = CALIB_STATE_CENTER;
    calib_context.step = CALIB_STEP_CENTER_STICKS;
    calib_context.auto_mode = true;
    
    /* 显示校准界面 */
    Calibration_ShowBootScreen();
    
    /* 播放提示音 */
    Buzzer_PlaySound(SOUND_BEEP_DOUBLE);
    
    return ERR_OK;
}

/**
 * @brief 处理开机校准
 */
void Calibration_ProcessBoot(void)
{
    if (calib_context.state == CALIB_STATE_IDLE) {
        return;
    }
    
    switch (calib_context.step) {
        case CALIB_STEP_CENTER_STICKS:
            Calibration_ProcessStickCalibration();
            break;

        case CALIB_STEP_MOVE_STICKS:
            if (Calibration_CheckStickMovement()) {
                calib_context.step = CALIB_STEP_CENTER_POTS;
                calib_context.step_start_time = millis();
                Buzzer_PlaySound(SOUND_BEEP_SHORT);
            }
            break;

        case CALIB_STEP_CENTER_POTS:
        case CALIB_STEP_MOVE_POTS:
            Calibration_ProcessPotentiometerCalibration();
            break;

        case CALIB_STEP_SWITCH_LOW:
        case CALIB_STEP_SWITCH_MID:
        case CALIB_STEP_SWITCH_HIGH:
            Calibration_ProcessSwitchCalibration();
            break;
            
        case CALIB_STEP_SAVE:
            Calibration_SaveData();
            calib_context.step = CALIB_STEP_DONE;
            Buzzer_PlaySound(SOUND_SUCCESS);
            break;
            
        case CALIB_STEP_DONE:
            calib_context.state = CALIB_STATE_COMPLETE;
            break;
            
        default:
            break;
    }
    
    /* 更新显示 */
    Calibration_UpdateDisplay();
}

/**
 * @brief 处理摇杆校准
 */
static void Calibration_ProcessStickCalibration(void)
{
    static uint32_t sample_start_time = 0;
    uint32_t current_time = millis();
    
    if (calib_context.step == CALIB_STEP_CENTER_STICKS) {
        /* 检查摇杆是否居中 */
        if (Calibration_IsStickCentered()) {
            if (sample_start_time == 0) {
                sample_start_time = current_time;
                calib_context.sample_count = 0;
                calib_context.sample_sum = 0;
            }
            
            /* 采样中心位置 */
            if (current_time - sample_start_time < CALIB_SAMPLE_TIME_MS) {
                for (uint8_t i = 0; i < 4; i++) {
                    stick_center_values[i] += ADC_Input_GetRawValue(i);
                }
                calib_context.sample_count++;
            } else {
                /* 计算平均值 */
                for (uint8_t i = 0; i < 4; i++) {
                    stick_center_values[i] /= calib_context.sample_count;
                    stick_min_values[i] = stick_center_values[i];
                    stick_max_values[i] = stick_center_values[i];
                }
                
                /* 进入下一步 */
                calib_context.step = CALIB_STEP_MOVE_STICKS;
                calib_context.step_start_time = current_time;
                sample_start_time = 0;
                Buzzer_PlaySound(SOUND_BEEP_SHORT);
            }
        } else {
            sample_start_time = 0;  // 重置采样
        }
    }
}

/**
 * @brief 检查摇杆移动
 */
static bool Calibration_CheckStickMovement(void)
{
    static uint32_t movement_start_time = 0;
    uint32_t current_time = millis();
    bool all_moved = true;
    
    /* 持续更新最小最大值 */
    for (uint8_t i = 0; i < 4; i++) {
        uint16_t current_value = ADC_Input_GetRawValue(i);
        if (current_value < stick_min_values[i]) {
            stick_min_values[i] = current_value;
        }
        if (current_value > stick_max_values[i]) {
            stick_max_values[i] = current_value;
        }
        
        /* 检查是否有足够的行程 */
        uint16_t range = stick_max_values[i] - stick_min_values[i];
        if (range < CALIB_MOVEMENT_THRESHOLD * 2) {
            all_moved = false;
        }
    }
    
    if (all_moved) {
        if (movement_start_time == 0) {
            movement_start_time = current_time;
        } else if (current_time - movement_start_time > 2000) {  // 保持2秒
            return true;
        }
    } else {
        movement_start_time = 0;
    }
    
    return false;
}

/**
 * @brief 处理开关校准
 */
static void Calibration_ProcessSwitchCalibration(void)
{
    static uint32_t stable_time = 0;
    uint32_t current_time = millis();
    
    uint8_t switch_index = (calib_context.step == CALIB_STEP_SWITCH_LOW) ? 0 :
                          (calib_context.step == CALIB_STEP_SWITCH_MID) ? 1 : 2;
    
    /* 检查开关位置是否稳定 */
    uint16_t swa_value = ADC_Input_GetRawValue(ADC_SWA);
    uint16_t swb_value = ADC_Input_GetRawValue(ADC_SWB);
    
    static uint16_t last_swa = 0, last_swb = 0;
    
    if (abs(swa_value - last_swa) < CALIB_SWITCH_TOLERANCE && 
        abs(swb_value - last_swb) < CALIB_SWITCH_TOLERANCE) {
        
        if (stable_time == 0) {
            stable_time = current_time;
        } else if (current_time - stable_time > 1000) {  // 稳定1秒
            /* 记录开关位置 */
            switch_positions[0][switch_index] = swa_value;
            switch_positions[1][switch_index] = swb_value;
            
            /* 进入下一步 */
            if (calib_context.step < CALIB_STEP_SWITCH_HIGH) {
                calib_context.step++;
            } else {
                calib_context.step = CALIB_STEP_SAVE;
            }
            
            stable_time = 0;
            Buzzer_PlaySound(SOUND_BEEP_SHORT);
        }
    } else {
        stable_time = 0;
    }
    
    last_swa = swa_value;
    last_swb = swb_value;
}

/**
 * @brief 处理电位器校准
 */
static void Calibration_ProcessPotentiometerCalibration(void)
{
    static uint32_t stable_time = 0;
    uint32_t current_time = millis();

    /* 读取电位器当前值 */
    uint16_t vra_value = ADC_Input_GetRawValue(ADC_VRA);
    uint16_t vrb_value = ADC_Input_GetRawValue(ADC_VRB);

    if (calib_context.step == CALIB_STEP_CENTER_POTS) {
        /* 电位器居中校准 */
        static uint16_t last_vra = 0, last_vrb = 0;

        if (abs(vra_value - last_vra) < CALIB_STICK_TOLERANCE &&
            abs(vrb_value - last_vrb) < CALIB_STICK_TOLERANCE) {

            if (stable_time == 0) {
                stable_time = current_time;
            } else if (current_time - stable_time > 2000) {  // 稳定2秒
                /* 记录中心位置 */
                pot_center_values[0] = vra_value;
                pot_center_values[1] = vrb_value;

                /* 初始化最大最小值 */
                pot_min_values[0] = pot_max_values[0] = vra_value;
                pot_min_values[1] = pot_max_values[1] = vrb_value;

                /* 进入下一步 */
                calib_context.step = CALIB_STEP_MOVE_POTS;
                calib_context.step_start_time = current_time;
                stable_time = 0;
                Buzzer_PlaySound(SOUND_BEEP_SHORT);
            }
        } else {
            stable_time = 0;
        }

        last_vra = vra_value;
        last_vrb = vrb_value;

    } else if (calib_context.step == CALIB_STEP_MOVE_POTS) {
        /* 电位器全行程校准 */

        /* 更新最大最小值 */
        if (vra_value < pot_min_values[0]) pot_min_values[0] = vra_value;
        if (vra_value > pot_max_values[0]) pot_max_values[0] = vra_value;
        if (vrb_value < pot_min_values[1]) pot_min_values[1] = vrb_value;
        if (vrb_value > pot_max_values[1]) pot_max_values[1] = vrb_value;

        /* 检查是否完成全行程校准 */
        if (Calibration_CheckPotentiometerMovement()) {
            calib_context.step = CALIB_STEP_SWITCH_LOW;
            calib_context.step_start_time = current_time;
            Buzzer_PlaySound(SOUND_BEEP_SHORT);
        }
    }
}

/**
 * @brief 检查电位器是否完成全行程移动
 */
static bool Calibration_CheckPotentiometerMovement(void)
{
    static uint32_t movement_start_time = 0;
    uint32_t current_time = millis();

    /* 检查电位器行程是否足够 */
    uint16_t vra_range = pot_max_values[0] - pot_min_values[0];
    uint16_t vrb_range = pot_max_values[1] - pot_min_values[1];

    bool vra_sufficient = vra_range > CALIB_MIN_RANGE;
    bool vrb_sufficient = vrb_range > CALIB_MIN_RANGE;

    if (vra_sufficient && vrb_sufficient) {
        if (movement_start_time == 0) {
            movement_start_time = current_time;
        } else if (current_time - movement_start_time > 2000) {  // 稳定2秒
            movement_start_time = 0;
            return true;
        }
    } else {
        movement_start_time = 0;
    }

    return false;
}

/**
 * @brief 检查摇杆是否居中
 */
bool Calibration_IsStickCentered(void)
{
    for (uint8_t i = 0; i < 4; i++) {
        uint16_t value = ADC_Input_GetRawValue(i);
        /* 检查是否在中心附近 */
        if (value < (ADC_RESOLUTION/2 - CALIB_CENTER_TOLERANCE) || 
            value > (ADC_RESOLUTION/2 + CALIB_CENTER_TOLERANCE)) {
            return false;
        }
    }
    return true;
}

/**
 * @brief 保存校准数据
 */
void Calibration_SaveData(void)
{
    calibration_data_t calib_data;
    
    /* 设置摇杆校准数据 */
    for (uint8_t i = 0; i < 4; i++) {
        calib_data.adc_calibration[i].min_value = stick_min_values[i];
        calib_data.adc_calibration[i].center_value = stick_center_values[i];
        calib_data.adc_calibration[i].max_value = stick_max_values[i];
        calib_data.adc_calibration[i].reversed = false;
        calib_data.adc_calibration[i].deadband = ADC_DEFAULT_DEADBAND;
    }
    
    /* 设置开关校准数据 */
    for (uint8_t i = 0; i < 2; i++) {
        uint8_t ch = ADC_SWA + i;
        calib_data.adc_calibration[ch].min_value = switch_positions[i][0];      // 下位
        calib_data.adc_calibration[ch].center_value = switch_positions[i][1];   // 中位
        calib_data.adc_calibration[ch].max_value = switch_positions[i][2];      // 上位
        calib_data.adc_calibration[ch].reversed = false;
        calib_data.adc_calibration[ch].deadband = 0;  // 开关无死区
    }

    /* 设置电位器校准数据 */
    for (uint8_t i = 0; i < 2; i++) {
        uint8_t ch = ADC_VRA + i;  // ADC_VRA=6, ADC_VRB=7
        calib_data.adc_calibration[ch].min_value = pot_min_values[i];
        calib_data.adc_calibration[ch].center_value = pot_center_values[i];
        calib_data.adc_calibration[ch].max_value = pot_max_values[i];
        calib_data.adc_calibration[ch].reversed = false;
        calib_data.adc_calibration[ch].deadband = 5;  // 电位器小死区
    }
    
    /* 保存到EEPROM */
    EEPROM_SaveCalibrationData(&calib_data);
    
    /* 应用校准数据到ADC模块 */
    for (uint8_t i = 0; i < ADC_CH_COUNT; i++) {
        adc_calibration[i] = calib_data.adc_calibration[i];
    }
}

/**
 * @brief 显示开机校准界面
 */
void Calibration_ShowBootScreen(void)
{
    OLED_Clear();
    OLED_SetFont(FONT_SIZE_6x8);
    OLED_WriteStringAligned(0, "遥控器校准", OLED_ALIGN_CENTER);
    OLED_DrawHLine(0, 8, OLED_WIDTH, true);
    OLED_Update();
}

/**
 * @brief 更新校准显示
 */
void Calibration_UpdateDisplay(void)
{
    OLED_Clear();
    OLED_SetFont(FONT_SIZE_6x8);
    
    /* 显示标题 */
    OLED_WriteStringAligned(0, "遥控器校准", OLED_ALIGN_CENTER);
    OLED_DrawHLine(0, 8, OLED_WIDTH, true);
    
    /* 显示当前步骤 */
    OLED_SetCursor(0, 16);
    OLED_WriteString(Calibration_GetStepText());
    
    /* 显示指令 */
    OLED_SetCursor(0, 32);
    OLED_WriteString(Calibration_GetInstructionText());
    
    /* 显示进度 */
    uint8_t progress = Calibration_GetProgress();
    OLED_DrawProgressBar(0, 50, OLED_WIDTH, 8, progress, 100);
    
    OLED_Update();
}

/**
 * @brief 获取步骤文本
 */
const char* Calibration_GetStepText(void)
{
    switch (calib_context.step) {
        case CALIB_STEP_CENTER_STICKS: return "步骤1: 摇杆居中";
        case CALIB_STEP_MOVE_STICKS:   return "步骤2: 摇杆全行程";
        case CALIB_STEP_CENTER_POTS:   return "步骤3: 电位器居中";
        case CALIB_STEP_MOVE_POTS:     return "步骤4: 电位器全行程";
        case CALIB_STEP_SWITCH_LOW:    return "步骤5: 开关下位";
        case CALIB_STEP_SWITCH_MID:    return "步骤6: 开关中位";
        case CALIB_STEP_SWITCH_HIGH:   return "步骤7: 开关上位";
        case CALIB_STEP_SAVE:          return "步骤8: 保存数据";
        case CALIB_STEP_DONE:          return "校准完成";
        default:                       return "校准中...";
    }
}

/**
 * @brief 获取指令文本
 */
const char* Calibration_GetInstructionText(void)
{
    switch (calib_context.step) {
        case CALIB_STEP_CENTER_STICKS: return "将摇杆置于中心位置";
        case CALIB_STEP_MOVE_STICKS:   return "转动摇杆到各个极限位置";
        case CALIB_STEP_CENTER_POTS:   return "将电位器VRA/VRB置于中心位置";
        case CALIB_STEP_MOVE_POTS:     return "转动电位器到各个极限位置";
        case CALIB_STEP_SWITCH_LOW:    return "将开关SWA/SWB拨到下位";
        case CALIB_STEP_SWITCH_MID:    return "将开关SWA/SWB拨到中位";
        case CALIB_STEP_SWITCH_HIGH:   return "将开关SWA/SWB拨到上位";
        case CALIB_STEP_SAVE:          return "正在保存校准数据...";
        case CALIB_STEP_DONE:          return "按任意键继续";
        default:                       return "";
    }
}

/**
 * @brief 获取校准进度
 */
uint8_t Calibration_GetProgress(void)
{
    return (calib_context.step * 100) / CALIB_STEP_DONE;
}

/**
 * @brief 初始化校准上下文
 */
static void Calibration_InitContext(void)
{
    memset(&calib_context, 0, sizeof(calib_context));
    memset(stick_min_values, 0, sizeof(stick_min_values));
    memset(stick_max_values, 0, sizeof(stick_max_values));
    memset(stick_center_values, 0, sizeof(stick_center_values));
    memset(switch_positions, 0, sizeof(switch_positions));
    memset(pot_min_values, 0, sizeof(pot_min_values));
    memset(pot_max_values, 0, sizeof(pot_max_values));
    memset(pot_center_values, 0, sizeof(pot_center_values));
}

/**
 * @brief 开始菜单校准
 */
error_code_t Calibration_StartMenu(void)
{
    Calibration_InitContext();
    calib_context.state = CALIB_STATE_CENTER;
    calib_context.step = CALIB_STEP_CENTER_STICKS;
    calib_context.auto_mode = false;  // 手动模式

    return ERR_OK;
}

/**
 * @brief 处理菜单校准
 */
void Calibration_ProcessMenu(void)
{
    /* 与开机校准相同的处理逻辑 */
    Calibration_ProcessBoot();
}

/**
 * @brief 菜单校准是否完成
 */
bool Calibration_IsMenuComplete(void)
{
    return calib_context.state == CALIB_STATE_COMPLETE;
}

/**
 * @brief 取消校准
 */
void Calibration_Cancel(void)
{
    calib_context.state = CALIB_STATE_IDLE;
    Buzzer_PlaySound(SOUND_BEEP_SHORT);
}

/**
 * @brief 下一步
 */
void Calibration_NextStep(void)
{
    if (calib_context.step < CALIB_STEP_DONE) {
        calib_context.step++;
        calib_context.step_start_time = millis();
        Buzzer_PlaySound(SOUND_BEEP_SHORT);
    }
}

/**
 * @brief 上一步
 */
void Calibration_PrevStep(void)
{
    if (calib_context.step > CALIB_STEP_INIT) {
        calib_context.step--;
        calib_context.step_start_time = millis();
        Buzzer_PlaySound(SOUND_BEEP_SHORT);
    }
}

/**
 * @brief 显示菜单校准界面
 */
void Calibration_ShowMenuScreen(void)
{
    OLED_Clear();
    OLED_SetFont(FONT_SIZE_6x8);
    OLED_WriteStringAligned(0, "菜单校准", OLED_ALIGN_CENTER);
    OLED_DrawHLine(0, 8, OLED_WIDTH, true);

    /* 显示操作提示 */
    OLED_SetCursor(0, 55);
    OLED_WriteString("上/下:步骤 确定:下一步 退出:取消");

    OLED_Update();
}

/**
 * @brief 采样通道数据
 */
void Calibration_SampleChannel(adc_channel_t channel)
{
    /* 简单实现：直接获取当前值 */
    uint16_t value = ADC_Input_GetRawValue(channel);

    if (channel < 4) {  // 摇杆通道
        switch (calib_context.step) {
            case CALIB_STEP_CENTER_STICKS:
                stick_center_values[channel] = value;
                break;
            case CALIB_STEP_MOVE_STICKS:
                if (value < stick_min_values[channel]) {
                    stick_min_values[channel] = value;
                }
                if (value > stick_max_values[channel]) {
                    stick_max_values[channel] = value;
                }
                break;
            default:
                break;
        }
    } else if (channel == ADC_VRA || channel == ADC_VRB) {  // 电位器通道
        uint8_t pot_index = (channel == ADC_VRA) ? 0 : 1;
        switch (calib_context.step) {
            case CALIB_STEP_CENTER_POTS:
                pot_center_values[pot_index] = value;
                break;
            case CALIB_STEP_MOVE_POTS:
                if (value < pot_min_values[pot_index]) {
                    pot_min_values[pot_index] = value;
                }
                if (value > pot_max_values[pot_index]) {
                    pot_max_values[pot_index] = value;
                }
                break;
            default:
                break;
        }
    }
}

/**
 * @brief 应用校准数据
 */
void Calibration_ApplyCalibration(adc_channel_t channel)
{
    if (channel >= ADC_CH_COUNT) {
        return;
    }

    /* 应用校准数据到ADC模块 */
    if (channel < 4) {  // 摇杆通道
        adc_calibration[channel].min_value = stick_min_values[channel];
        adc_calibration[channel].center_value = stick_center_values[channel];
        adc_calibration[channel].max_value = stick_max_values[channel];
    } else if (channel == ADC_VRA || channel == ADC_VRB) {  // 电位器通道
        uint8_t pot_index = (channel == ADC_VRA) ? 0 : 1;
        adc_calibration[channel].min_value = pot_min_values[pot_index];
        adc_calibration[channel].center_value = pot_center_values[pot_index];
        adc_calibration[channel].max_value = pot_max_values[pot_index];
    }
}

/**
 * @brief 加载校准数据
 */
void Calibration_LoadData(void)
{
    calibration_data_t calib_data;
    if (EEPROM_LoadCalibrationData(&calib_data) == ERR_OK) {
        /* 加载到全局校准数组 */
        for (uint8_t i = 0; i < ADC_CH_COUNT; i++) {
            adc_calibration[i] = calib_data.adc_calibration[i];
        }
    }
}

/**
 * @brief 重置校准数据
 */
void Calibration_ResetData(void)
{
    /* 重置为默认值 */
    for (uint8_t i = 0; i < ADC_CH_COUNT; i++) {
        adc_calibration[i].min_value = 0;
        adc_calibration[i].center_value = ADC_RESOLUTION / 2;
        adc_calibration[i].max_value = ADC_RESOLUTION - 1;
        adc_calibration[i].reversed = false;
        adc_calibration[i].deadband = ADC_DEFAULT_DEADBAND;
    }
}

/**
 * @brief 检查是否为摇杆通道
 */
bool Calibration_IsStickChannel(adc_channel_t channel)
{
    return (channel >= ADC_CH1 && channel <= ADC_CH4);
}

/**
 * @brief 检查是否为开关通道
 */
bool Calibration_IsSwitchChannel(adc_channel_t channel)
{
    return (channel == ADC_SWA || channel == ADC_SWB);
}

/**
 * @brief 检查是否为电位器通道
 */
bool Calibration_IsPotentiometerChannel(adc_channel_t channel)
{
    return (channel == ADC_VRA || channel == ADC_VRB);
}

/**
 * @brief 状态查询函数
 */
bool Calibration_IsBootComplete(void) { return calib_context.state == CALIB_STATE_COMPLETE; }
calibration_state_t Calibration_GetState(void) { return calib_context.state; }
calibration_step_t Calibration_GetStep(void) { return calib_context.step; }
bool Calibration_IsActive(void) { return calib_context.state != CALIB_STATE_IDLE; }
