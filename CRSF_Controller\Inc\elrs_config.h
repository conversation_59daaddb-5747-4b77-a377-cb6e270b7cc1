/**
 * @file elrs_config.h
 * @brief ELRS配置管理接口
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __ELRS_CONFIG_H
#define __ELRS_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"
#include "crsf.h"

/* ELRS参数类型 */
typedef enum {
    ELRS_PARAM_PACKET_RATE = 0,
    ELRS_PARAM_TX_POWER,
    ELRS_PARAM_TELEM_RATIO,
    ELRS_PARAM_SWITCH_MODE,
    ELRS_PARAM_MODEL_MATCH,
    ELRS_PARAM_DYNAMIC_POWER,
    ELRS_PARAM_BOOST_CHANNEL,
    ELRS_PARAM_VTX_ADMIN,
    ELRS_PARAM_COUNT
} elrs_param_type_t;

/* ELRS参数结构 */
typedef struct {
    uint8_t param_id;
    char name[32];
    uint8_t type;
    uint8_t value;
    uint8_t min_value;
    uint8_t max_value;
    uint8_t default_value;
    char** options;
    uint8_t option_count;
    bool hidden;
    bool read_only;
} elrs_param_t;

/* ELRS设备状态 */
typedef struct {
    bool tx_connected;
    bool rx_connected;
    char tx_name[32];
    char rx_name[32];
    uint32_t tx_serial;
    uint32_t rx_serial;
    uint8_t tx_params_loaded;
    uint8_t rx_params_loaded;
    uint8_t tx_total_params;
    uint8_t rx_total_params;
    bool params_loading;
    uint32_t last_ping_time;
    uint32_t last_info_time;
} elrs_device_status_t;

/* ELRS配置上下文 */
typedef struct {
    elrs_device_status_t device_status;
    elrs_param_t tx_params[CRSF_MAX_PARAMS];
    elrs_param_t rx_params[CRSF_MAX_PARAMS];
    uint8_t current_param_loading;
    uint8_t current_chunk_loading;
    uint8_t target_device;
    bool config_changed;
    uint32_t last_param_request_time;
    uint32_t param_request_timeout;
} elrs_config_context_t;

/* 全局变量声明 */
extern elrs_config_context_t elrs_config_context;

/* 函数声明 */

/* 初始化和配置 */
error_code_t ELRS_Config_Init(void);
error_code_t ELRS_Config_Start(void);
error_code_t ELRS_Config_Stop(void);

/* 设备管理 */
void ELRS_Config_CheckDevices(void);
void ELRS_Config_PingDevices(void);
void ELRS_Config_RequestDeviceInfo(uint8_t target);
void ELRS_Config_RequestElrsInfo(uint8_t target);

/* 参数管理 */
error_code_t ELRS_Config_LoadParams(uint8_t target);
error_code_t ELRS_Config_ReadParam(uint8_t target, uint8_t param_id, uint8_t chunk);
error_code_t ELRS_Config_WriteParam(uint8_t target, uint8_t param_id, uint8_t value);
error_code_t ELRS_Config_SaveParams(uint8_t target);

/* 参数查询 */
elrs_param_t* ELRS_Config_GetParam(uint8_t target, uint8_t param_id);
elrs_param_t* ELRS_Config_FindParam(uint8_t target, const char* name);
uint8_t ELRS_Config_GetParamCount(uint8_t target);
bool ELRS_Config_IsParamLoaded(uint8_t target, uint8_t param_id);

/* 参数操作 */
error_code_t ELRS_Config_SetParamValue(uint8_t target, uint8_t param_id, uint8_t value);
uint8_t ELRS_Config_GetParamValue(uint8_t target, uint8_t param_id);
const char* ELRS_Config_GetParamName(uint8_t target, uint8_t param_id);
const char* ELRS_Config_GetParamOption(uint8_t target, uint8_t param_id, uint8_t option_index);

/* 设备状态查询 */
bool ELRS_Config_IsTxConnected(void);
bool ELRS_Config_IsRxConnected(void);
const char* ELRS_Config_GetTxName(void);
const char* ELRS_Config_GetRxName(void);
uint8_t ELRS_Config_GetLoadProgress(uint8_t target);
bool ELRS_Config_IsLoading(void);

/* 配置预设 */
error_code_t ELRS_Config_ApplyPreset(uint8_t target, const char* preset_name);
error_code_t ELRS_Config_SavePreset(uint8_t target, const char* preset_name);
error_code_t ELRS_Config_LoadPreset(uint8_t target, const char* preset_name);

/* 快速配置 */
error_code_t ELRS_Config_SetPacketRate(uint8_t rate_index);
error_code_t ELRS_Config_SetTxPower(uint8_t power_index);
error_code_t ELRS_Config_SetTelemRatio(uint8_t ratio_index);
error_code_t ELRS_Config_SetSwitchMode(uint8_t mode_index);

/* 事件处理 */
void ELRS_Config_OnDeviceInfo(crsf_device_t* device);
void ELRS_Config_OnParamEntry(uint8_t* buffer, uint8_t length);
void ELRS_Config_OnElrsInfo(uint8_t* buffer);

/* 任务函数 */
void ELRS_Config_Task(void* parameters);

/* 工具函数 */
void ELRS_Config_ProcessTimeouts(void);
void ELRS_Config_UpdateDeviceStatus(void);
bool ELRS_Config_IsDeviceResponding(uint8_t target);

/* 调试功能 */
#if DEBUG_ENABLED
void ELRS_Config_PrintDeviceStatus(void);
void ELRS_Config_PrintParams(uint8_t target);
void ELRS_Config_PrintParam(uint8_t target, uint8_t param_id);
#endif

/* 常量定义 */
#define ELRS_CONFIG_PING_INTERVAL       2000   // 2秒ping间隔
#define ELRS_CONFIG_INFO_INTERVAL       5000   // 5秒信息请求间隔
#define ELRS_CONFIG_PARAM_TIMEOUT       1000   // 1秒参数请求超时
#define ELRS_CONFIG_DEVICE_TIMEOUT      10000  // 10秒设备超时

/* 包率选项 */
extern const char* elrs_packet_rate_options[];
extern const uint8_t elrs_packet_rate_values[];
extern const uint8_t elrs_packet_rate_count;

/* 发射功率选项 */
extern const char* elrs_tx_power_options[];
extern const uint8_t elrs_tx_power_values[];
extern const uint8_t elrs_tx_power_count;

/* 遥测比率选项 */
extern const char* elrs_telem_ratio_options[];
extern const uint8_t elrs_telem_ratio_values[];
extern const uint8_t elrs_telem_ratio_count;

/* 开关模式选项 */
extern const char* elrs_switch_mode_options[];
extern const uint8_t elrs_switch_mode_values[];
extern const uint8_t elrs_switch_mode_count;

#ifdef __cplusplus
}
#endif

#endif /* __ELRS_CONFIG_H */
