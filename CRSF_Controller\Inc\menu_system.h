/**
 * @file menu_system.h
 * @brief 菜单系统接口定义
 * <AUTHOR> Controller Team
 * @date 2024
 */

#ifndef __MENU_SYSTEM_H
#define __MENU_SYSTEM_H

#ifdef __cplusplus
extern "C" {
#endif

#include "config.h"
#include "button_input.h"
#include "oled_display.h"

/* 菜单项类型 */
typedef enum {
    MENU_TYPE_FOLDER = 0,       // 文件夹
    MENU_TYPE_ACTION,           // 动作项
    MENU_TYPE_VALUE,            // 数值项
    MENU_TYPE_OPTION,           // 选项项
    MENU_TYPE_INFO,             // 信息项
    MENU_TYPE_BACK              // 返回项
} menu_type_t;

/* 菜单状态 */
typedef enum {
    MENU_STATE_IDLE = 0,        // 空闲状态
    MENU_STATE_MAIN,            // 主界面
    MENU_STATE_BROWSE,          // 浏览菜单
    MENU_STATE_EDIT,            // 编辑状态
    MENU_STATE_CONFIRM          // 确认状态
} menu_state_t;

/* 菜单项结构 */
typedef struct menu_item {
    uint8_t id;                 // 菜单项ID
    char name[32];              // 菜单项名称
    menu_type_t type;           // 菜单项类型
    struct menu_item* parent;   // 父菜单
    struct menu_item* children; // 子菜单
    struct menu_item* next;     // 下一个菜单项
    
    /* 数值相关 */
    int32_t value;              // 当前值
    int32_t min_value;          // 最小值
    int32_t max_value;          // 最大值
    int32_t step;               // 步长
    char unit[8];               // 单位
    
    /* 选项相关 */
    char** options;             // 选项列表
    uint8_t option_count;       // 选项数量
    
    /* 回调函数 */
    void (*action_callback)(struct menu_item* item);
    void (*value_changed_callback)(struct menu_item* item, int32_t old_value, int32_t new_value);
    
    /* 显示相关 */
    bool visible;               // 是否可见
    bool enabled;               // 是否使能
    bool modified;              // 是否已修改
} menu_item_t;

/* 菜单上下文 */
typedef struct {
    menu_state_t state;         // 当前状态
    menu_item_t* root_menu;     // 根菜单
    menu_item_t* current_menu;  // 当前菜单
    menu_item_t* selected_item; // 选中的菜单项
    uint8_t selected_index;     // 选中索引
    uint8_t first_visible_index; // 第一个可见项索引
    uint8_t visible_items;      // 可见项数量
    uint8_t max_visible_items;  // 最大可见项数量
    
    /* 编辑状态 */
    bool editing;               // 是否在编辑
    int32_t edit_value;         // 编辑中的值
    uint8_t edit_digit;         // 编辑的数位
    
    /* 显示相关 */
    bool need_refresh;          // 需要刷新显示
    uint32_t last_activity;     // 最后活动时间
    uint32_t auto_return_time;  // 自动返回时间
} menu_context_t;

/* 主界面信息结构 */
typedef struct {
    char tx_name[32];           // TX模块名称
    char rx_name[32];           // RX模块名称
    uint8_t link_quality;       // 链路质量
    int8_t rssi;                // 信号强度
    uint8_t rf_mode;            // RF模式
    uint16_t good_packets;      // 好包数
    uint8_t bad_packets;        // 坏包数
    uint8_t battery_level;      // 电池电量
    bool tx_connected;          // TX连接状态
    bool rx_connected;          // RX连接状态
} main_screen_info_t;

/* 全局变量声明 */
extern menu_context_t menu_context;
extern main_screen_info_t main_screen_info;

/* 函数声明 */

/* 菜单系统初始化 */
error_code_t Menu_Init(void);
error_code_t Menu_BuildMenuTree(void);
void Menu_SetAutoReturnTime(uint32_t time_ms);

/* 菜单导航 */
void Menu_EnterMenu(menu_item_t* menu);
void Menu_ExitMenu(void);
void Menu_GoToParent(void);
void Menu_GoToRoot(void);
void Menu_SelectNext(void);
void Menu_SelectPrevious(void);
void Menu_SelectItem(uint8_t index);

/* 菜单操作 */
void Menu_ExecuteAction(void);
void Menu_StartEdit(void);
void Menu_StopEdit(void);
void Menu_ConfirmEdit(void);
void Menu_CancelEdit(void);
void Menu_IncreaseValue(void);
void Menu_DecreaseValue(void);
void Menu_SetValue(int32_t value);

/* 按键处理 */
void Menu_HandleButton(button_id_t button, button_event_t event);
void Menu_ProcessInput(void);

/* 显示更新 */
void Menu_UpdateDisplay(void);
void Menu_RefreshDisplay(void);
void Menu_ShowMainScreen(void);
void Menu_ShowMenuScreen(void);
void Menu_ShowEditScreen(void);
void Menu_ShowConfirmScreen(const char* message);

/* 菜单项管理 */
menu_item_t* Menu_CreateItem(const char* name, menu_type_t type);
void Menu_AddChild(menu_item_t* parent, menu_item_t* child);
void Menu_RemoveChild(menu_item_t* parent, menu_item_t* child);
menu_item_t* Menu_FindItem(menu_item_t* root, const char* name);
menu_item_t* Menu_FindItemById(menu_item_t* root, uint8_t id);
uint8_t Menu_GetChildCount(menu_item_t* parent);
menu_item_t* Menu_GetChild(menu_item_t* parent, uint8_t index);

/* 菜单项配置 */
void Menu_SetValueRange(menu_item_t* item, int32_t min, int32_t max, int32_t step);
void Menu_SetUnit(menu_item_t* item, const char* unit);
void Menu_SetOptions(menu_item_t* item, char** options, uint8_t count);
void Menu_SetActionCallback(menu_item_t* item, void (*callback)(menu_item_t*));
void Menu_SetValueChangedCallback(menu_item_t* item, void (*callback)(menu_item_t*, int32_t, int32_t));

/* 状态查询 */
menu_state_t Menu_GetState(void);
menu_item_t* Menu_GetCurrentMenu(void);
menu_item_t* Menu_GetSelectedItem(void);
bool Menu_IsEditing(void);
bool Menu_NeedRefresh(void);

/* 主界面信息更新 */
void Menu_UpdateMainScreenInfo(const main_screen_info_t* info);
void Menu_SetTxConnected(bool connected, const char* name);
void Menu_SetRxConnected(bool connected, const char* name);
void Menu_SetLinkQuality(uint8_t quality, int8_t rssi);
void Menu_SetRfMode(uint8_t mode);
void Menu_SetPacketStats(uint16_t good, uint8_t bad);
void Menu_SetBatteryLevel(uint8_t level);

/* 任务函数 */
void Menu_Task(void* parameters);

/* 工具函数 */
void Menu_FormatValue(menu_item_t* item, char* buffer, size_t buffer_size);
bool Menu_IsItemVisible(menu_item_t* item);
void Menu_ScrollToItem(uint8_t index);
void Menu_UpdateScrollPosition(void);

/* 预定义菜单项ID */
#define MENU_ID_ROOT            0
#define MENU_ID_MAIN            1
#define MENU_ID_SETTINGS        2
#define MENU_ID_CALIBRATION     3
#define MENU_ID_ELRS_CONFIG     4
#define MENU_ID_SYSTEM_INFO     5
#define MENU_ID_ABOUT           6

/* 显示参数 */
#define MENU_MAX_VISIBLE_ITEMS  6
#define MENU_ITEM_HEIGHT        10
#define MENU_SCROLL_BAR_WIDTH   4
#define MENU_AUTO_RETURN_TIME   30000   // 30秒自动返回

#ifdef __cplusplus
}
#endif

#endif /* __MENU_SYSTEM_H */
